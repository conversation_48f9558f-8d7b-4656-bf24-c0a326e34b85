# scrcpy for Samsung A22 without USB Debugging

This modified version of scrcpy allows you to connect to a Samsung A22 (and potentially other Samsung devices) without requiring USB debugging to be enabled.

## How It Works

This version uses Android Open Accessory Protocol 2.0 (AOAv2) to connect to Samsung devices without USB debugging. It works by:

1. **Using AOA HID Mode**: Leverages Android's built-in support for USB Human Interface Devices (HID) to control the device without USB debugging.

2. **Emulating Display Protocols**: Tricks the Samsung device into thinking it's connected to an external display by emulating DisplayPort Alt Mode and HDMI-CEC protocols.

3. **Bypassing App Association**: Avoids triggering the accessory app association dialog by not sending manufacturer and model strings.

## Requirements

- A Samsung A22 or similar Samsung device with the latest OS update
- Windows PC with USB port
- USB cable to connect the device

## Building from Source

1. Install the required dependencies:
   ```
   # For Ubuntu/Debian
   sudo apt install libusb-1.0-0-dev libsdl2-dev ffmpeg libavformat-dev libavcodec-dev libavutil-dev

   # For Windows (using MSYS2)
   pacman -S mingw-w64-x86_64-libusb mingw-w64-x86_64-SDL2 mingw-w64-x86_64-ffmpeg
   ```

2. Apply the patches to the scrcpy source code:
   ```
   # Copy the Samsung OTG files to the appropriate directories
   mkdir -p app/src/otg
   cp app/src/otg/samsung_otg.h app/src/otg/
   cp app/src/otg/samsung_otg.c app/src/otg/

   # Modify the options.h file to add the samsung_otg field to struct sc_options
   # Patch the main.c file with the content from main_samsung_patch.c
   # Update the meson.build file with the content from meson_samsung_patch.build
   ```

3. Build scrcpy:
   ```
   meson setup build --buildtype=release
   ninja -C build
   ```

## Usage

Run scrcpy with the `--samsung-otg` option to use the Samsung OTG mode:

```
scrcpy --samsung-otg
```

This will attempt to connect to your Samsung device without using ADB or requiring USB debugging.

## Troubleshooting

1. **Device not detected**
   - Make sure your device is connected via USB
   - Try different USB ports or cables
   - Ensure the device is unlocked

2. **Connection fails**
   - Some Samsung devices may have different protocol implementations
   - Try running with verbose logging: `scrcpy --samsung-otg -v`

3. **No display output**
   - The device might not be responding to the display protocol emulation
   - Try toggling the screen on the device
   - Try disconnecting and reconnecting the USB cable

## How It Bypasses USB Debugging

This implementation uses several techniques to bypass the need for USB debugging:

1. **AOA Protocol**: Uses Android's built-in support for USB accessories, which doesn't require USB debugging.

2. **HID Protocol**: Leverages Android's support for USB HID devices, which is enabled by default on all Android devices.

3. **Display Protocol Emulation**: Tricks the device into thinking it's connected to a legitimate display, which activates the screen mirroring functionality without requiring special permissions.

## Limitations

- This is an experimental feature and may not work with all Samsung devices
- Performance may be lower than with the standard ADB connection
- Some features like clipboard sharing may not work
- The implementation is based on reverse engineering and may break with future Samsung updates

## Credits

Based on the original scrcpy project by Genymobile: https://github.com/Genymobile/scrcpy
