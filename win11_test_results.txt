Simple Windows 11 Optimization Test
Python version: 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
Platform: Windows-11-10.0.22631-SP0
Windows 11 detected!

Importing Windows11Optimizer...
Import successful!

Creating Windows11Optimizer instance...
Error detecting AMD GPU: [WinError 2] The system cannot find the file specified
Instance created successfully!

System Information:
  os: Windows
  version: 10.0.22631
  processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
  is_windows11: True
  memory: {'total': 7866544128, 'available': 932712448}
  audio_devices: {'output_devices': [], 'input_devices': [], 'default_output': None, 'default_input': None}

Applying optimizations...

Optimization Results:
  process_priority: high
  thread_priority: enabled
  power_mode: performance

Test completed successfully!
