const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { PythonShell } = require('python-shell');
const sharp = require('sharp');
const axios = require('axios');
const FormData = require('form-data');
const { v4: uuidv4 } = require('uuid');
const sqlite3 = require('sqlite3').verbose();
const { createServer } = require('http');
const WebSocket = require('ws');
const { execFile } = require('child_process');

// Configuration
const config = require('./config');

// Initialize directories
const TEMP_DIR = path.join(__dirname, 'temp');
const SCREENSHOTS_DIR = path.join(__dirname, 'screenshots');
const CACHE_DIR = path.join(__dirname, 'cache');
const MODELS_DIR = path.join(__dirname, 'models');
const DB_PATH = path.join(__dirname, 'database.sqlite');

// Ensure directories exist
[TEMP_DIR, SCREENSHOTS_DIR, CACHE_DIR, MODELS_DIR].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Initialize database
const db = new sqlite3.Database(DB_PATH);
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS solutions (
      id TEXT PRIMARY KEY,
      problem_hash TEXT,
      problem_text TEXT,
      solution TEXT,
      model TEXT,
      timestamp INTEGER
    )
  `);
  
  db.run(`
    CREATE TABLE IF NOT EXISTS embeddings (
      id TEXT PRIMARY KEY,
      text TEXT,
      embedding BLOB,
      type TEXT,
      timestamp INTEGER
    )
  `);
});

// Native messaging setup
const nativeMessaging = {
  // Read message from stdin
  readMessage: () => {
    return new Promise((resolve) => {
      // First 4 bytes are the length of the message
      const buffer = Buffer.alloc(4);
      
      fs.read(0, buffer, 0, 4, null, (err, bytesRead) => {
        if (bytesRead < 4) {
          resolve(null);
          return;
        }
        
        const messageLength = buffer.readUInt32LE(0);
        const messageBuffer = Buffer.alloc(messageLength);
        
        fs.read(0, messageBuffer, 0, messageLength, null, (err, bytesRead) => {
          if (bytesRead < messageLength) {
            resolve(null);
            return;
          }
          
          try {
            const message = JSON.parse(messageBuffer.toString());
            resolve(message);
          } catch (error) {
            console.error('Error parsing message:', error);
            resolve(null);
          }
        });
      });
    });
  },
  
  // Write message to stdout
  writeMessage: (message) => {
    const messageJson = JSON.stringify(message);
    const messageBuffer = Buffer.from(messageJson);
    const headerBuffer = Buffer.alloc(4);
    
    headerBuffer.writeUInt32LE(messageBuffer.length, 0);
    
    fs.writeSync(1, headerBuffer);
    fs.writeSync(1, messageBuffer);
  }
};

// WebSocket server for overlay communication
const server = createServer();
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
  console.log('Overlay connected');
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      
      if (data.type === 'toggle-overlay') {
        toggleOverlay();
      } else if (data.type === 'move-overlay') {
        moveOverlay(data.direction);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  });
  
  ws.on('close', () => {
    console.log('Overlay disconnected');
  });
});

server.listen(8787, () => {
  console.log('WebSocket server listening on port 8787');
});

// Overlay process
let overlayProcess = null;

// Start overlay process
function startOverlay() {
  if (overlayProcess) {
    console.log('Overlay already running');
    return;
  }
  
  try {
    overlayProcess = execFile('electron', [path.join(__dirname, 'overlay/main.js')], {
      windowsHide: true
    });
    
    overlayProcess.on('error', (error) => {
      console.error('Error starting overlay process:', error);
      overlayProcess = null;
    });
    
    overlayProcess.on('exit', (code) => {
      console.log(`Overlay process exited with code ${code}`);
      overlayProcess = null;
    });
    
    console.log('Overlay process started');
  } catch (error) {
    console.error('Error starting overlay process:', error);
  }
}

// Toggle overlay visibility
function toggleOverlay() {
  if (!overlayProcess) {
    startOverlay();
    return;
  }
  
  const ws = new WebSocket('ws://localhost:8787');
  
  ws.on('open', () => {
    ws.send(JSON.stringify({ type: 'toggle-visibility' }));
    ws.close();
  });
  
  ws.on('error', (error) => {
    console.error('Error connecting to overlay WebSocket:', error);
  });
}

// Move overlay
function moveOverlay(direction) {
  if (!overlayProcess) {
    startOverlay();
    return;
  }
  
  const ws = new WebSocket('ws://localhost:8787');
  
  ws.on('open', () => {
    ws.send(JSON.stringify({ type: 'move', direction }));
    ws.close();
  });
  
  ws.on('error', (error) => {
    console.error('Error connecting to overlay WebSocket:', error);
  });
}

// Send solution to overlay
function sendSolutionToOverlay(solution, type = 'solution') {
  if (!overlayProcess) {
    startOverlay();
  }
  
  const ws = new WebSocket('ws://localhost:8787');
  
  ws.on('open', () => {
    ws.send(JSON.stringify({ 
      type: 'update-solution',
      solution,
      solutionType: type
    }));
    ws.close();
  });
  
  ws.on('error', (error) => {
    console.error('Error connecting to overlay WebSocket:', error);
  });
}

// Services
const services = {
  // Local CodeGen model
  codegen: {
    generateSolution: async (problemText) => {
      return new Promise((resolve, reject) => {
        const options = {
          mode: 'json',
          pythonPath: config.pythonPath,
          pythonOptions: ['-u'],
          scriptPath: path.join(__dirname, 'python'),
          args: [
            '--model', path.join(MODELS_DIR, config.codegen.modelFile),
            '--prompt', problemText,
            '--max_tokens', config.codegen.maxTokens.toString(),
            '--temperature', config.codegen.temperature.toString()
          ]
        };
        
        PythonShell.run('codegen_inference.py', options, (err, results) => {
          if (err) {
            console.error('CodeGen inference error:', err);
            reject(err);
            return;
          }
          
          if (results && results.length > 0) {
            try {
              const solution = JSON.parse(results[0]);
              solution.model = 'CodeGen';
              resolve(solution);
            } catch (error) {
              console.error('Error parsing CodeGen result:', error);
              reject(error);
            }
          } else {
            reject(new Error('No results from CodeGen inference'));
          }
        });
      });
    }
  },
  
  // Mistral OCR API
  mistralOcr: {
    processImage: async (imagePath) => {
      try {
        // Check if file exists
        if (!fs.existsSync(imagePath)) {
          throw new Error(`Image file not found: ${imagePath}`);
        }
        
        // Create form data
        const formData = new FormData();
        formData.append('file', fs.createReadStream(imagePath));
        formData.append('model', 'mistral-ocr-latest');
        
        // Make API request
        const response = await axios.post('https://api.mistral.ai/v1/ocr', formData, {
          headers: {
            'Authorization': `Bearer ${config.mistral.apiKey}`,
            ...formData.getHeaders()
          }
        });
        
        // Process response
        if (response.data && response.data.pages) {
          // Extract text from all pages
          const extractedText = response.data.pages
            .map(page => page.markdown || page.text)
            .join('\n\n');
          
          return extractedText;
        } else {
          throw new Error('Invalid response format from Mistral OCR API');
        }
      } catch (error) {
        console.error('Mistral OCR Error:', error.message);
        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }
        throw error;
      }
    },
    
    processBase64Image: async (base64Image) => {
      try {
        // Remove data URL prefix if present
        const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');
        const buffer = Buffer.from(base64Data, 'base64');
        
        // Save to temporary file
        const tempFilePath = path.join(TEMP_DIR, `temp_${Date.now()}.png`);
        fs.writeFileSync(tempFilePath, buffer);
        
        // Process the image
        const result = await services.mistralOcr.processImage(tempFilePath);
        
        // Clean up
        fs.unlinkSync(tempFilePath);
        
        return result;
      } catch (error) {
        console.error('Base64 OCR Error:', error);
        throw error;
      }
    }
  },
  
  // Groq API
  groq: {
    currentAccountIndex: 0,
    currentModelIndex: 0,
    requestCount: 0,
    maxRequestsPerAccount: 100,
    
    getApiKey: function() {
      return config.groq.accounts[this.currentAccountIndex].apiKey;
    },
    
    getModel: function() {
      return config.groq.models[this.currentModelIndex];
    },
    
    switchAccount: function() {
      this.currentAccountIndex = (this.currentAccountIndex + 1) % config.groq.accounts.length;
      this.requestCount = 0;
      console.log(`Switched to Groq account: ${config.groq.accounts[this.currentAccountIndex].name}`);
    },
    
    switchModel: function() {
      this.currentModelIndex = (this.currentModelIndex + 1) % config.groq.models.length;
      console.log(`Switched to Groq model: ${config.groq.models[this.currentModelIndex].name}`);
      return config.groq.models[this.currentModelIndex];
    },
    
    switchToModel: function(modelName) {
      const modelIndex = config.groq.models.findIndex(model => model.name === modelName);
      if (modelIndex !== -1) {
        this.currentModelIndex = modelIndex;
        console.log(`Switched to Groq model: ${modelName}`);
        return config.groq.models[this.currentModelIndex];
      } else {
        console.error(`Model ${modelName} not found, using current model`);
        return this.getModel();
      }
    },
    
    checkAndSwitchAccount: function() {
      this.requestCount++;
      if (this.requestCount >= this.maxRequestsPerAccount) {
        this.switchAccount();
      }
    },
    
    generateSolution: async function(problemText) {
      try {
        this.checkAndSwitchAccount();
        
        const model = this.getModel();
        
        const prompt = `
You are an expert coding interview assistant. Analyze the following problem and provide a detailed solution.

PROBLEM:
${problemText}

Provide your response in the following JSON format:
{
  "analysis": "Brief analysis of the problem, identifying key concepts and patterns",
  "approach": "Step-by-step approach to solve the problem",
  "code": "Well-commented code solution",
  "time_complexity": "Analysis of time complexity",
  "space_complexity": "Analysis of space complexity"
}

Make sure your response is valid JSON.
`;

        const response = await axios.post(
          'https://api.groq.com/openai/v1/chat/completions',
          {
            model: model.id,
            messages: [
              { role: "system", content: "You are an expert coding assistant that helps with technical interviews." },
              { role: "user", content: prompt }
            ],
            temperature: 0.3,
            max_tokens: 2048,
            response_format: { type: "json_object" }
          },
          { 
            headers: {
              'Authorization': `Bearer ${this.getApiKey()}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.data && response.data.choices && response.data.choices.length > 0) {
          const content = response.data.choices[0].message.content;
          try {
            const solution = JSON.parse(content);
            solution.model = model.name;
            return solution;
          } catch (e) {
            console.error('Error parsing JSON response:', e);
            // Try to extract JSON from the response using regex
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const solution = JSON.parse(jsonMatch[0]);
              solution.model = model.name;
              return solution;
            }
            throw new Error('Failed to parse solution JSON');
          }
        } else {
          throw new Error('Invalid response format from Groq API');
        }
      } catch (error) {
        console.error('Groq API Error:', error.message);
        if (error.response) {
          console.error('Response status:', error.response.status);
          
          // Handle rate limiting or quota exceeded
          if (error.response.status === 429) {
            console.log('Rate limit exceeded, switching account and retrying...');
            this.switchAccount();
            return this.generateSolution(problemText);
          }
        }
        throw error;
      }
    }
  },
  
  // Lightning AI BitNet
  lightningBitNet: {
    generateSolution: async (problemText) => {
      try {
        const prompt = `
Analyze this coding problem and provide a solution:

${problemText}

Format your response as follows:
1. Problem Analysis
2. Approach
3. Code Solution
4. Time Complexity
5. Space Complexity
`;

        const response = await axios.post(
          config.lightning.bitnetModelEndpoint,
          {
            prompt: prompt,
            max_tokens: 1024,
            temperature: 0.3,
            top_p: 0.9
          },
          { 
            headers: {
              'Authorization': `Bearer ${config.lightning.apiKey}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.data && response.data.generated_text) {
          // Parse the response into structured format
          return services.lightningBitNet.parseResponse(response.data.generated_text);
        } else {
          throw new Error('Invalid response format from Lightning AI BitNet API');
        }
      } catch (error) {
        console.error('Lightning BitNet API Error:', error.message);
        if (error.response) {
          console.error('Response status:', error.response.status);
        }
        throw error;
      }
    },
    
    parseResponse: (text) => {
      // Initialize solution object
      const solution = {
        analysis: '',
        approach: '',
        code: '',
        time_complexity: '',
        space_complexity: '',
        model: 'BitNet 1.58'
      };

      // Extract sections using regex patterns
      const analysisMatch = text.match(/Problem Analysis:?([\s\S]*?)(?=Approach:|$)/i);
      if (analysisMatch && analysisMatch[1]) {
        solution.analysis = analysisMatch[1].trim();
      }

      const approachMatch = text.match(/Approach:?([\s\S]*?)(?=Code Solution:|$)/i);
      if (approachMatch && approachMatch[1]) {
        solution.approach = approachMatch[1].trim();
      }

      const codeMatch = text.match(/Code Solution:?([\s\S]*?)(?=Time Complexity:|$)/i);
      if (codeMatch && codeMatch[1]) {
        solution.code = codeMatch[1].trim();
        
        // Ensure code is wrapped in code blocks if not already
        if (!solution.code.startsWith('```')) {
          const language = services.lightningBitNet.detectLanguage(solution.code);
          solution.code = '```' + language + '\n' + solution.code + '\n```';
        }
      }

      const timeMatch = text.match(/Time Complexity:?([\s\S]*?)(?=Space Complexity:|$)/i);
      if (timeMatch && timeMatch[1]) {
        solution.time_complexity = timeMatch[1].trim();
      }

      const spaceMatch = text.match(/Space Complexity:?([\s\S]*?)(?=$)/i);
      if (spaceMatch && spaceMatch[1]) {
        solution.space_complexity = spaceMatch[1].trim();
      }

      return solution;
    },
    
    detectLanguage: (code) => {
      // Simple language detection based on keywords and syntax
      if (code.includes('def ') || code.includes('import ') || 'print(' in code) {
        return 'python';
      } else if (code.includes('function ') || code.includes('const ') || code.includes('let ') || code.includes('var ')) {
        return 'javascript';
      } else if (code.includes('public class ') || code.includes('private ') || code.includes('System.out.println')) {
        return 'java';
      } else if (code.includes('#include') || code.includes('int main')) {
        return 'cpp';
      } else {
        return '';
      }
    }
  }
};

// RAG (Retrieval Augmented Generation) system
const rag = {
  // Generate embedding for text
  generateEmbedding: async (text) => {
    return new Promise((resolve, reject) => {
      const options = {
        mode: 'json',
        pythonPath: config.pythonPath,
        pythonOptions: ['-u'],
        scriptPath: path.join(__dirname, 'python'),
        args: [text]
      };
      
      PythonShell.run('generate_embedding.py', options, (err, results) => {
        if (err) {
          console.error('Embedding generation error:', err);
          reject(err);
          return;
        }
        
        if (results && results.length > 0) {
          try {
            const embedding = JSON.parse(results[0]);
            resolve(embedding);
          } catch (error) {
            console.error('Error parsing embedding result:', error);
            reject(error);
          }
        } else {
          reject(new Error('No results from embedding generation'));
        }
      });
    });
  },
  
  // Store embedding in database
  storeEmbedding: (text, embedding, type) => {
    return new Promise((resolve, reject) => {
      const id = uuidv4();
      const timestamp = Date.now();
      
      db.run(
        'INSERT INTO embeddings (id, text, embedding, type, timestamp) VALUES (?, ?, ?, ?, ?)',
        [id, text, Buffer.from(new Float32Array(embedding).buffer), type, timestamp],
        function(err) {
          if (err) {
            reject(err);
            return;
          }
          
          resolve(id);
        }
      );
    });
  },
  
  // Find similar texts
  findSimilar: (embedding, type, limit = 5) => {
    return new Promise((resolve, reject) => {
      db.all('SELECT id, text, embedding, type FROM embeddings WHERE type = ?', [type], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }
        
        // Calculate cosine similarity
        const similarities = rows.map(row => {
          const rowEmbedding = new Float32Array(row.embedding.buffer);
          const similarity = rag.cosineSimilarity(embedding, Array.from(rowEmbedding));
          
          return {
            id: row.id,
            text: row.text,
            similarity
          };
        });
        
        // Sort by similarity (descending)
        similarities.sort((a, b) => b.similarity - a.similarity);
        
        // Return top results
        resolve(similarities.slice(0, limit));
      });
    });
  },
  
  // Calculate cosine similarity between two vectors
  cosineSimilarity: (a, b) => {
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);
    
    return dotProduct / (normA * normB);
  },
  
  // Enhance solution with RAG
  enhanceSolution: async (problemText, solution) => {
    try {
      // Generate embedding for problem
      const problemEmbedding = await rag.generateEmbedding(problemText);
      
      // Store embedding
      await rag.storeEmbedding(problemText, problemEmbedding, 'problem');
      
      // Find similar problems
      const similarProblems = await rag.findSimilar(problemEmbedding, 'problem');
      
      // If no similar problems found, return original solution
      if (similarProblems.length === 0) {
        return solution;
      }
      
      // Get the most similar problem
      const mostSimilar = similarProblems[0];
      
      // If similarity is low, return original solution
      if (mostSimilar.similarity < 0.8) {
        return solution;
      }
      
      // Get solution for similar problem
      const similarSolution = await new Promise((resolve, reject) => {
        db.get('SELECT solution FROM solutions WHERE problem_hash = ?', [mostSimilar.id], (err, row) => {
          if (err) {
            reject(err);
            return;
          }
          
          if (row) {
            try {
              resolve(JSON.parse(row.solution));
            } catch (error) {
              reject(error);
            }
          } else {
            reject(new Error('No solution found for similar problem'));
          }
        });
      });
      
      // Enhance solution with similar problem's solution
      return {
        ...solution,
        approach: solution.approach + '\n\n**Similar Problem Approach:**\n' + similarSolution.approach,
        code: solution.code,
        time_complexity: solution.time_complexity,
        space_complexity: solution.space_complexity
      };
    } catch (error) {
      console.error('Error enhancing solution with RAG:', error);
      return solution;
    }
  }
};

// Solution generation
async function generateSolution(problemText, options = {}) {
  const {
    useCodeGen = true,
    useGroq = true,
    useBitNet = true,
    preferredModel = null,
    cacheResults = true
  } = options;
  
  // Generate hash for problem text
  const problemHash = Buffer.from(problemText).toString('base64').substring(0, 44);
  
  // Check cache
  if (cacheResults) {
    const cachedSolution = await new Promise((resolve, reject) => {
      db.get('SELECT solution FROM solutions WHERE problem_hash = ?', [problemHash], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (row) {
          try {
            resolve(JSON.parse(row.solution));
          } catch (error) {
            resolve(null);
          }
        } else {
          resolve(null);
        }
      });
    }).catch(() => null);
    
    if (cachedSolution) {
      console.log('Using cached solution');
      return cachedSolution;
    }
  }
  
  // Generate quick solution with CodeGen
  let quickSolution = null;
  let finalSolution = null;
  
  if (useCodeGen) {
    try {
      console.log('Generating quick solution with CodeGen...');
      quickSolution = await services.codegen.generateSolution(problemText);
      
      // Send quick solution to overlay
      sendSolutionToOverlay(quickSolution, 'quick-solution');
      
      // Send quick solution to extension
      nativeMessaging.writeMessage({
        type: 'quick-solution',
        solution: quickSolution
      });
    } catch (error) {
      console.error('Error generating quick solution:', error);
    }
  }
  
  // Use preferred model if specified
  if (preferredModel) {
    if (preferredModel === 'groq' && useGroq) {
      finalSolution = await services.groq.generateSolution(problemText);
    } else if (preferredModel === 'bitnet' && useBitNet) {
      finalSolution = await services.lightningBitNet.generateSolution(problemText);
    }
  } else {
    // Try Groq first (higher quality)
    if (useGroq) {
      try {
        console.log('Generating solution with Groq...');
        finalSolution = await services.groq.generateSolution(problemText);
      } catch (error) {
        console.error('Error generating solution with Groq:', error);
        
        // Fall back to BitNet if Groq fails
        if (useBitNet) {
          try {
            console.log('Falling back to BitNet...');
            finalSolution = await services.lightningBitNet.generateSolution(problemText);
          } catch (bitNetError) {
            console.error('Error generating solution with BitNet:', bitNetError);
            
            // If both fail, use the quick solution if available
            finalSolution = quickSolution || {
              error: 'All solution generation services failed',
              analysis: 'Unable to generate solution with available services',
              approach: 'Please check your internet connection and API keys',
              code: '# Error generating solution',
              time_complexity: 'N/A',
              space_complexity: 'N/A',
              model: 'Error'
            };
          }
        }
      }
    } else if (useBitNet) {
      // Use BitNet if Groq is disabled
      try {
        console.log('Generating solution with BitNet...');
        finalSolution = await services.lightningBitNet.generateSolution(problemText);
      } catch (error) {
        console.error('Error generating solution with BitNet:', error);
        finalSolution = quickSolution;
      }
    } else {
      // Use quick solution if both Groq and BitNet are disabled
      finalSolution = quickSolution;
    }
  }
  
  // Enhance solution with RAG if available
  if (finalSolution) {
    try {
      finalSolution = await rag.enhanceSolution(problemText, finalSolution);
    } catch (error) {
      console.error('Error enhancing solution with RAG:', error);
    }
  }
  
  // Cache the final solution
  if (cacheResults && finalSolution) {
    db.run(
      'INSERT OR REPLACE INTO solutions (id, problem_hash, problem_text, solution, model, timestamp) VALUES (?, ?, ?, ?, ?, ?)',
      [uuidv4(), problemHash, problemText, JSON.stringify(finalSolution), finalSolution.model || 'unknown', Date.now()]
    );
  }
  
  // Send final solution to overlay
  sendSolutionToOverlay(finalSolution, 'solution');
  
  // Send final solution to extension
  nativeMessaging.writeMessage({
    type: 'solution',
    solution: finalSolution
  });
  
  return finalSolution;
}

// Process screenshot
async function processScreenshot(screenshotData) {
  try {
    // Remove data URL prefix if present
    const base64Data = screenshotData.replace(/^data:image\/\w+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    
    // Save screenshot to file
    const timestamp = Date.now();
    const screenshotPath = path.join(SCREENSHOTS_DIR, `screenshot_${timestamp}.png`);
    fs.writeFileSync(screenshotPath, buffer);
    
    // Process with Mistral OCR
    console.log('Processing screenshot with Mistral OCR...');
    const extractedText = await services.mistralOcr.processBase64Image(screenshotData);
    
    // Generate solution
    console.log('Generating solution...');
    await generateSolution(extractedText);
    
    return extractedText;
  } catch (error) {
    console.error('Error processing screenshot:', error);
    throw error;
  }
}

// Main message processing loop
async function processMessages() {
  while (true) {
    const message = await nativeMessaging.readMessage();
    
    if (!message) {
      console.log('No more messages, exiting...');
      break;
    }
    
    console.log('Received message:', message.type);
    
    try {
      if (message.type === 'capture-problem') {
        await processScreenshot(message.screenshot);
      } else if (message.type === 'capture-problem-text') {
        await generateSolution(message.text);
      } else if (message.type === 'toggle-overlay') {
        toggleOverlay();
      } else if (message.type === 'move-overlay') {
        moveOverlay(message.direction);
      } else if (message.type === 'switch-model') {
        if (message.model === 'codegen') {
          // No action needed, CodeGen is always used for quick solutions
        } else if (message.model.startsWith('llama3') || message.model === 'mixtral') {
          services.groq.switchToModel(message.model);
        }
      } else if (message.type === 'check-connection') {
        nativeMessaging.writeMessage({
          type: 'connection-status',
          connected: true
        });
      }
    } catch (error) {
      console.error('Error processing message:', error);
      
      nativeMessaging.writeMessage({
        type: 'error',
        error: error.message
      });
    }
  }
}

// Start processing messages
console.log('NeuralCodeAssist service started');
processMessages().catch(error => {
  console.error('Error in message processing loop:', error);
  process.exit(1);
});
