"""
Test script to process a specific audio file
"""

import os
import sys
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import core modules
from core.audio_processor import AudioProcessor
from core.file_utils import play_audio_file, create_output_path

def main():
    # File to process
    input_file = r"C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac"
    
    # Check if file exists
    if not os.path.isfile(input_file):
        print(f"Error: File not found: {input_file}")
        return 1
    
    print(f"Processing file: {input_file}")
    
    # Create output path
    output_file = create_output_path(input_file, suffix="upscaled")
    print(f"Output will be saved to: {output_file}")
    
    # Initialize audio processor with Windows 11 optimizations
    print("Initializing audio processor with Windows 11 optimizations...")
    processor = AudioProcessor(use_win11_opt=True, use_hardware_accel=True)
    
    # Process the file
    print("Processing file...")
    start_time = time.time()
    
    result = processor.process_file(
        input_file,
        output_file=output_file,
        upscale_factor=2,
        quality_level=2  # Medium quality
    )
    
    process_time = time.time() - start_time
    
    if result:
        print(f"Processing completed in {process_time:.2f} seconds")
        print(f"Output saved to: {result}")
        
        # Play the processed file
        print("Playing processed file...")
        play_audio_file(result)
        
        return 0
    else:
        print("Processing failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
