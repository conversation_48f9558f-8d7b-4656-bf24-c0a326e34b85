@echo off
echo Running pip command...

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Check if Python exists
if exist "%PYTHON_PATH%\python.exe" (
    echo Found Python at: %PYTHON_PATH%
) else (
    echo Python not found at %PYTHON_PATH%
    goto :end
)

REM Add to PATH for this session
set "PATH=%PYTHON_PATH%;%PATH%"
set "PATH=%PATH%;C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Run pip with all arguments passed to this batch file
"%PYTHON_PATH%\python.exe" -m pip %*

:end
pause
