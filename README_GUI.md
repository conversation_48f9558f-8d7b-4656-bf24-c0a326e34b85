# AudioSR GUI

A drag-and-drop graphical user interface for [AudioSR](https://github.com/haoheliu/versatile_audio_super_resolution), a versatile audio super-resolution tool.

## Features

- Simple drag-and-drop interface for audio files
- Support for various audio formats (WAV, MP3, FLAC, etc.)
- Adjustable settings for guidance scale and DDIM steps
- Choice between "basic" and "speech" models
- Stereo output support (fixed from original repository)
- Standalone executable for Windows

## Installation

### Option 1: Download the Executable (Windows)

1. Download the latest release from the [Releases](https://github.com/yourusername/audiosr-gui/releases) page
2. Extract the ZIP file
3. Run `AudioSR.exe`

### Option 2: Install from Source

1. Clone this repository:
   ```
   git clone https://github.com/haoheliu/versatile_audio_super_resolution.git
   cd versatile_audio_super_resolution
   ```

2. Run the setup script:
   ```
   python setup_audiosr.py
   ```

   This will:
   - Install required dependencies
   - Fix the mono output issue
   - Create an executable (optional)

3. Run the GUI:
   ```
   python audiosr_gui.py
   ```

## Usage

1. Launch the application
2. Drag and drop an audio file onto the drop area (or click to select a file)
3. Adjust settings as needed:
   - **Model**: Choose between "basic" (general purpose) or "speech" (optimized for speech)
   - **Guidance Scale**: Higher values produce better quality but less diversity (recommended: 3.5)
   - **DDIM Steps**: Higher values produce better quality but take longer (recommended: 50)
   - **Device**: Choose between auto-detection, CPU, or CUDA (if available)
4. Click "Process Audio"
5. Wait for processing to complete
6. The output file will be saved in an "audiosr_output" folder next to the input file

## System Requirements

- Windows 10/11 (for executable)
- Python 3.9-3.11 (for source installation)
- 8GB RAM minimum (16GB recommended)
- NVIDIA GPU with CUDA support (optional, for faster processing)

## Troubleshooting

### Common Issues

1. **"No module named 'tkinterdnd2'"**
   - Run: `pip install tkinterdnd2`

2. **"No module named 'audiosr'"**
   - Make sure you've installed the package: `pip install -e .`

3. **"CUDA out of memory"**
   - Try processing shorter audio files
   - Use CPU mode instead of CUDA
   - Close other applications using the GPU

4. **Mono output instead of stereo**
   - Make sure you've run the setup script to fix the mono output issue

5. **Python 3.12+ compatibility issues**
   - Use Python 3.9-3.11 instead, as some dependencies don't support Python 3.12 yet

## Credits

- [AudioSR](https://github.com/haoheliu/versatile_audio_super_resolution) by Haohe Liu
- GUI implementation by [Your Name]

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
