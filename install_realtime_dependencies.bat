@echo on
echo Installing dependencies for real-time audio upscaler

R<PERSON> Set Python paths
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Install required packages
echo Installing sounddevice...
"%PYTHON_PATH%\python.exe" -m pip install sounddevice

echo Installing soundfile...
"%PYTHON_PATH%\python.exe" -m pip install soundfile

echo Installing numpy...
"%PYTHON_PATH%\python.exe" -m pip install numpy

echo Installing scipy...
"%PYTHON_PATH%\python.exe" -m pip install scipy

echo Installing PyTorch (for hardware acceleration)...
"%PYTHON_PATH%\python.exe" -m pip install torch

echo.
echo Installation complete!
pause
