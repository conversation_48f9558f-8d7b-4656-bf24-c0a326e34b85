"""
Process audio with AudioSR (Audio Super Resolution)
"""

import os
import sys
import time
import platform
import argparse
import subprocess
import tempfile

def check_audiosr_installed():
    """Check if AudioSR is installed"""
    try:
        import audiosr
        return True
    except ImportError:
        return False

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def process_audio_with_sr(input_file, output_file=None, model_name="basic", device="cpu", 
                          guidance_scale=3.5, ddim_steps=50, use_win11_opt=True):
    """Process audio with AudioSR"""
    try:
        # Import required modules
        from audiosr import build_model, super_resolution
        import soundfile as sf
        import numpy as np
        
        # Apply Windows 11 optimizations if enabled
        if use_win11_opt and platform.system() == "Windows":
            try:
                from windows11_optimizations import Windows11Optimizer
                print("Applying Windows 11 optimizations...")
                optimizer = Windows11Optimizer()
                optimizations = optimizer.optimize_audio_processing()
                print("Optimization results:")
                for key, value in optimizations.items():
                    print(f"  {key}: {value}")
            except ImportError:
                print("Windows 11 optimizations not available, continuing without them.")
        
        # Generate output filename if not provided
        if output_file is None:
            input_dir = os.path.dirname(input_file)
            input_basename = os.path.basename(input_file)
            input_name, ext = os.path.splitext(input_basename)
            output_dir = os.path.join(input_dir, "audiosr_output")
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{input_name}_SR.wav")
        
        # Build model
        print(f"Building model '{model_name}' on {device}...")
        start_time = time.time()
        model = build_model(model_name=model_name, device=device)
        model_load_time = time.time() - start_time
        print(f"Model loaded in {model_load_time:.2f} seconds")
        
        # Process audio
        print(f"Processing audio: {input_file}")
        print(f"Parameters: guidance_scale={guidance_scale}, ddim_steps={ddim_steps}")
        process_start = time.time()
        
        waveform = super_resolution(
            model,
            input_file,
            guidance_scale=guidance_scale,
            ddim_steps=ddim_steps
        )
        
        process_time = time.time() - process_start
        print(f"Audio processed in {process_time:.2f} seconds")
        
        # Save output
        print(f"Saving output to: {output_file}")
        sf.write(output_file, waveform, 48000)  # AudioSR outputs at 48kHz
        
        return output_file
        
    except ImportError as e:
        print(f"Error: Required module not found: {e}")
        print("Please install AudioSR and its dependencies:")
        print("python install_audiosr.py")
        return None
    except Exception as e:
        print(f"Error processing audio: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Process audio with AudioSR (Audio Super Resolution)")
    parser.add_argument('input_file', help="Path to input audio file")
    parser.add_argument('--output', help="Path to output audio file (optional)")
    parser.add_argument('--model', choices=['basic', 'speech'], default='basic', help="Model to use (default: basic)")
    parser.add_argument('--device', choices=['cpu', 'cuda'], default='cpu', help="Device to use (default: cpu)")
    parser.add_argument('--guidance-scale', type=float, default=3.5, help="Guidance scale (default: 3.5)")
    parser.add_argument('--ddim-steps', type=int, default=50, help="DDIM steps (default: 50)")
    parser.add_argument('--no-win11-opt', action='store_true', help="Disable Windows 11 optimizations")
    parser.add_argument('--play-original', action='store_true', help="Play original audio before processing")
    parser.add_argument('--play-processed', action='store_true', help="Play processed audio after processing")
    parser.add_argument('--install', action='store_true', help="Install AudioSR if not already installed")
    
    args = parser.parse_args()
    
    # Check if AudioSR is installed
    if not check_audiosr_installed():
        print("AudioSR is not installed.")
        if args.install or input("Install AudioSR now? (y/n): ").lower() == 'y':
            print("Installing AudioSR...")
            subprocess.run([sys.executable, "install_audiosr.py"])
        else:
            print("Exiting. Please install AudioSR to continue.")
            return 1
    
    # Check if input file exists
    if not os.path.isfile(args.input_file):
        print(f"Error: Input file not found: {args.input_file}")
        return 1
    
    # Play original if requested
    if args.play_original:
        print("\n=== Playing Original Audio ===")
        play_audio_file(args.input_file)
        # Wait a bit to allow the player to start
        time.sleep(2)
    
    # Process audio
    print("\n=== Processing Audio with Super Resolution ===")
    output_file = process_audio_with_sr(
        args.input_file, 
        args.output, 
        args.model, 
        args.device, 
        args.guidance_scale, 
        args.ddim_steps,
        not args.no_win11_opt
    )
    
    if output_file is None:
        print("Audio processing failed.")
        return 1
    
    # Play processed if requested
    if args.play_processed:
        print("\n=== Playing Processed Audio ===")
        play_audio_file(output_file)
    
    print("\nDone!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
