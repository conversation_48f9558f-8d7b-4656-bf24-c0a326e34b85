"""
Simple script to play the RAVE.flac file
"""

import os
import sys
import platform
import subprocess

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def main():
    # Path to the RAVE.flac file
    file_path = r"C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac"
    
    # Check if file exists
    if not os.path.isfile(file_path):
        print(f"Error: File not found: {file_path}")
        
        # Try to find the file in the Downloads folder
        downloads_dir = os.path.expanduser("~/Downloads")
        print(f"Searching in Downloads folder: {downloads_dir}")
        
        for file in os.listdir(downloads_dir):
            if "RAVE" in file and file.endswith(".flac"):
                file_path = os.path.join(downloads_dir, file)
                print(f"Found file: {file_path}")
                break
        else:
            print("Could not find RAVE.flac in Downloads folder.")
            return 1
    
    # Play the file
    play_audio_file(file_path)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
