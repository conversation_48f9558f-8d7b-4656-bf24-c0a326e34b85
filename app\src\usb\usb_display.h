#ifndef SC_USB_DISPLAY_H
#define SC_USB_DISPLAY_H

#include <stdbool.h>
#include <stdint.h>
#include <libusb.h>
#include "common.h"
#include "util/intr.h"

// Samsung A22 USB VID/PID
#define SAMSUNG_VID 0x04e8
#define SAMSUNG_A22_PID 0x6860  // This may need to be adjusted for specific device

// Protocol constants
#define SAMSUNG_SMART_VIEW_PROTOCOL_VERSION 0x0103
#define SAMSUNG_DEX_PROTOCOL_VERSION 0x0201

// Connection states
enum sc_usb_display_state {
    SC_USB_DISPLAY_STATE_DISCONNECTED,
    SC_USB_DISPLAY_STATE_CONNECTING,
    SC_USB_DISPLAY_STATE_CONNECTED,
    SC_USB_DISPLAY_STATE_ERROR
};

// USB Display connection structure
struct sc_usb_display {
    libusb_context *usb_ctx;
    libusb_device_handle *device_handle;
    enum sc_usb_display_state state;
    
    // Endpoints
    uint8_t ep_in;
    uint8_t ep_out;
    
    // Connection info
    char *device_name;
    uint16_t width;
    uint16_t height;
    
    // Buffers
    unsigned char *recv_buffer;
    size_t recv_buffer_size;
};

// Initialize USB display connection
bool
sc_usb_display_init(struct sc_usb_display *ud);

// Connect to Samsung device
bool
sc_usb_display_connect(struct sc_usb_display *ud, struct sc_intr *intr);

// Disconnect from device
void
sc_usb_display_disconnect(struct sc_usb_display *ud);

// Clean up resources
void
sc_usb_display_destroy(struct sc_usb_display *ud);

// Send display identification data to trick device
bool
sc_usb_display_send_identification(struct sc_usb_display *ud);

// Send HID input event
bool
sc_usb_display_send_input(struct sc_usb_display *ud, const unsigned char *buffer, size_t len);

// Receive screen data
ssize_t
sc_usb_display_receive(struct sc_usb_display *ud, unsigned char *buffer, size_t len);

// Check if device is connected
bool
sc_usb_display_is_connected(struct sc_usb_display *ud);

#endif // SC_USB_DISPLAY_H
