#ifndef SC_SAMSUNG_PROTOCOL_H
#define SC_SAMSUNG_PROTOCOL_H

#include <stdbool.h>
#include <stdint.h>
#include "common.h"

// Samsung protocol types
enum sc_samsung_protocol_type {
    SC_SAMSUNG_PROTOCOL_SMART_VIEW,
    SC_SAMSUNG_PROTOCOL_DEX,
    SC_SAMSUNG_PROTOCOL_UNKNOWN
};

// Smart View protocol constants
#define SMART_VIEW_MAGIC 0x4D535600  // "MSV\0"
#define SMART_VIEW_CMD_HELLO 0x01
#define SMART_VIEW_CMD_READY 0x02
#define SMART_VIEW_CMD_FRAME 0x03
#define SMART_VIEW_CMD_INPUT 0x04
#define SMART_VIEW_CMD_AUDIO 0x05

// DeX protocol constants
#define DEX_MAGIC 0x44455800  // "DEX\0"
#define DEX_CMD_INIT 0x01
#define DEX_CMD_READY 0x02
#define DEX_CMD_FRAME 0x03
#define DEX_CMD_INPUT 0x04
#define DEX_CMD_AUDIO 0x05

// Protocol header structure
struct sc_samsung_protocol_header {
    uint32_t magic;
    uint8_t command;
    uint16_t version;
    uint8_t flags;
};

// Frame data structure
struct sc_samsung_frame {
    uint32_t timestamp;
    uint16_t width;
    uint16_t height;
    uint32_t data_size;
    uint8_t format;
    uint8_t reserved[3];
    // Followed by frame data
};

// Input event structure
struct sc_samsung_input_event {
    uint32_t timestamp;
    uint8_t type;
    uint8_t code;
    uint16_t value;
    uint16_t x;
    uint16_t y;
};

// Create protocol header
void
sc_samsung_protocol_create_header(struct sc_samsung_protocol_header *header,
                                 enum sc_samsung_protocol_type type,
                                 uint8_t command);

// Parse protocol header
enum sc_samsung_protocol_type
sc_samsung_protocol_parse_header(const unsigned char *buffer, size_t len,
                                struct sc_samsung_protocol_header *header);

// Create Smart View hello message
size_t
sc_samsung_protocol_create_smart_view_hello(unsigned char *buffer, size_t len,
                                          const char *device_name,
                                          uint16_t width, uint16_t height);

// Create DeX init message
size_t
sc_samsung_protocol_create_dex_init(unsigned char *buffer, size_t len,
                                   const char *device_name,
                                   uint16_t width, uint16_t height);

// Create input event message
size_t
sc_samsung_protocol_create_input_event(unsigned char *buffer, size_t len,
                                      enum sc_samsung_protocol_type type,
                                      const struct sc_samsung_input_event *event);

// Parse frame data
bool
sc_samsung_protocol_parse_frame(const unsigned char *buffer, size_t len,
                               struct sc_samsung_frame *frame,
                               const unsigned char **frame_data);

#endif // SC_SAMSUNG_PROTOCOL_H
