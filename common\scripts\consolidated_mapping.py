"""
Consolidated File Mapping for Project Reorganization

This script defines mappings for consolidating multiple files into fewer,
more comprehensive files to streamline the project structure.

Usage:
    python consolidated_mapping.py

This will print the mapping information that can be used by the consolidation script.
"""

# Consolidation mappings for each project
CONSOLIDATION_MAPPINGS = {
    # Audio Processing Suite
    "audio_processing": {
        # Consolidate all audio playback scripts into a single file
        "audio_player.py": [
            "play_audio.py",
            "play_audio_from_dir.py",
            "play_dxrk_rave.bat",
            "play_dxrk_rave_simple.bat",
            "play_flac_files.py",
            "play_rave.py",
            "play_rave_simple.py",
            "play_rave_with_opt.py",
            "play_with_win11_opt.py",
        ],
        
        # Consolidate all audio processing scripts into a single file
        "audio_processor.py": [
            "process_audio_sr.py",
            "process_any_audio_sr.py",
            "process_dxrk_rave_sr.bat",
            "process_rave.py",
            "process_rave_simple.py",
        ],
        
        # Consolidate all batch runners into a single file
        "run_audio_tools.bat": [
            "run_audio_upscaler_gui.bat",
            "run_audio_upscaler_test.bat",
            "process_dxrk_rave_sr.bat",
            "process_dxrk_rave_sr_fast.bat",
            "process_any_audio_sr.bat",
            "play_audio_from_downloads.bat",
            "play_dxrk_rave.bat",
            "play_dxrk_rave_simple.bat",
            "play_flac_files.bat",
            "play_rave.bat",
            "play_rave_simple.bat",
            "play_rave_with_opt.bat",
            "play_with_win11_opt.bat",
        ],
        
        # Consolidate all installation scripts into a single file
        "install_audio_tools.py": [
            "install_audiosr.py",
            "install_audiosr_direct.py",
            "setup_audiosr.py",
        ],
        
        # Keep these core files separate as they have distinct purposes
        "keep_separate": [
            "windows11_optimizations.py",
            "realtime_audio_upscaler.py",
            "audio_upscaler_gui.py",
            "audiosr_gui.py",
            "amd_rocm_accelerator.py",
            "mojo_audio_upscaler.mojo",
            "mojo_bridge.py",
        ],
    },
    
    # Samsung Screen Mirroring
    "samsung_mirroring": {
        # Consolidate all Samsung connection methods into a single file
        "samsung_connector.py": [
            "samsung_exploit.bat",
            "samsung_exploit.sh",
        ],
        
        # Create a unified GUI for Samsung screen mirroring
        "samsung_mirroring_gui.py": [],
        
        # Keep these core files separate
        "keep_separate": [
            "main.js",  # scrcpy integration
        ],
    },
    
    # Common utilities
    "common": {
        # Consolidate all Python path-related batch files into a single file
        "setup_python.bat": [
            "add_python_simple.bat",
            "add_python_to_path.bat",
            "add_python_to_system_path.bat",
            "run_with_python312.bat",
            "run_with_scripts_path.bat",
        ],
        
        # Consolidate all installation scripts into a single file
        "install_dependencies.py": [
            "install_packages.bat",
            "install_pytorch.bat",
            "install_realtime_dependencies.bat",
            "run_pip.bat",
        ],
        
        # Consolidate all environment check scripts into a single file
        "check_environment.py": [
            "check_env.py",
            "check_file.py",
            "simple_test.py",
        ],
    },
}

def print_consolidation_info():
    """Print information about the consolidation mappings"""
    total_files = 0
    total_consolidated = 0
    
    print("File Consolidation Information:")
    print("==============================\n")
    
    for project, consolidations in CONSOLIDATION_MAPPINGS.items():
        project_files = 0
        project_consolidated = 0
        
        print(f"{project}:")
        
        for target_file, source_files in consolidations.items():
            if target_file == "keep_separate":
                print(f"  Files to keep separate:")
                for file in source_files:
                    print(f"    - {file}")
                    project_files += 1
                print()
                continue
                
            print(f"  {target_file} will consolidate:")
            for file in source_files:
                print(f"    - {file}")
                project_files += 1
                project_consolidated += 1
            print()
        
        print(f"  Total files: {project_files}")
        print(f"  Files to be consolidated: {project_consolidated}")
        print(f"  Resulting files: {project_files - project_consolidated + len(consolidations) - 1}\n")
        
        total_files += project_files
        total_consolidated += project_consolidated
    
    total_resulting = total_files - total_consolidated + sum(len(consolidations) - 1 for consolidations in CONSOLIDATION_MAPPINGS.values())
    print(f"Total files: {total_files}")
    print(f"Files to be consolidated: {total_consolidated}")
    print(f"Resulting files after consolidation: {total_resulting}")
    print(f"Reduction: {total_files - total_resulting} files ({(total_files - total_resulting) / total_files * 100:.1f}%)")

if __name__ == "__main__":
    print_consolidation_info()
