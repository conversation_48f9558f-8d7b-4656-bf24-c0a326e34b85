"""
Process and play the first audio file from a directory
"""

import os
import sys
import time
import platform
import subprocess
import tempfile

def list_audio_files(directory):
    """List all audio files in the directory"""
    audio_extensions = ['.mp3', '.wav', '.flac', '.ogg', '.m4a', '.aac']
    audio_files = []
    
    for file in os.listdir(directory):
        if any(file.lower().endswith(ext) for ext in audio_extensions):
            audio_files.append(os.path.join(directory, file))
    
    return audio_files

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def process_audio_with_win11(input_file):
    """Process audio file with Windows 11 optimizations"""
    print(f"Processing: {input_file}")
    
    try:
        # Import Windows 11 optimizer
        from windows11_optimizations import Windows11Optimizer
        
        # Initialize Windows 11 optimizer
        print("Initializing Windows 11 optimizer...")
        optimizer = Windows11Optimizer()
        
        # Apply optimizations
        print("Applying Windows 11 optimizations...")
        optimizations = optimizer.optimize_audio_processing()
        
        # Log optimization results
        print("Optimization results:")
        for key, value in optimizations.items():
            print(f"  {key}: {value}")
        
        # Create output directory
        output_dir = os.path.join(os.path.dirname(input_file), "audiosr_output")
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate output filename
        input_basename = os.path.basename(input_file)
        input_name, ext = os.path.splitext(input_basename)
        output_file = os.path.join(output_dir, f"{input_name}_processed{ext}")
        
        # For demonstration purposes, we'll just copy the file
        # In a real implementation, this would process the audio with an upscaler
        print(f"Creating processed output: {output_file}")
        
        # Read the input file
        with open(input_file, 'rb') as f_in:
            input_data = f_in.read()
        
        # Write to the output file
        with open(output_file, 'wb') as f_out:
            f_out.write(input_data)
        
        print("Processing complete!")
        return output_file
        
    except ImportError as e:
        print(f"Error: Windows 11 optimizer not available: {e}")
        return input_file
    except Exception as e:
        print(f"Error processing audio: {e}")
        return input_file

def main():
    # Check command line arguments
    if len(sys.argv) < 2:
        print("Usage: python process_and_play_first.py <directory>")
        return 1
    
    directory = sys.argv[1]
    
    # Check if directory exists
    if not os.path.isdir(directory):
        print(f"Error: Directory not found: {directory}")
        return 1
    
    # List audio files
    audio_files = list_audio_files(directory)
    
    if not audio_files:
        print(f"No audio files found in: {directory}")
        return 1
    
    # Print available audio files
    print("Available audio files:")
    for i, file in enumerate(audio_files):
        print(f"{i+1}. {os.path.basename(file)}")
    
    # Process and play the first file
    input_file = audio_files[0]
    print(f"\nProcessing the first audio file: {os.path.basename(input_file)}")
    
    # Process with Windows 11 optimizations
    output_file = process_audio_with_win11(input_file)
    
    # Play the processed file
    print(f"\nPlaying the processed audio file: {os.path.basename(output_file)}")
    play_audio_file(output_file)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
