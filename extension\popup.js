// Popup script for NeuralCodeAssist extension

document.addEventListener('DOMContentLoaded', () => {
  // Check connection status
  checkConnectionStatus();
  
  // Add event listeners to model buttons
  document.querySelectorAll('.model-btn').forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all buttons
      document.querySelectorAll('.model-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      
      // Add active class to clicked button
      button.classList.add('active');
      
      // Send message to switch model
      chrome.runtime.sendMessage({
        type: 'switch-model',
        model: button.getAttribute('data-model')
      });
    });
  });
});

// Check connection status with native host
function checkConnectionStatus() {
  chrome.runtime.sendMessage({ type: 'check-connection' }, (response) => {
    const statusElement = document.getElementById('connection-status');
    
    if (response && response.connected) {
      statusElement.textContent = 'Connected';
      statusElement.className = 'status connected';
    } else {
      statusElement.textContent = 'Disconnected';
      statusElement.className = 'status disconnected';
      
      // Show reconnect button if disconnected
      if (!document.getElementById('reconnect-btn')) {
        const reconnectBtn = document.createElement('button');
        reconnectBtn.id = 'reconnect-btn';
        reconnectBtn.className = 'model-btn';
        reconnectBtn.textContent = 'Reconnect';
        reconnectBtn.style.marginTop = '10px';
        reconnectBtn.style.width = '100%';
        
        reconnectBtn.addEventListener('click', () => {
          chrome.runtime.sendMessage({ type: 'reconnect' });
          checkConnectionStatus();
        });
        
        document.querySelector('.section').appendChild(reconnectBtn);
      }
    }
  });
}
