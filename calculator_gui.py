import tkinter as tk
from tkinter import ttk, messagebox
import re
from calculator_improved import Calculator, CalculatorError

class CalculatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Scientific Calculator")
        self.root.geometry("400x500")
        self.root.resizable(False, False)

        # Set theme and style
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configure styles
        self.style.configure('TButton', font=('Arial', 12))
        self.style.configure('Display.TEntry', font=('Arial', 16))
        self.style.configure('History.TLabel', font=('Arial', 10), anchor='e')

        # Initialize calculator
        self.calculator = Calculator()

        # Create display variables
        self.display_var = tk.StringVar()
        self.history_var = tk.StringVar()

        # Create UI components
        self.create_ui()

    def create_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # History display
        history_frame = ttk.Frame(main_frame)
        history_frame.pack(fill=tk.X, pady=(0, 5))

        history_label = ttk.Label(
            history_frame,
            textvariable=self.history_var,
            style='History.TLabel',
            wraplength=380
        )
        history_label.pack(fill=tk.X)

        # Display
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(fill=tk.X, pady=(0, 10))

        self.display_entry = ttk.Entry(
            display_frame,
            textvariable=self.display_var,
            style='Display.TEntry',
            justify='right'
        )
        self.display_entry.pack(fill=tk.X, ipady=10)

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.BOTH, expand=True)

        # Configure grid
        for i in range(6):
            buttons_frame.rowconfigure(i, weight=1)
        for i in range(5):
            buttons_frame.columnconfigure(i, weight=1)

        # Function buttons
        function_buttons = [
            ('sin', 0, 0), ('cos', 0, 1), ('tan', 0, 2),
            ('log', 1, 0), ('ln', 1, 1), ('sqrt', 1, 2)
        ]

        for text, row, col in function_buttons:
            btn = ttk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.add_function(t)
            )
            btn.grid(row=row, column=col, sticky='nsew', padx=2, pady=2)

        # Clear and backspace buttons
        ttk.Button(
            buttons_frame,
            text='C',
            command=self.clear
        ).grid(row=0, column=3, sticky='nsew', padx=2, pady=2)

        ttk.Button(
            buttons_frame,
            text='⌫',
            command=self.backspace
        ).grid(row=0, column=4, sticky='nsew', padx=2, pady=2)

        # Parentheses and power buttons
        ttk.Button(
            buttons_frame,
            text='(',
            command=lambda: self.add_text('(')
        ).grid(row=1, column=3, sticky='nsew', padx=2, pady=2)

        ttk.Button(
            buttons_frame,
            text=')',
            command=lambda: self.add_text(')')
        ).grid(row=1, column=4, sticky='nsew', padx=2, pady=2)

        ttk.Button(
            buttons_frame,
            text='^',
            command=lambda: self.add_text('^')
        ).grid(row=2, column=4, sticky='nsew', padx=2, pady=2)

        # Number buttons
        numbers = [
            ('7', 2, 0), ('8', 2, 1), ('9', 2, 2),
            ('4', 3, 0), ('5', 3, 1), ('6', 3, 2),
            ('1', 4, 0), ('2', 4, 1), ('3', 4, 2),
            ('0', 5, 1)
        ]

        for text, row, col in numbers:
            btn = ttk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.add_text(t)
            )
            btn.grid(row=row, column=col, sticky='nsew', padx=2, pady=2)

        # Decimal point and negative
        ttk.Button(
            buttons_frame,
            text='.',
            command=lambda: self.add_text('.')
        ).grid(row=5, column=0, sticky='nsew', padx=2, pady=2)

        ttk.Button(
            buttons_frame,
            text='+/-',
            command=self.toggle_sign
        ).grid(row=5, column=2, sticky='nsew', padx=2, pady=2)

        # Operator buttons
        operators = [
            ('÷', 2, 3, '/'), ('+', 3, 3, '+'),
            ('×', 3, 4, '*'), ('−', 4, 3, '-'),
            ('=', 5, 3, '=')
        ]

        for text, row, col, op in operators:
            btn = ttk.Button(
                buttons_frame,
                text=text,
                command=lambda o=op: self.handle_operator(o)
            )
            btn.grid(row=row, column=col, sticky='nsew', padx=2, pady=2)

            # Make equals button span two columns
            if text == '=':
                btn.grid(columnspan=2, sticky='nsew')

        # Bind keyboard events
        self.root.bind('<Return>', lambda _: self.calculate())
        self.root.bind('<Key>', self.key_pressed)

        # Set focus to the display
        self.display_entry.focus_set()

    def add_text(self, text):
        """Add text to the display"""
        current = self.display_var.get()
        cursor_pos = self.display_entry.index(tk.INSERT)

        # Insert at cursor position
        new_text = current[:cursor_pos] + text + current[cursor_pos:]
        self.display_var.set(new_text)

        # Move cursor after inserted text
        self.display_entry.icursor(cursor_pos + len(text))

    def add_function(self, func):
        """Add a function to the display"""
        self.add_text(f"{func}(")

    def clear(self):
        """Clear the display"""
        self.display_var.set("")

    def backspace(self):
        """Remove the last character"""
        current = self.display_var.get()
        cursor_pos = self.display_entry.index(tk.INSERT)

        if cursor_pos > 0:
            new_text = current[:cursor_pos-1] + current[cursor_pos:]
            self.display_var.set(new_text)
            self.display_entry.icursor(cursor_pos - 1)

    def toggle_sign(self):
        """Toggle the sign of the current number"""
        current = self.display_var.get()
        cursor_pos = self.display_entry.index(tk.INSERT)

        # Find the number at or before the cursor position
        pattern = r'[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?'
        matches = list(re.finditer(pattern, current))

        for match in reversed(matches):
            start, end = match.span()
            if start <= cursor_pos <= end:
                number = match.group()
                if number.startswith('-'):
                    new_number = number[1:]
                else:
                    new_number = '-' + number

                new_text = current[:start] + new_number + current[end:]
                self.display_var.set(new_text)

                # Adjust cursor position
                new_cursor_pos = cursor_pos
                if number.startswith('-'):
                    new_cursor_pos -= 1
                else:
                    new_cursor_pos += 1

                self.display_entry.icursor(min(new_cursor_pos, len(new_text)))
                break

    def handle_operator(self, op):
        """Handle operator button press"""
        if op == '=':
            self.calculate()
        else:
            self.add_text(op)

    def calculate(self):
        """Calculate the result of the expression"""
        expression = self.display_var.get()

        if not expression:
            return

        # Update history
        self.history_var.set(expression)

        try:
            # Replace display symbols with calculator operators
            calc_expr = expression.replace('×', '*').replace('÷', '/')

            # Calculate result
            result = self.calculator.evaluate(calc_expr)

            # Format result
            if result == int(result):
                formatted_result = str(int(result))
            else:
                formatted_result = str(result)

            # Update display
            self.display_var.set(formatted_result)

        except CalculatorError as e:
            messagebox.showerror("Error", e.message)
        except Exception as e:
            messagebox.showerror("Error", str(e))

    def key_pressed(self, event):
        """Handle keyboard input"""
        key = event.char

        # Ignore special keys
        if not key:
            return

        # Map keyboard operators to calculator operators
        if key in '0123456789.()+-*/^':
            self.add_text(key)
        elif key == '\r':  # Enter key
            self.calculate()
        elif key == '\x08':  # Backspace
            self.backspace()

def main():
    root = tk.Tk()
    CalculatorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
