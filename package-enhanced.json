{"name": "interview-assistant", "version": "1.0.0", "description": "Desktop application for technical interview assistance", "main": "main-enhanced.js", "scripts": {"start": "electron .", "build": "electron-builder"}, "author": "", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.6.2", "electron-screenshot-app": "^4.0.3", "form-data": "^4.0.0", "llama-node": "^0.1.6", "node-tesseract-ocr": "^2.2.1", "python-shell": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "build": {"appId": "com.interview.assistant", "productName": "Interview Assistant", "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "models", "to": "models", "filter": ["**/*"]}], "mac": {"category": "public.app-category.developer-tools", "target": ["dmg", "zip"]}, "win": {"target": ["nsis", "portable"]}, "linux": {"target": ["AppImage", "deb"], "category": "Development"}}}