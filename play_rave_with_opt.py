"""
Play RAVE.flac with Windows 11 optimizations
"""

import os
import sys
import platform
import subprocess

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Apply Windows 11 optimizations if available
    try:
        from windows11_optimizations import Windows11Optimizer
        print("Applying Windows 11 optimizations...")
        optimizer = Windows11Optimizer()
        optimizations = optimizer.optimize_audio_processing()
        print("Optimization results:")
        for key, value in optimizations.items():
            print(f"  {key}: {value}")
    except ImportError:
        print("Windows 11 optimizations not available, continuing without them.")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def main():
    # Path to the RAVE.flac file
    downloads_dir = os.path.expanduser("~/Downloads")
    
    # Try to find the RAVE.flac file
    rave_file = None
    for file in os.listdir(downloads_dir):
        if "RAVE" in file and file.endswith(".flac"):
            rave_file = os.path.join(downloads_dir, file)
            break
    
    if rave_file is None:
        print("Could not find RAVE.flac in Downloads folder.")
        return 1
    
    print(f"Found RAVE.flac: {rave_file}")
    
    # Play the file with Windows 11 optimizations
    play_audio_file(rave_file)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
