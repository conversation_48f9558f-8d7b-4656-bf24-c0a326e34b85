"""
Very simple script to play RAVE.flac
"""

import os
import sys

def main():
    # Path to the RAVE.flac file
    downloads_dir = os.path.expanduser("~/Downloads")
    
    # Try to find the RAVE.flac file
    rave_file = None
    for file in os.listdir(downloads_dir):
        if "RAVE" in file and file.endswith(".flac"):
            rave_file = os.path.join(downloads_dir, file)
            break
    
    if rave_file is None:
        print("Could not find RAVE.flac in Downloads folder.")
        return 1
    
    print(f"Found RAVE.flac: {rave_file}")
    
    # Play the file using the default system player
    print(f"Playing: {rave_file}")
    os.startfile(rave_file)
    
    print("Audio playback started. Use the system player controls to control playback.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
