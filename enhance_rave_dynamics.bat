@echo off
echo Enhancing dynamic range of RAVE.flac...
echo.

REM Check if required packages are installed
python -c "import numpy, scipy, soundfile" 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    pip install numpy scipy soundfile
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install required packages. Please install them manually.
        pause
        exit /b 1
    )
)

REM Set quality level (3=highest quality, preserves dynamics better)
set QUALITY=3

echo Processing with quality level %QUALITY%...
echo Using multiband dynamic range enhancement with transient preservation...
echo This may take some time for higher quality results...
echo.

python enhance_rave_dynamics.py --quality %QUALITY%

if %ERRORLEVEL% NEQ 0 (
    echo Processing failed. Trying with simpler settings...
    python enhance_rave_dynamics.py --quality %QUALITY% --no-multiband
    
    if %ERRORLEVEL% NEQ 0 (
        echo Processing failed. Trying with minimal settings...
        python enhance_rave_dynamics.py --quality 1 --no-multiband --no-transients
        
        if %ERRORLEVEL% NEQ 0 (
            echo Processing failed.
            pause
            exit /b 1
        )
    )
)

echo.
echo Processing complete!
echo.
echo The enhanced file has been created with improved dynamic range.
echo.
pause
