@echo off
echo Project Reorganization Script
echo ============================
echo.
echo This script will reorganize the project files according to the defined mappings.
echo It will copy files to their target directories and add header comments.
echo The original files will remain in place.
echo.

REM Set Python path
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Run the reorganization script
echo Running reorganization script...
"%PYTHON_PATH%\python.exe" common\scripts\reorganize_files.py

echo.
echo Done!
pause
