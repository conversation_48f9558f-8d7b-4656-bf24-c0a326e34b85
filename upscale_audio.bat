@echo off
setlocal enabledelayedexpansion

echo Audio Upscaling System
echo =====================
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python 3.6 or higher.
    pause
    exit /b 1
)

REM Check if required modules are installed
python -c "import torch, numpy, soundfile" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing required modules...
    python -m pip install torch numpy soundfile scipy
)

REM Parse command line arguments
set INPUT=
set OUTPUT=
set QUALITY=3
set SAMPLE_RATE=
set BIT_DEPTH=
set LUFS=
set BATCH=0
set RECURSIVE=0
set CPU=0
set NO_WIN11_OPT=0
set FULL_LOAD=0
set SKIP_PRE=0
set SKIP_POST=0
set ANALYZE=0
set COMPARE=

:parse_args
if "%~1"=="" goto :end_parse_args
if /i "%~1"=="-o" (
    set OUTPUT=%~2
    shift
) else if /i "%~1"=="--output" (
    set OUTPUT=%~2
    shift
) else if /i "%~1"=="-q" (
    set QUALITY=%~2
    shift
) else if /i "%~1"=="--quality" (
    set QUALITY=%~2
    shift
) else if /i "%~1"=="-s" (
    set SAMPLE_RATE=%~2
    shift
) else if /i "%~1"=="--sample-rate" (
    set SAMPLE_RATE=%~2
    shift
) else if /i "%~1"=="-d" (
    set BIT_DEPTH=%~2
    shift
) else if /i "%~1"=="--bit-depth" (
    set BIT_DEPTH=%~2
    shift
) else if /i "%~1"=="-l" (
    set LUFS=%~2
    shift
) else if /i "%~1"=="--lufs" (
    set LUFS=%~2
    shift
) else if /i "%~1"=="-b" (
    set BATCH=1
) else if /i "%~1"=="--batch" (
    set BATCH=1
) else if /i "%~1"=="-r" (
    set RECURSIVE=1
) else if /i "%~1"=="--recursive" (
    set RECURSIVE=1
) else if /i "%~1"=="--cpu" (
    set CPU=1
) else if /i "%~1"=="--no-win11-opt" (
    set NO_WIN11_OPT=1
) else if /i "%~1"=="--full-load" (
    set FULL_LOAD=1
) else if /i "%~1"=="--skip-preprocessing" (
    set SKIP_PRE=1
) else if /i "%~1"=="--skip-postprocessing" (
    set SKIP_POST=1
) else if /i "%~1"=="-a" (
    set ANALYZE=1
) else if /i "%~1"=="--analyze" (
    set ANALYZE=1
) else if /i "%~1"=="--compare" (
    set COMPARE=%~2
    shift
) else if /i "%~1"=="-h" (
    goto :show_help
) else if /i "%~1"=="--help" (
    goto :show_help
) else (
    set INPUT=%~1
)
shift
goto :parse_args
:end_parse_args

REM Check if input is provided
if "%INPUT%"=="" (
    echo Error: No input file or directory specified.
    echo.
    goto :show_help
)

REM Build command
set CMD=python audio_upscaling_system.py "%INPUT%"

if not "%OUTPUT%"=="" (
    set CMD=!CMD! -o "%OUTPUT%"
)

if not "%QUALITY%"=="" (
    set CMD=!CMD! -q %QUALITY%
)

if not "%SAMPLE_RATE%"=="" (
    set CMD=!CMD! -s %SAMPLE_RATE%
)

if not "%BIT_DEPTH%"=="" (
    set CMD=!CMD! -d %BIT_DEPTH%
)

if not "%LUFS%"=="" (
    set CMD=!CMD! -l %LUFS%
)

if %BATCH%==1 (
    set CMD=!CMD! -b
)

if %RECURSIVE%==1 (
    set CMD=!CMD! -r
)

if %CPU%==1 (
    set CMD=!CMD! --cpu
)

if %NO_WIN11_OPT%==1 (
    set CMD=!CMD! --no-win11-opt
)

if %FULL_LOAD%==1 (
    set CMD=!CMD! --full-load
)

if %SKIP_PRE%==1 (
    set CMD=!CMD! --skip-preprocessing
)

if %SKIP_POST%==1 (
    set CMD=!CMD! --skip-postprocessing
)

if %ANALYZE%==1 (
    set CMD=!CMD! -a
)

if not "%COMPARE%"=="" (
    set CMD=!CMD! --compare "%COMPARE%"
)

REM Run command
echo Running: !CMD!
echo.
!CMD!

echo.
echo Processing complete.
pause
exit /b 0

:show_help
echo Usage: upscale_audio.bat [options] input
echo.
echo Options:
echo   -o, --output FILE         Output file or directory
echo   -q, --quality LEVEL       Quality level (1=Low, 2=Medium, 3=High)
echo   -s, --sample-rate RATE    Target sample rate
echo   -d, --bit-depth DEPTH     Target bit depth (16, 24, 32)
echo   -l, --lufs LEVEL          Target LUFS level
echo   -b, --batch               Batch process directory
echo   -r, --recursive           Process subdirectories recursively
echo   --cpu                     Force CPU processing
echo   --no-win11-opt            Disable Windows 11 optimizations
echo   --full-load               Use full CPU/GPU load
echo   --skip-preprocessing      Skip preprocessing
echo   --skip-postprocessing     Skip postprocessing
echo   -a, --analyze             Analyze audio file without processing
echo   --compare FILE            Compare with another file
echo   -h, --help                Show this help message
echo.
echo Examples:
echo   upscale_audio.bat -q 3 -s 96000 -d 24 input.wav
echo   upscale_audio.bat -b -r -q 2 input_directory
echo   upscale_audio.bat -a input.wav
echo.
pause
exit /b 0
