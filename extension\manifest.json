{"manifest_version": 3, "name": "NeuralCodeAssist", "version": "1.0.0", "description": "AI-powered coding assistant with sci-fi interface", "permissions": ["activeTab", "scripting", "storage", "nativeMessaging"], "host_permissions": ["*://*.leetcode.com/*", "*://*.hackerrank.com/*", "*://*.codechef.com/*", "*://*.codeforces.com/*", "*://*.codesignal.com/*", "*://*.topcoder.com/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://*.leetcode.com/*", "*://*.hackerrank.com/*", "*://*.codechef.com/*", "*://*.codeforces.com/*", "*://*.codesignal.com/*", "*://*.topcoder.com/*"], "js": ["content.js"]}], "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "commands": {"toggle-overlay": {"suggested_key": {"default": "Ctrl+B", "mac": "Command+B"}, "description": "Toggle solution overlay"}, "capture-problem": {"suggested_key": {"default": "Ctrl+H", "mac": "Command+H"}, "description": "Capture problem and generate solution"}}, "nativeMessaging": {"allowed_origins": ["chrome-extension://<extension-id>/"]}}