@echo off
echo Adding Python to system PATH permanently...

REM Define Python paths
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Python312\Scripts"
set "USER_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Check if Python exists
if not exist "%PYTHON_PATH%\python.exe" (
    echo Python not found at %PYTHON_PATH%
    goto :end
)

REM Add Python to system PATH permanently
setx PATH "%PYTHON_PATH%;%PYTHON_SCRIPTS%;%USER_SCRIPTS%;%PATH%" /M

echo Python has been added to your system PATH permanently.
echo Please restart your command prompt or terminal for the changes to take effect.

:end
pause
