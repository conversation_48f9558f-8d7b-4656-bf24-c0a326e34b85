"""
Python bridge to Mojo audio upscaler implementation
"""

import os
import numpy as np
import subprocess
import tempfile
from typing import Tuple

class MojoBridge:
    """
    Bridge to Mojo audio upscaler implementation
    """
    def __init__(self):
        self.mojo_available = self._check_mojo()
        self.mojo_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mojo_audio_upscaler.mojo')

    def _check_mojo(self) -> bool:
        """Check if Mojo is available"""
        try:
            # Try to run mojo --version
            result = subprocess.run(['mojo', '--version'],
                                   capture_output=True,
                                   text=True)
            return result.returncode == 0
        except (subprocess.SubprocessError, OSError):
            return False

    def compile_mojo(self) -> bool:
        """Compile Mojo implementation"""
        if not self.mojo_available:
            print("Mojo is not available. Using fallback implementation.")
            return False

        try:
            # Compile Mojo file
            result = subprocess.run(['mojo', 'build', self.mojo_path, '-o', 'audio_upscaler'],
                                   capture_output=True,
                                   text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"Error compiling Mojo implementation: {e}")
            return False

    def upscale_audio(self,
                     audio: np.ndarray,
                     sample_rate: int,
                     target_sample_rate: int) -> Tuple[np.ndarray, int]:
        """
        Upscale audio using Mojo implementation

        Args:
            audio: Audio data as numpy array (samples, channels)
            sample_rate: Original sample rate
            target_sample_rate: Target sample rate

        Returns:
            Tuple of (upscaled_audio, new_sample_rate)
        """
        if not self.mojo_available:
            # Use fallback implementation
            return self._fallback_upscale(audio, sample_rate, target_sample_rate)

        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix='.npy', delete=False) as f:
                input_path = f.name
                np.save(input_path, audio)

            # Create output path
            output_path = input_path + '.out.npy'

            # Run Mojo implementation
            cmd = [
                './audio_upscaler',
                input_path,
                output_path,
                str(sample_rate),
                str(target_sample_rate)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                print(f"Error running Mojo implementation: {result.stderr}")
                return self._fallback_upscale(audio, sample_rate, target_sample_rate)

            # Load output
            upscaled_audio = np.load(output_path)

            # Clean up
            try:
                os.unlink(input_path)
                os.unlink(output_path)
            except OSError:
                pass

            return upscaled_audio, target_sample_rate

        except Exception as e:
            print(f"Error using Mojo implementation: {e}")
            return self._fallback_upscale(audio, sample_rate, target_sample_rate)

    def _fallback_upscale(self,
                         audio: np.ndarray,
                         sample_rate: int,
                         target_sample_rate: int) -> Tuple[np.ndarray, int]:
        """
        Fallback implementation using scipy

        Args:
            audio: Audio data as numpy array
            sample_rate: Original sample rate
            target_sample_rate: Target sample rate

        Returns:
            Tuple of (upscaled_audio, new_sample_rate)
        """
        try:
            from scipy import signal

            # Calculate ratio
            ratio = target_sample_rate / sample_rate

            # Resample
            if audio.ndim == 1:
                # Mono
                upscaled = signal.resample_poly(audio, target_sample_rate, sample_rate)
            else:
                # Stereo or multi-channel
                upscaled = np.zeros((int(audio.shape[0] * ratio), audio.shape[1]))
                for i in range(audio.shape[1]):
                    upscaled[:, i] = signal.resample_poly(audio[:, i], target_sample_rate, sample_rate)

            return upscaled, target_sample_rate

        except Exception as e:
            print(f"Error in fallback upscaling: {e}")
            # Return original audio
            return audio, sample_rate
