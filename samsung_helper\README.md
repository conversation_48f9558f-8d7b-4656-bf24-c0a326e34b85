# Samsung Screen Mirroring Application

A tool that enables screen mirroring for Samsung devices (particularly the A22) by exploiting vulnerabilities or using alternative connection methods.

## Project Structure

- **src/**: Core source code
  - Exploit implementation
  - Screen mirroring logic
  - Device communication

- **gui/**: Graphical user interface components
  - Control interface
  - Device selection
  - Connection management

- **scripts/**: Utility scripts
  - Exploit scripts
  - Installation scripts
  - Development utilities

- **utils/**: Helper utilities
  - ADB wrappers
  - Device detection
  - Error handling

- **tests/**: Test files
  - Unit tests
  - Integration tests
  - Device compatibility tests

- **docs/**: Documentation
  - User guides
  - Technical documentation
  - Security considerations

## Key Features

- Mimics HDMI-to-OTG connections
- Integrates with scrcpy for screen mirroring
- GUI for interacting with the Samsung device
- Exploits Samsung TTS vulnerability to enable USB debugging
- Works without requiring user interaction on the device

## Related Files

- **samsung_exploit.bat**: Windows batch script for exploiting Samsung devices
- **samsung_exploit.sh**: Linux/macOS shell script for the same purpose
- **README_SAMSUNG_EXPLOIT.md**: Documentation for the exploit
- **README_SAMSUNG_OTG.md** and **README_SAMSUNG_USB.md**: Documentation for alternative methods

## Usage

See the documentation in the `docs/` directory for detailed usage instructions.
