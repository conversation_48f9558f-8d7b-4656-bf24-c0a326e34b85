@echo off
echo Processing "Dxrk ダーク - RAVE.flac" with Audio Super Resolution (Fast Mode)

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Check if AudioSR is installed
echo Checking if <PERSON><PERSON> is installed...
"%PYTHON_PATH%\python.exe" -c "import audiosr" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo AudioSR is not installed. Installing now...
    "%PYTHON_PATH%\python.exe" install_audiosr.py
)

REM Process the audio file with fewer DDIM steps for faster processing
echo.
echo Processing audio file in fast mode...
"%PYTHON_PATH%\python.exe" process_audio_sr.py "C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac" --model basic --device cpu --guidance-scale 3.5 --ddim-steps 10 --play-processed

echo.
echo Done!
pause
