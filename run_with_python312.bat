@echo on
echo Adding Python 3.12 to PATH and running Windows 11 optimizations test...

REM Add Python 3.12 to PATH
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Python312\Scripts"

REM Check if Python exists at the expected location
if exist "%PYTHON_PATH%\python.exe" (
    echo Found Python at: %PYTHON_PATH%
) else (
    echo Python not found at %PYTHON_PATH%
    echo Trying alternative locations...

    set "PYTHON_PATH=C:\Program Files\Python312"
    set "PYTHON_SCRIPTS=C:\Program Files\Python312\Scripts"

    if exist "%PYTHON_PATH%\python.exe" (
        echo Found Python at: %PYTHON_PATH%
    ) else (
        set "PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python312"
        set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts"

        if exist "%PYTHON_PATH%\python.exe" (
            echo Found Python at: %PYTHON_PATH%
        ) else (
            echo Python not found at common locations.
            echo Please make sure Python 3.12 is installed.
            goto :end
        )
    )
)

REM Add Python to PATH for this session
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

echo Python has been added to PATH for this session.
echo Testing Python installation...
echo Running: "%PYTHON_PATH%\python.exe" --version
"%PYTHON_PATH%\python.exe" --version

if %ERRORLEVEL% NEQ 0 (
    echo Failed to run Python. Please check your installation.
    goto :end
)

echo.
echo Running Windows 11 optimizations test...
echo Running: "%PYTHON_PATH%\python.exe" windows11_optimizations.py
"%PYTHON_PATH%\python.exe" windows11_optimizations.py

:end
echo.
echo Press any key to exit...
pause > nul
