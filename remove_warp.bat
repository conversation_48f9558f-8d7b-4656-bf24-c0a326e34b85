@echo off
echo Removing all Warp files and related components...
echo.

REM Set to continue on errors
setlocal EnableDelayedExpansion

REM Kill any running Warp processes
echo Terminating any running Warp processes...
taskkill /F /IM Warp.exe 2>NUL
if %ERRORLEVEL% EQU 0 (
    echo Successfully terminated Warp processes.
) else (
    echo No running Warp processes found.
)
echo.

REM Remove Warp from Program Files
echo Removing Warp installation directory...
if exist "C:\Program Files\Warp" (
    rd /s /q "C:\Program Files\Warp"
    echo Warp installation directory removed.
) else (
    echo Warp installation directory not found.
)
echo.

REM Remove Warp from AppData directories
echo Removing Warp data from AppData...
if exist "%LOCALAPPDATA%\Warp" (
    rd /s /q "%LOCALAPPDATA%\Warp"
    echo Removed Warp from Local AppData.
)
if exist "%APPDATA%\Warp" (
    rd /s /q "%APPDATA%\Warp"
    echo Removed Warp from Roaming AppData.
)
echo.

REM Remove Warp registry entries
echo Removing Warp registry entries...
reg delete "HKCU\Software\Warp" /f 2>NUL
if %ERRORLEVEL% EQU 0 (
    echo Removed Warp registry entries from HKCU.
) else (
    echo No Warp registry entries found in HKCU.
)

reg delete "HKLM\Software\Warp" /f 2>NUL
if %ERRORLEVEL% EQU 0 (
    echo Removed Warp registry entries from HKLM.
) else (
    echo No Warp registry entries found in HKLM.
)

reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Warp" /f 2>NUL
if %ERRORLEVEL% EQU 0 (
    echo Removed Warp uninstall registry entry.
) else (
    echo No Warp uninstall registry entry found.
)
echo.

REM Remove Warp from startup
echo Removing Warp from startup...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "Warp" /f 2>NUL
if %ERRORLEVEL% EQU 0 (
    echo Removed Warp from startup.
) else (
    echo Warp not found in startup.
)
echo.

REM Clean up any temporary files
echo Cleaning up temporary files...
if exist "%TEMP%\Warp*" (
    del /f /s /q "%TEMP%\Warp*" 2>NUL
    echo Removed Warp temporary files.
) else (
    echo No Warp temporary files found.
)
echo.

REM Remove start menu shortcuts
echo Removing Start Menu shortcuts...
if exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Warp" (
    rd /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Warp"
    echo Removed Warp Start Menu shortcuts.
) else (
    echo No Warp Start Menu shortcuts found.
)

if exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Warp" (
    rd /s /q "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Warp"
    echo Removed Warp Start Menu shortcuts from ProgramData.
) else (
    echo No Warp Start Menu shortcuts found in ProgramData.
)
echo.

REM Remove desktop shortcuts
echo Removing Desktop shortcuts...
if exist "%USERPROFILE%\Desktop\Warp.lnk" (
    del /f /q "%USERPROFILE%\Desktop\Warp.lnk"
    echo Removed Warp desktop shortcut.
) else (
    echo No Warp desktop shortcut found.
)

if exist "%PUBLIC%\Desktop\Warp.lnk" (
    del /f /q "%PUBLIC%\Desktop\Warp.lnk"
    echo Removed Warp public desktop shortcut.
) else (
    echo No Warp public desktop shortcut found.
)
echo.

REM Remove any previously created start scripts
echo Removing any previously created Warp start scripts...
if exist "start_warp.ps1" del /f /q "start_warp.ps1"
if exist "start_warp.bat" del /f /q "start_warp.bat"
if exist "start_warp_mutex.ps1" del /f /q "start_warp_mutex.ps1"
if exist "start_warp_mutex.bat" del /f /q "start_warp_mutex.bat"
if exist "start_warp_batch.bat" del /f /q "start_warp_batch.bat"
echo.

echo Warp has been completely removed from your system.
echo If you want to reinstall Warp, please download a fresh copy from the official website.
echo.
pause
