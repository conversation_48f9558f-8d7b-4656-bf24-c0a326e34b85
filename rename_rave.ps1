# Rename the RAVE.flac file to remove Japanese characters

$originalPath = "C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac"
$newPath = "C:\Users\<USER>\Downloads\RAVE.flac"

# Check if the original file exists
if (Test-Path $originalPath) {
    Write-Host "Found original file: $originalPath"
    
    # Check if the destination already exists
    if (Test-Path $newPath) {
        Write-Host "Destination file already exists: $newPath"
        Write-Host "Using existing file."
    } else {
        # Copy the file (instead of renaming to preserve the original)
        Write-Host "Copying file to: $newPath"
        Copy-Item -Path $originalPath -Destination $newPath
        Write-Host "File copied successfully."
    }
} else {
    Write-Host "Error: Original file not found: $originalPath"
    exit 1
}

Write-Host "Done!"
