@echo off
echo Running Audio Upscaler GUI with Windows 11 Optimizations

REM Set Python paths
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Check if required packages are installed
echo Checking required packages...
"%PYTHON_PATH%\python.exe" -c "try: import numpy, soundfile, scipy; print('All required packages are installed.'); exit(0); except ImportError as e: print(f'Missing package: {e}'); exit(1)"

if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    "%PYTHON_PATH%\python.exe" -m pip install numpy soundfile scipy
)

REM Run the GUI
echo Starting Audio Upscaler GUI...
"%PYTHON_PATH%\python.exe" audio_upscaler_gui.py

echo.
echo Done!
pause
