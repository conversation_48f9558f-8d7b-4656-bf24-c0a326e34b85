#include "common.h"

#include <assert.h>

#include "options.h"

static void test_transforms(void) {
    #define O(X) SC_ORIENTATION_ ## X
    #define ASSERT_TRANSFORM(SRC, TR, RES) \
        assert(sc_orientation_apply(O(SRC), O(TR)) == O(RES));

    ASSERT_TRANSFORM(0, 0, 0);
    ASSERT_TRANSFORM(0, 90, 90);
    ASSERT_TRANSFORM(0, 180, 180);
    ASSERT_TRANSFORM(0, 270, 270);
    ASSERT_TRANSFORM(0, FLIP_0, FLIP_0);
    ASSERT_TRANSFORM(0, FLIP_90, FLIP_90);
    ASSERT_TRANSFORM(0, FLIP_180, FLIP_180);
    ASSERT_TRANSFORM(0, FLIP_270, FLIP_270);

    ASSERT_TRANSFORM(90, 0, 90);
    ASSERT_TRANSFORM(90, 90, 180);
    ASSERT_TRANSFORM(90, 180, 270);
    ASSERT_TRANSFORM(90, 270, 0);
    ASSERT_TRANSFORM(90, FLIP_0, FLIP_270);
    ASSERT_TRANSFORM(90, FLIP_90, FLIP_0);
    ASSERT_TRANSFORM(90, FLIP_180, FLIP_90);
    ASSERT_TRANSFORM(90, FLIP_270, FLIP_180);

    ASSERT_TRANSFORM(180, 0, 180);
    ASSERT_TRANSFORM(180, 90, 270);
    ASSERT_TRANSFORM(180, 180, 0);
    ASSERT_TRANSFORM(180, 270, 90);
    ASSERT_TRANSFORM(180, FLIP_0, FLIP_180);
    ASSERT_TRANSFORM(180, FLIP_90, FLIP_270);
    ASSERT_TRANSFORM(180, FLIP_180, FLIP_0);
    ASSERT_TRANSFORM(180, FLIP_270, FLIP_90);

    ASSERT_TRANSFORM(270, 0, 270);
    ASSERT_TRANSFORM(270, 90, 0);
    ASSERT_TRANSFORM(270, 180, 90);
    ASSERT_TRANSFORM(270, 270, 180);
    ASSERT_TRANSFORM(270, FLIP_0, FLIP_90);
    ASSERT_TRANSFORM(270, FLIP_90, FLIP_180);
    ASSERT_TRANSFORM(270, FLIP_180, FLIP_270);
    ASSERT_TRANSFORM(270, FLIP_270, FLIP_0);

    ASSERT_TRANSFORM(FLIP_0, 0, FLIP_0);
    ASSERT_TRANSFORM(FLIP_0, 90, FLIP_90);
    ASSERT_TRANSFORM(FLIP_0, 180, FLIP_180);
    ASSERT_TRANSFORM(FLIP_0, 270, FLIP_270);
    ASSERT_TRANSFORM(FLIP_0, FLIP_0, 0);
    ASSERT_TRANSFORM(FLIP_0, FLIP_90, 90);
    ASSERT_TRANSFORM(FLIP_0, FLIP_180, 180);
    ASSERT_TRANSFORM(FLIP_0, FLIP_270, 270);

    ASSERT_TRANSFORM(FLIP_90, 0, FLIP_90);
    ASSERT_TRANSFORM(FLIP_90, 90, FLIP_180);
    ASSERT_TRANSFORM(FLIP_90, 180, FLIP_270);
    ASSERT_TRANSFORM(FLIP_90, 270, FLIP_0);
    ASSERT_TRANSFORM(FLIP_90, FLIP_0, 270);
    ASSERT_TRANSFORM(FLIP_90, FLIP_90, 0);
    ASSERT_TRANSFORM(FLIP_90, FLIP_180, 90);
    ASSERT_TRANSFORM(FLIP_90, FLIP_270, 180);

    ASSERT_TRANSFORM(FLIP_180, 0, FLIP_180);
    ASSERT_TRANSFORM(FLIP_180, 90, FLIP_270);
    ASSERT_TRANSFORM(FLIP_180, 180, FLIP_0);
    ASSERT_TRANSFORM(FLIP_180, 270, FLIP_90);
    ASSERT_TRANSFORM(FLIP_180, FLIP_0, 180);
    ASSERT_TRANSFORM(FLIP_180, FLIP_90, 270);
    ASSERT_TRANSFORM(FLIP_180, FLIP_180, 0);
    ASSERT_TRANSFORM(FLIP_180, FLIP_270, 90);

    ASSERT_TRANSFORM(FLIP_270, 0, FLIP_270);
    ASSERT_TRANSFORM(FLIP_270, 90, FLIP_0);
    ASSERT_TRANSFORM(FLIP_270, 180, FLIP_90);
    ASSERT_TRANSFORM(FLIP_270, 270, FLIP_180);
    ASSERT_TRANSFORM(FLIP_270, FLIP_0, 90);
    ASSERT_TRANSFORM(FLIP_270, FLIP_90, 180);
    ASSERT_TRANSFORM(FLIP_270, FLIP_180, 270);
    ASSERT_TRANSFORM(FLIP_270, FLIP_270, 0);
}

int main(int argc, char *argv[]) {
    (void) argc;
    (void) argv;

    test_transforms();
    return 0;
}
