// DOM Elements
const tabButtons = document.querySelectorAll('.tab-btn');
const tabPanes = document.querySelectorAll('.tab-pane');
const minimizeBtn = document.getElementById('minimize-btn');
const closeBtn = document.getElementById('close-btn');
const analysisContent = document.getElementById('analysis-content');
const approachContent = document.getElementById('approach-content');
const solutionContent = document.getElementById('solution-content');
const timeContent = document.getElementById('time-content');
const spaceContent = document.getElementById('space-content');

// Tab switching
tabButtons.forEach(button => {
  button.addEventListener('click', () => {
    // Remove active class from all buttons and panes
    tabButtons.forEach(btn => btn.classList.remove('active'));
    tabPanes.forEach(pane => pane.classList.remove('active'));
    
    // Add active class to clicked button and corresponding pane
    button.classList.add('active');
    const tabId = `${button.dataset.tab}-tab`;
    document.getElementById(tabId).classList.add('active');
  });
});

// Minimize button
minimizeBtn.addEventListener('click', () => {
  window.api.send('toggle-overlay');
});

// Close button
closeBtn.addEventListener('click', () => {
  window.api.send('toggle-overlay');
});

// Receive solution from main process
window.api.receive('solution-ready', (solution) => {
  // Update content in each tab
  analysisContent.innerHTML = formatText(solution.analysis);
  approachContent.innerHTML = formatText(solution.approach);
  solutionContent.innerHTML = formatCode(solution.code);
  timeContent.innerHTML = formatText(solution.time_complexity);
  spaceContent.innerHTML = formatText(solution.space_complexity);
});

// Format text with markdown-like syntax
function formatText(text) {
  if (!text) return '';
  
  // Replace line breaks with <br>
  text = text.replace(/\n/g, '<br>');
  
  // Bold text between ** **
  text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // Italic text between * *
  text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // Code snippets between ` `
  text = text.replace(/`(.*?)`/g, '<code>$1</code>');
  
  return text;
}

// Format code with syntax highlighting
function formatCode(code) {
  if (!code) return '';
  
  // Simple syntax highlighting
  // This is a basic implementation - a real solution would use a library like highlight.js
  
  // Remove code block markers
  code = code.replace(/```\w*\n/g, '').replace(/```$/g, '');
  
  // Highlight keywords
  const keywords = ['function', 'return', 'if', 'else', 'for', 'while', 'class', 'const', 'let', 'var', 'import', 'export', 'from', 'try', 'catch', 'switch', 'case', 'break', 'default', 'continue', 'new', 'this', 'super', 'extends', 'def', 'lambda', 'in', 'not', 'and', 'or', 'True', 'False', 'None'];
  
  keywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'g');
    code = code.replace(regex, `<span class="keyword">${keyword}</span>`);
  });
  
  // Highlight strings
  code = code.replace(/(["'])(.*?)\1/g, '<span class="string">$1$2$1</span>');
  
  // Highlight comments
  code = code.replace(/\/\/(.*?)$/gm, '<span class="comment">//$1</span>');
  code = code.replace(/\/\*(.*?)\*\//gs, '<span class="comment">/*$1*/</span>');
  code = code.replace(/#(.*?)$/gm, '<span class="comment">#$1</span>');
  
  return code;
}
