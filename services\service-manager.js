const MistralOCR = require('./mistral-ocr');
const GroqService = require('./groq-service');
const LightningBitNet = require('./lightning-bitnet');
const LocalModel = require('./local-model');
const path = require('path');
const fs = require('fs');
const config = require('../config');

class ServiceManager {
  constructor() {
    // Initialize services
    this.mistralOcr = new MistralOCR();
    this.groqService = new GroqService();
    this.lightningBitNet = new LightningBitNet();
    this.localModel = new LocalModel();
    
    // Create cache directory if it doesn't exist
    this.cacheDir = path.join(__dirname, '..', config.app.solutionCacheDir);
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true });
    }
    
    // Create screenshots directory if it doesn't exist
    this.screenshotDir = path.join(__dirname, '..', config.app.screenshotDir);
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true });
    }
  }

  /**
   * Process a screenshot and extract text using Mistral OCR
   * @param {string} screenshotPath - Path to the screenshot
   * @returns {Promise<string>} - Extracted text
   */
  async processScreenshot(screenshotPath) {
    try {
      console.log(`Processing screenshot: ${screenshotPath}`);
      return await this.mistralOcr.processImage(screenshotPath);
    } catch (error) {
      console.error('Error processing screenshot:', error);
      throw error;
    }
  }

  /**
   * Generate a solution for a coding problem
   * @param {string} problemText - The coding problem text
   * @param {Object} options - Options for solution generation
   * @returns {Promise<Object>} - Generated solution
   */
  async generateSolution(problemText, options = {}) {
    const {
      useLocalModel = true,
      useGroq = true,
      useBitNet = true,
      preferredModel = null,
      cacheResults = true
    } = options;
    
    // Generate cache key from problem text
    const cacheKey = this.generateCacheKey(problemText);
    const cachePath = path.join(this.cacheDir, `${cacheKey}.json`);
    
    // Check cache first
    if (cacheResults && fs.existsSync(cachePath)) {
      try {
        const cachedSolution = JSON.parse(fs.readFileSync(cachePath, 'utf8'));
        console.log('Using cached solution');
        return cachedSolution;
      } catch (error) {
        console.error('Error reading cached solution:', error);
      }
    }
    
    // Start with local model for quick response
    let quickSolution = null;
    let finalSolution = null;
    
    if (useLocalModel) {
      try {
        console.log('Generating quick solution with local model...');
        quickSolution = await this.localModel.generateQuickSolution(problemText);
        
        // Emit event for quick solution
        this.emitSolutionEvent('quick-solution-ready', quickSolution);
      } catch (error) {
        console.error('Error generating quick solution:', error);
      }
    }
    
    // Use preferred model if specified
    if (preferredModel) {
      if (preferredModel === 'groq' && useGroq) {
        finalSolution = await this.groqService.generateSolution(problemText);
      } else if (preferredModel === 'bitnet' && useBitNet) {
        finalSolution = await this.lightningBitNet.generateSolution(problemText);
      }
    } else {
      // Try Groq first (higher quality)
      if (useGroq) {
        try {
          console.log('Generating solution with Groq...');
          finalSolution = await this.groqService.generateSolution(problemText);
        } catch (error) {
          console.error('Error generating solution with Groq:', error);
          
          // Fall back to BitNet if Groq fails
          if (useBitNet) {
            try {
              console.log('Falling back to BitNet...');
              finalSolution = await this.lightningBitNet.generateSolution(problemText);
            } catch (bitNetError) {
              console.error('Error generating solution with BitNet:', bitNetError);
              
              // If both fail, use the quick solution if available
              finalSolution = quickSolution || {
                error: 'All solution generation services failed',
                analysis: 'Unable to generate solution with available services',
                approach: 'Please check your internet connection and API keys',
                code: '# Error generating solution',
                time_complexity: 'N/A',
                space_complexity: 'N/A'
              };
            }
          }
        }
      } else if (useBitNet) {
        // Use BitNet if Groq is disabled
        try {
          console.log('Generating solution with BitNet...');
          finalSolution = await this.lightningBitNet.generateSolution(problemText);
        } catch (error) {
          console.error('Error generating solution with BitNet:', error);
          finalSolution = quickSolution;
        }
      } else {
        // Use quick solution if both Groq and BitNet are disabled
        finalSolution = quickSolution;
      }
    }
    
    // Cache the final solution
    if (cacheResults && finalSolution) {
      try {
        fs.writeFileSync(cachePath, JSON.stringify(finalSolution, null, 2));
        console.log(`Solution cached at ${cachePath}`);
      } catch (error) {
        console.error('Error caching solution:', error);
      }
    }
    
    return finalSolution;
  }

  /**
   * Generate a cache key from problem text
   * @param {string} text - Problem text
   * @returns {string} - Cache key
   */
  generateCacheKey(text) {
    // Create a simple hash from the text
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return `solution_${Math.abs(hash).toString(16)}`;
  }

  /**
   * Emit solution event (to be implemented by the caller)
   * @param {string} eventName - Event name
   * @param {Object} solution - Solution object
   */
  emitSolutionEvent(eventName, solution) {
    // This method should be overridden by the caller
    console.log(`Event: ${eventName}`);
  }

  /**
   * Switch Groq model
   * @param {string} modelName - Name of the model to switch to
   */
  switchGroqModel(modelName) {
    this.groqService.switchToModel(modelName);
  }

  /**
   * Switch to next Groq model in rotation
   */
  rotateGroqModel() {
    this.groqService.switchModel();
  }

  /**
   * Switch to next Groq account in rotation
   */
  rotateGroqAccount() {
    this.groqService.switchAccount();
  }
}

module.exports = ServiceManager;
