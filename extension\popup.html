<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>NeuralCodeAssist</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;600&family=Orbitron:wght@500&display=swap">
  <style>
    body {
      width: 320px;
      font-family: 'Rajdhani', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #0a0e19;
      color: #e0f7ff;
    }
    
    .container {
      padding: 15px;
    }
    
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(64, 220, 255, 0.3);
    }
    
    .logo {
      font-family: 'Orbitron', sans-serif;
      font-size: 18px;
      font-weight: 500;
      color: #40dcff;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    
    .logo span {
      color: #ff79c6;
    }
    
    .status {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 4px;
      background-color: rgba(64, 220, 255, 0.1);
      color: #40dcff;
    }
    
    .status.connected {
      background-color: rgba(80, 250, 123, 0.1);
      color: #50fa7b;
    }
    
    .status.disconnected {
      background-color: rgba(255, 85, 85, 0.1);
      color: #ff5555;
    }
    
    .section {
      margin-bottom: 15px;
    }
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #40dcff;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }
    
    .section-title::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: #40dcff;
      margin-right: 8px;
      clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    }
    
    .shortcuts {
      background-color: rgba(10, 14, 25, 0.5);
      border: 1px solid rgba(64, 220, 255, 0.3);
      border-radius: 4px;
      padding: 10px;
    }
    
    .shortcut {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .shortcut:last-child {
      margin-bottom: 0;
    }
    
    .key {
      display: inline-block;
      padding: 2px 6px;
      background-color: rgba(64, 220, 255, 0.1);
      border: 1px solid rgba(64, 220, 255, 0.3);
      border-radius: 3px;
      font-size: 12px;
      color: #40dcff;
      margin-right: 5px;
    }
    
    .models {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    
    .model-btn {
      flex: 1 0 calc(50% - 8px);
      background-color: rgba(10, 14, 25, 0.5);
      border: 1px solid rgba(64, 220, 255, 0.3);
      color: #e0f7ff;
      border-radius: 4px;
      padding: 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
    }
    
    .model-btn:hover {
      background-color: rgba(64, 220, 255, 0.1);
      border-color: rgba(64, 220, 255, 0.5);
    }
    
    .model-btn.active {
      background-color: rgba(64, 220, 255, 0.2);
      border-color: #40dcff;
      color: #40dcff;
    }
    
    .footer {
      font-size: 11px;
      text-align: center;
      color: rgba(224, 247, 255, 0.5);
      margin-top: 15px;
      padding-top: 10px;
      border-top: 1px solid rgba(64, 220, 255, 0.3);
    }
    
    /* Sci-fi decorations */
    .decoration {
      position: absolute;
      background-color: rgba(64, 220, 255, 0.3);
    }
    
    .decoration-1 {
      top: 0;
      right: 0;
      width: 30px;
      height: 2px;
      transform: translateY(15px) rotate(-45deg);
    }
    
    .decoration-2 {
      top: 0;
      left: 0;
      width: 30px;
      height: 2px;
      transform: translateY(15px) rotate(45deg);
    }
    
    .decoration-3 {
      bottom: 0;
      right: 0;
      width: 30px;
      height: 2px;
      transform: translateY(-15px) rotate(45deg);
    }
    
    .decoration-4 {
      bottom: 0;
      left: 0;
      width: 30px;
      height: 2px;
      transform: translateY(-15px) rotate(-45deg);
    }
    
    @keyframes pulse {
      0% { opacity: 0.3; }
      50% { opacity: 0.7; }
      100% { opacity: 0.3; }
    }
    
    .pulse {
      animation: pulse 2s infinite;
    }
  </style>
</head>
<body>
  <div class="decoration decoration-1"></div>
  <div class="decoration decoration-2"></div>
  <div class="decoration decoration-3"></div>
  <div class="decoration decoration-4"></div>
  
  <div class="container">
    <div class="header">
      <div class="logo">Neural<span>Code</span>Assist</div>
      <div id="connection-status" class="status disconnected">Disconnected</div>
    </div>
    
    <div class="section">
      <div class="section-title">Keyboard Shortcuts</div>
      <div class="shortcuts">
        <div class="shortcut">
          <div>Capture Problem</div>
          <div>
            <span class="key">Ctrl</span>+<span class="key">H</span>
          </div>
        </div>
        <div class="shortcut">
          <div>Toggle Overlay</div>
          <div>
            <span class="key">Ctrl</span>+<span class="key">B</span>
          </div>
        </div>
        <div class="shortcut">
          <div>Move Overlay</div>
          <div>
            <span class="key">Ctrl</span>+<span class="key">↑↓←→</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="section">
      <div class="section-title">AI Models</div>
      <div class="models">
        <button class="model-btn active" data-model="codegen">CodeGen (Local)</button>
        <button class="model-btn" data-model="llama3-8b">Llama 3 8B</button>
        <button class="model-btn" data-model="llama3-70b">Llama 3 70B</button>
        <button class="model-btn" data-model="mixtral">Mixtral 8x7B</button>
      </div>
    </div>
    
    <div class="section">
      <div class="section-title">Supported Websites</div>
      <div class="shortcuts">
        <div>• LeetCode</div>
        <div>• HackerRank</div>
        <div>• CodeChef</div>
        <div>• CodeForces</div>
        <div>• CodeSignal</div>
        <div>• TopCoder</div>
      </div>
    </div>
    
    <div class="footer">
      NeuralCodeAssist v1.0.0 • Powered by AI
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
