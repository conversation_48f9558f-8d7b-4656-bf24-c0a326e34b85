import os
import sys
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinterdnd2 import DND_FILES, TkinterDnD
import tempfile
import time
import torch
import numpy as np
import soundfile as sf
from PIL import Image, ImageTk
import webbrowser
import platform
import subprocess

# Add the repository to the path so we can import from it
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import AudioSR functions
try:
    from audiosr import build_model, super_resolution
except ImportError:
    messagebox.showerror("Error", "AudioSR module not found. Please make sure it's installed.")
    sys.exit(1)

# Import our custom optimizations
try:
    from amd_rocm_accelerator import AMDAccelerator
    from windows11_optimizations import Windows11Optimizer
    from mojo_bridge import MojoBridge
    amd_accelerator_available = True
    windows11_optimizer_available = True
    mojo_bridge_available = True
except ImportError as e:
    print(f"Warning: Some optimizations are not available: {e}")
    amd_accelerator_available = False
    windows11_optimizer_available = False
    mojo_bridge_available = False

class AudioSRApp(TkinterDnD.Tk):
    def __init__(self):
        super().__init__()

        self.title("AudioSR - Audio Super Resolution")
        self.geometry("800x600")
        self.minsize(800, 600)

        # Set app icon
        try:
            self.iconbitmap("icon.ico")
        except Exception:
            pass  # Icon not found, use default

        # Initialize model variables
        self.model = None
        self.model_name = tk.StringVar(value="basic")
        self.guidance_scale = tk.DoubleVar(value=3.5)
        self.ddim_steps = tk.IntVar(value=50)
        self.device = tk.StringVar(value="auto")
        self.processing = False
        self.input_file = None
        self.output_file = None

        # Initialize optimizations
        self.use_optimizations = tk.BooleanVar(value=True)
        self.use_amd_acceleration = tk.BooleanVar(value=True)
        self.use_mojo = tk.BooleanVar(value=True)

        # Initialize accelerators
        self.amd_accelerator = None
        self.windows_optimizer = None
        self.mojo_bridge = None

        # Initialize platform-specific optimizations
        self._initialize_optimizations()

        # Create UI
        self.create_ui()

        # Initialize model in a separate thread
        self.status_var.set("Initializing model...")
        threading.Thread(target=self.initialize_model, daemon=True).start()

    def create_ui(self):
        # Create main frame
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create title
        title_label = ttk.Label(main_frame, text="AudioSR - Audio Super Resolution", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Create drop area
        self.drop_frame = ttk.LabelFrame(main_frame, text="Drag and Drop Audio File")
        self.drop_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.drop_label = ttk.Label(self.drop_frame, text="Drop your audio file here\nor click to select", font=("Helvetica", 12))
        self.drop_label.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Configure drop area for drag and drop
        self.drop_label.drop_target_register(DND_FILES)
        self.drop_label.dnd_bind('<<Drop>>', self.on_drop)
        self.drop_label.bind("<Button-1>", self.on_click_select)

        # Create settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings")
        settings_frame.pack(fill=tk.X, pady=10)

        # Model selection
        model_frame = ttk.Frame(settings_frame)
        model_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(model_frame, text="Model:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(model_frame, text="Basic", variable=self.model_name, value="basic").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(model_frame, text="Speech", variable=self.model_name, value="speech").pack(side=tk.LEFT, padx=5)

        # Guidance scale
        guidance_frame = ttk.Frame(settings_frame)
        guidance_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(guidance_frame, text="Guidance Scale:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Scale(guidance_frame, from_=1.0, to=10.0, variable=self.guidance_scale, orient=tk.HORIZONTAL).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Label(guidance_frame, textvariable=tk.StringVar(value=lambda: f"{self.guidance_scale.get():.1f}")).pack(side=tk.LEFT, padx=5)

        # DDIM steps
        steps_frame = ttk.Frame(settings_frame)
        steps_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(steps_frame, text="DDIM Steps:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Scale(steps_frame, from_=10, to=100, variable=self.ddim_steps, orient=tk.HORIZONTAL).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Label(steps_frame, textvariable=tk.StringVar(value=lambda: str(self.ddim_steps.get()))).pack(side=tk.LEFT, padx=5)

        # Device selection
        device_frame = ttk.Frame(settings_frame)
        device_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(device_frame, text="Device:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(device_frame, text="Auto", variable=self.device, value="auto").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(device_frame, text="CPU", variable=self.device, value="cpu").pack(side=tk.LEFT, padx=5)
        if torch.cuda.is_available():
            if 'rocm' in torch.version.hip if hasattr(torch.version, 'hip') else False:
                ttk.Radiobutton(device_frame, text="AMD GPU", variable=self.device, value="cuda").pack(side=tk.LEFT, padx=5)
            else:
                ttk.Radiobutton(device_frame, text="CUDA", variable=self.device, value="cuda").pack(side=tk.LEFT, padx=5)

        # Optimizations frame
        optimizations_frame = ttk.LabelFrame(settings_frame, text="Optimizations")
        optimizations_frame.pack(fill=tk.X, padx=10, pady=5)

        # Enable optimizations
        ttk.Checkbutton(optimizations_frame, text="Enable Optimizations", variable=self.use_optimizations).pack(anchor=tk.W, padx=10, pady=2)

        # AMD acceleration
        amd_check = ttk.Checkbutton(optimizations_frame, text="AMD GPU Acceleration", variable=self.use_amd_acceleration)
        amd_check.pack(anchor=tk.W, padx=10, pady=2)
        if not amd_accelerator_available:
            amd_check.config(state=tk.DISABLED)

        # Mojo acceleration
        mojo_check = ttk.Checkbutton(optimizations_frame, text="Mojo Acceleration", variable=self.use_mojo)
        mojo_check.pack(anchor=tk.W, padx=10, pady=2)
        if not mojo_bridge_available:
            mojo_check.config(state=tk.DISABLED)

        # Process button
        self.process_button = ttk.Button(main_frame, text="Process Audio", command=self.process_audio, state=tk.DISABLED)
        self.process_button.pack(pady=10)

        # Status bar
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))

        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, anchor=tk.W)
        status_label.pack(side=tk.LEFT)

        # Progress bar
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # Footer with GitHub link
        footer_frame = ttk.Frame(main_frame)
        footer_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))

        github_link = ttk.Label(footer_frame, text="GitHub Repository", foreground="blue", cursor="hand2")
        github_link.pack(side=tk.RIGHT)
        github_link.bind("<Button-1>", lambda e: webbrowser.open("https://github.com/haoheliu/versatile_audio_super_resolution"))

    def _initialize_optimizations(self):
        """Initialize platform-specific optimizations"""
        # Initialize Windows 11 optimizer if available
        if windows11_optimizer_available:
            try:
                self.windows_optimizer = Windows11Optimizer()
                if self.windows_optimizer.is_windows11:
                    self.status_var.set("Windows 11 detected. Optimizations available.")
                    # Apply Windows 11 optimizations
                    self.windows_optimizer.optimize_audio_processing()
            except Exception as e:
                print(f"Error initializing Windows 11 optimizations: {e}")

        # Initialize AMD accelerator if available
        if amd_accelerator_available:
            try:
                self.amd_accelerator = AMDAccelerator(device=self.device.get())
                if self.amd_accelerator.is_rocm_available:
                    self.status_var.set("AMD GPU detected. ROCm acceleration available.")
            except Exception as e:
                print(f"Error initializing AMD accelerator: {e}")

        # Initialize Mojo bridge if available
        if mojo_bridge_available:
            try:
                self.mojo_bridge = MojoBridge()
                if self.mojo_bridge.mojo_available:
                    self.status_var.set("Mojo runtime detected. Acceleration available.")
                    # Compile Mojo implementation
                    self.mojo_bridge.compile_mojo()
            except Exception as e:
                print(f"Error initializing Mojo bridge: {e}")

    def initialize_model(self):
        try:
            # Build the base model
            self.model = build_model(model_name=self.model_name.get(), device=self.device.get())

            # Apply AMD optimizations if available and enabled
            if self.use_optimizations.get() and self.use_amd_acceleration.get() and self.amd_accelerator and self.amd_accelerator.is_rocm_available:
                self.model = self.amd_accelerator.optimize_model(self.model)
                self.status_var.set("Model loaded with AMD optimizations. Ready to process audio.")
            else:
                self.status_var.set("Model loaded. Ready to process audio.")

            self.process_button.config(state=tk.NORMAL)
        except Exception as e:
            self.status_var.set(f"Error loading model: {str(e)}")
            messagebox.showerror("Error", f"Failed to load model: {str(e)}")

    def on_drop(self, event):
        file_path = event.data.strip('{}')
        self.load_file(file_path)

    def on_click_select(self, event):
        file_path = filedialog.askopenfilename(
            title="Select Audio File",
            filetypes=[
                ("Audio Files", "*.wav *.mp3 *.ogg *.flac *.aac *.m4a"),
                ("All Files", "*.*")
            ]
        )
        if file_path:
            self.load_file(file_path)

    def load_file(self, file_path):
        if not os.path.isfile(file_path):
            messagebox.showerror("Error", f"File not found: {file_path}")
            return

        self.input_file = file_path
        self.drop_label.config(text=f"File loaded: {os.path.basename(file_path)}")
        self.status_var.set(f"File loaded: {os.path.basename(file_path)}")

    def process_audio(self):
        if not self.input_file:
            messagebox.showerror("Error", "No audio file selected")
            return

        if self.processing:
            return

        # Check if model needs to be reloaded
        if self.model is None or self.model_name.get() != getattr(self.model, "model_name", None) or self.device.get() != getattr(self.model, "device", None):
            self.status_var.set("Reloading model...")
            threading.Thread(target=self.initialize_model, daemon=True).start()
            return

        # Start processing
        self.processing = True
        self.process_button.config(state=tk.DISABLED)
        self.progress.start()
        self.status_var.set("Processing audio...")

        # Process in a separate thread
        threading.Thread(target=self._process_audio_thread, daemon=True).start()

    def _process_audio_thread(self):
        try:
            # Create output directory if it doesn't exist
            output_dir = os.path.join(os.path.dirname(self.input_file), "audiosr_output")
            os.makedirs(output_dir, exist_ok=True)

            # Generate output filename
            input_basename = os.path.basename(self.input_file)
            input_name, _ = os.path.splitext(input_basename)
            self.output_file = os.path.join(output_dir, f"{input_name}_SR.wav")

            # Check if optimizations are enabled
            use_optimized_path = self.use_optimizations.get()

            # Process audio with optimizations if enabled
            if use_optimized_path and self.use_mojo.get() and self.mojo_bridge and self.mojo_bridge.mojo_available:
                # Use Mojo implementation for processing
                self.status_var.set("Processing audio with Mojo acceleration...")

                # Load audio file
                audio_data, sample_rate = sf.read(self.input_file)

                # Process with Mojo bridge
                waveform, _ = self.mojo_bridge.upscale_audio(
                    audio_data,
                    sample_rate,
                    48000  # Target sample rate
                )

            elif use_optimized_path and self.use_amd_acceleration.get() and self.amd_accelerator and self.amd_accelerator.is_rocm_available:
                # Use AMD acceleration
                self.status_var.set("Processing audio with AMD acceleration...")

                # Process with standard pipeline but with AMD optimizations
                waveform = super_resolution(
                    self.model,
                    self.input_file,
                    guidance_scale=self.guidance_scale.get(),
                    ddim_steps=self.ddim_steps.get()
                )

                # Apply additional AMD-specific post-processing if needed
                if isinstance(waveform, torch.Tensor):
                    waveform = self.amd_accelerator.process_audio(waveform).cpu().numpy()
            else:
                # Use standard processing
                self.status_var.set("Processing audio...")
                waveform = super_resolution(
                    self.model,
                    self.input_file,
                    guidance_scale=self.guidance_scale.get(),
                    ddim_steps=self.ddim_steps.get()
                )

            # Save output
            sf.write(self.output_file, waveform, 48000)

            # Update UI
            self.status_var.set(f"Processing complete. Saved to: {self.output_file}")
            messagebox.showinfo("Success", f"Audio processing complete!\nSaved to: {self.output_file}")

            # Open output folder
            if platform.system() == "Windows":
                os.startfile(output_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])

        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
            messagebox.showerror("Error", f"Processing failed: {str(e)}")

        finally:
            self.processing = False
            self.process_button.config(state=tk.NORMAL)
            self.progress.stop()

if __name__ == "__main__":
    # Check if tkinterdnd2 is installed
    try:
        import tkinterdnd2
    except ImportError:
        messagebox.showerror("Error", "tkinterdnd2 module not found. Please install it with: pip install tkinterdnd2")
        sys.exit(1)

    # Check if torch is installed
    try:
        import torch
    except ImportError:
        messagebox.showerror("Error", "PyTorch not found. Please install it.")
        sys.exit(1)

    # Check for Windows 11
    is_windows11 = False
    if platform.system() == "Windows":
        try:
            version = platform.version().split('.')
            if len(version) >= 3 and int(version[2]) >= 22000:
                is_windows11 = True
                print("Windows 11 detected. Platform-specific optimizations will be enabled.")
        except Exception:
            pass

    # Check for AMD GPU
    has_amd_gpu = False
    if torch.cuda.is_available():
        try:
            if hasattr(torch.version, 'hip') and 'rocm' in torch.version.hip:
                has_amd_gpu = True
                print("AMD GPU with ROCm detected. Hardware acceleration will be enabled.")
        except Exception:
            pass

    app = AudioSRApp()
    app.mainloop()
