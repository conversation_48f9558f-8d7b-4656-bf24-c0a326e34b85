@echo off
echo Starting Audio Upscaler...

REM Set Python path
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Check if required packages are installed
echo Checking required packages...
"%PYTHON_PATH%\python.exe" -c "try: import numpy, soundfile, scipy, tkinter; print('All required packages are installed.'); exit(0); except ImportError as e: print(f'Missing package: {e}'); exit(1)"

if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    "%PYTHON_PATH%\python.exe" -m pip install numpy soundfile scipy
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install required packages.
        echo Please install them manually:
        echo pip install numpy soundfile scipy
        pause
        exit /b 1
    )
)

REM Run the GUI
echo Starting Audio Upscaler GUI...
"%PYTHON_PATH%\python.exe" gui\main_gui.py

echo.
echo Audio Upscaler closed.
pause
