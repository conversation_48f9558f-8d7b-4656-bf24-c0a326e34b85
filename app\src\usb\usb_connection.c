#include "usb_connection.h"

#include <assert.h>
#include <string.h>
#include "util/log.h"

// Initialize USB connection
bool
sc_usb_connection_init(struct sc_usb_connection *conn) {
    assert(conn);
    
    // Initialize USB display
    if (!sc_usb_display_init(&conn->display)) {
        LOGE("Failed to initialize USB display");
        return false;
    }
    
    conn->device_name = NULL;
    conn->width = 0;
    conn->height = 0;
    conn->frame_buffer = NULL;
    conn->frame_buffer_size = 0;
    conn->connected = false;
    
    return true;
}

// Connect to device
bool
sc_usb_connection_connect(struct sc_usb_connection *conn, struct sc_intr *intr) {
    assert(conn);
    
    if (conn->connected) {
        // Already connected
        return true;
    }
    
    // Connect to USB display
    if (!sc_usb_display_connect(&conn->display, intr)) {
        LOGE("Failed to connect to USB display");
        return false;
    }
    
    // Set connection info
    conn->width = 1920;  // Default to 1080p
    conn->height = 1080;
    
    // Allocate frame buffer (assuming 32-bit RGBA)
    conn->frame_buffer_size = conn->width * conn->height * 4;
    conn->frame_buffer = malloc(conn->frame_buffer_size);
    if (!conn->frame_buffer) {
        LOGE("Failed to allocate frame buffer");
        sc_usb_connection_disconnect(conn);
        return false;
    }
    
    // Set device name
    conn->device_name = strdup("Samsung Device");
    if (!conn->device_name) {
        LOGE("Failed to allocate device name");
        sc_usb_connection_disconnect(conn);
        return false;
    }
    
    conn->connected = true;
    LOGI("Successfully connected to USB device");
    
    return true;
}

// Disconnect from device
void
sc_usb_connection_disconnect(struct sc_usb_connection *conn) {
    assert(conn);
    
    // Disconnect from USB display
    sc_usb_display_disconnect(&conn->display);
    
    // Free resources
    free(conn->frame_buffer);
    conn->frame_buffer = NULL;
    conn->frame_buffer_size = 0;
    
    free(conn->device_name);
    conn->device_name = NULL;
    
    conn->connected = false;
}

// Clean up resources
void
sc_usb_connection_destroy(struct sc_usb_connection *conn) {
    assert(conn);
    
    sc_usb_connection_disconnect(conn);
    sc_usb_display_destroy(&conn->display);
}

// Send input event
bool
sc_usb_connection_send_input(struct sc_usb_connection *conn, 
                             const unsigned char *buffer, size_t len) {
    assert(conn);
    
    if (!conn->connected) {
        LOGE("Not connected to device");
        return false;
    }
    
    return sc_usb_display_send_input(&conn->display, buffer, len);
}

// Receive frame data
ssize_t
sc_usb_connection_receive_frame(struct sc_usb_connection *conn, 
                               unsigned char *buffer, size_t len) {
    assert(conn);
    
    if (!conn->connected) {
        LOGE("Not connected to device");
        return -1;
    }
    
    // First, receive data into our internal buffer
    ssize_t received = sc_usb_display_receive(&conn->display, 
                                             conn->frame_buffer, 
                                             conn->frame_buffer_size);
    
    if (received <= 0) {
        // No data or error
        return received;
    }
    
    // Copy data to the provided buffer, up to the specified length
    size_t to_copy = received < (ssize_t)len ? received : len;
    memcpy(buffer, conn->frame_buffer, to_copy);
    
    return to_copy;
}

// Check if connected
bool
sc_usb_connection_is_connected(struct sc_usb_connection *conn) {
    assert(conn);
    return conn->connected;
}

// Get device name
const char *
sc_usb_connection_get_device_name(struct sc_usb_connection *conn) {
    assert(conn);
    return conn->device_name;
}

// Get display width
uint16_t
sc_usb_connection_get_width(struct sc_usb_connection *conn) {
    assert(conn);
    return conn->width;
}

// Get display height
uint16_t
sc_usb_connection_get_height(struct sc_usb_connection *conn) {
    assert(conn);
    return conn->height;
}
