"""
File Mapping for Project Reorganization

This script defines mappings between files and their target directories
to help reorganize the project structure.

Usage:
    python file_mapping.py

This will print the mapping information that can be used by the reorganization script.
"""

# File mappings for each project
FILE_MAPPINGS = {
    # Audio Processing Suite with Windows 11 Optimizations
    "audio_processing": {
        "src": [
            "windows11_optimizations.py",
            "realtime_audio_upscaler.py",
            "amd_rocm_accelerator.py",
            "mojo_audio_upscaler.mojo",
            "mojo_bridge.py",
            "process_audio_sr.py",
            "process_and_play.py",
            "process_and_play_first.py",
            "process_and_play_first_from_downloads.bat",
        ],
        "gui": [
            "audio_upscaler_gui.py",
            "audiosr_gui.py",
        ],
        "scripts": [
            "run_audio_upscaler_gui.bat",
            "run_audio_upscaler_test.bat",
            "process_dxrk_rave_sr.bat",
            "process_dxrk_rave_sr_fast.bat",
            "process_any_audio_sr.bat",
            "process_any_audio_sr.py",
            "process_flac_files.bat",
            "process_flac_files.py",
            "process_rave.bat",
            "process_rave.py",
            "process_rave_simple.bat",
            "process_rave_simple.py",
            "install_audiosr.py",
            "install_audiosr_direct.bat",
            "install_audiosr_direct.py",
            "remove_warp.bat",
        ],
        "utils": [
            "play_audio.py",
            "play_audio_from_dir.py",
            "play_audio_from_downloads.bat",
            "play_dxrk_rave.bat",
            "play_dxrk_rave_simple.bat",
            "play_first_audio.py",
            "play_first_audio_from_downloads.bat",
            "play_flac_files.bat",
            "play_flac_files.py",
            "play_rave.bat",
            "play_rave.ps1",
            "play_rave.py",
            "play_rave_simple.bat",
            "play_rave_simple.py",
            "play_rave_with_opt.bat",
            "play_rave_with_opt.py",
            "play_with_win11_opt.bat",
            "play_with_win11_opt.py",
            "fix_mono_output.py",
        ],
        "tests": [
            "test_win11.py",
            "test_win11_only.py",
            "test_audiosr.py",
            "test_audio_upscaler.py",
            "test_windows11_optimizations.py",
            "run_test.bat",
            "run_test_win11.bat",
            "run_win11_test.bat",
            "run_windows11_test.bat",
            "run_simple_win11_test.bat",
            "run_standard_test.bat",
            "win11_test_results.txt",
        ],
        "docs": [
            "README-AUDIOSR.md",
            "README_GUI.md",
        ],
    },

    # Interview Assistant
    "interview_assistant": {
        "src": [
            "main.js",
            "preload.js",
            "config.js",
            "test.js",
        ],
        "electron": [
            "main-enhanced.js",
            "preload-enhanced.js",
            "package.json",
            "package-enhanced.json",
        ],
        "docs": [
            "README-enhanced.md",
            "README.md",
        ],
    },

    # Calculator
    "calculator": {
        "src": [
            "calculator_improved.py",
        ],
        "gui": [
            "calculator_gui.py",
            "calculator_scifi_gui.py",
        ],
        "tests": [
            "calculator_test_suite.py",
        ],
    },

    # Samsung Screen Mirroring with scrcpy
    "samsung_mirroring": {
        "src": [
            "samsung_exploit.bat",
            "samsung_exploit.sh",
            "main.js",  # scrcpy integration
        ],
        "scrcpy": [
            # Files related to scrcpy integration
        ],
        "gui": [
            # GUI files for Samsung screen mirroring
        ],
        "docs": [
            "README_SAMSUNG_EXPLOIT.md",
            "README_SAMSUNG_OTG.md",
            "README_SAMSUNG_USB.md",
        ],
    },

    # Common utilities
    "common": {
        "utils": [
            "add_python_simple.bat",
            "add_python_to_path.bat",
            "add_python_to_system_path.bat",
            "check_env.bat",
            "check_env.py",
            "check_file.bat",
            "check_file.py",
            "fix_mono_output.py",
            "install_packages.bat",
            "install_pytorch.bat",
            "install_realtime_dependencies.bat",
            "run_pip.bat",
            "run_with_python312.bat",
            "run_with_scripts_path.bat",
            "setup_and_run.bat",
            "setup_audiosr.py",
            "simple_python_test.bat",
            "simple_test.py",
        ],
    },
}

def print_mapping_info():
    """Print information about the file mappings"""
    total_files = 0

    print("File Mapping Information:")
    print("========================\n")

    for project, directories in FILE_MAPPINGS.items():
        project_files = 0
        print(f"{project}:")

        for directory, files in directories.items():
            print(f"  {directory}/")
            for file in files:
                print(f"    - {file}")
                project_files += 1
            print()

        print(f"  Total files: {project_files}\n")
        total_files += project_files

    print(f"Total files to be moved: {total_files}")

if __name__ == "__main__":
    print_mapping_info()
