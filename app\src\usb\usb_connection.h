#ifndef SC_USB_CONNECTION_H
#define SC_USB_CONNECTION_H

#include <stdbool.h>
#include <stdint.h>
#include "common.h"
#include "util/intr.h"
#include "usb/usb_display.h"

// USB connection structure
struct sc_usb_connection {
    struct sc_usb_display display;
    
    // Connection info
    char *device_name;
    uint16_t width;
    uint16_t height;
    
    // Frame data
    unsigned char *frame_buffer;
    size_t frame_buffer_size;
    
    // Connection state
    bool connected;
};

// Initialize USB connection
bool
sc_usb_connection_init(struct sc_usb_connection *conn);

// Connect to device
bool
sc_usb_connection_connect(struct sc_usb_connection *conn, struct sc_intr *intr);

// Disconnect from device
void
sc_usb_connection_disconnect(struct sc_usb_connection *conn);

// Clean up resources
void
sc_usb_connection_destroy(struct sc_usb_connection *conn);

// Send input event
bool
sc_usb_connection_send_input(struct sc_usb_connection *conn, 
                             const unsigned char *buffer, size_t len);

// Receive frame data
ssize_t
sc_usb_connection_receive_frame(struct sc_usb_connection *conn, 
                               unsigned char *buffer, size_t len);

// Check if connected
bool
sc_usb_connection_is_connected(struct sc_usb_connection *conn);

// Get device name
const char *
sc_usb_connection_get_device_name(struct sc_usb_connection *conn);

// Get display width
uint16_t
sc_usb_connection_get_width(struct sc_usb_connection *conn);

// Get display height
uint16_t
sc_usb_connection_get_height(struct sc_usb_connection *conn);

#endif // SC_USB_CONNECTION_H
