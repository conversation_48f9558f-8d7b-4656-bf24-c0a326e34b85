"""
File Consolidation Script

This script consolidates multiple files into fewer, more comprehensive files
to streamline the project structure and make it more maintainable.

Usage:
    python consolidate_files.py

This will consolidate files according to the defined mappings.
"""

import os
import sys
import shutil
from datetime import datetime
from consolidated_mapping import CONSOLIDATION_MAPPINGS

# Root directory of the project
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

# File extensions and their comment styles
COMMENT_STYLES = {
    '.py': {'start': '"""', 'end': '"""', 'line': '#'},
    '.js': {'start': '/**', 'end': ' */', 'line': '//'},
    '.bat': {'start': 'REM ', 'end': '', 'line': 'REM'},
    '.sh': {'start': '# ', 'end': '', 'line': '#'},
    '.mojo': {'start': '"""', 'end': '"""', 'line': '#'},
}

# Project descriptions
PROJECT_DESCRIPTIONS = {
    'audio_processing': 'Audio Processing Suite with Windows 11 Optimizations',
    'interview_assistant': 'Interview Assistant Application',
    'calculator': 'Sci-Fi Calculator Application',
    'samsung_mirroring': 'Samsung Screen Mirroring Application with scrcpy Integration',
    'common': 'Common Utilities and Resources',
}

def create_header_comment(target_file, project, source_files):
    """Create a header comment for the consolidated file"""
    _, ext = os.path.splitext(target_file)
    
    if ext not in COMMENT_STYLES:
        return ""
    
    comment_style = COMMENT_STYLES[ext]
    
    # Create the header comment
    header = f"{comment_style['start']}\n"
    header += f"{PROJECT_DESCRIPTIONS.get(project, 'Project')} - Consolidated File\n\n"
    header += f"File: {os.path.basename(target_file)}\n"
    header += f"Part of: {project}\n\n"
    header += f"Description:\n"
    header += f"  This file is part of the {PROJECT_DESCRIPTIONS.get(project, 'Project')}.\n"
    header += f"  It consolidates functionality from multiple files to streamline the codebase.\n\n"
    header += f"Consolidated from:\n"
    
    # Add source files
    for source_file in source_files:
        header += f"  - {source_file}\n"
    
    header += f"\nCreated: {datetime.now().strftime('%Y-%m-%d')}\n"
    
    if comment_style['end']:
        header += f"{comment_style['end']}\n\n"
    else:
        header += "\n"
    
    return header

def extract_content(file_path):
    """Extract the content from a file, skipping any existing header comments"""
    _, ext = os.path.splitext(file_path)
    
    if not os.path.exists(file_path):
        print(f"Warning: Source file not found: {file_path}")
        return ""
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return ""
    
    # Skip existing header comments
    if ext in COMMENT_STYLES:
        comment_style = COMMENT_STYLES[ext]
        if comment_style['start'] and comment_style['end'] and comment_style['start'] in content and comment_style['end'] in content:
            start_idx = content.find(comment_style['start'])
            end_idx = content.find(comment_style['end'], start_idx) + len(comment_style['end'])
            if start_idx == 0:  # Only skip if the comment is at the beginning
                content = content[end_idx:].lstrip()
        elif comment_style['line']:
            # Skip consecutive line comments at the beginning
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if not line.strip().startswith(comment_style['line']):
                    content = '\n'.join(lines[i:])
                    break
    
    return content

def consolidate_files(project, target_file, source_files):
    """Consolidate multiple source files into a single target file"""
    # Create the target directory if it doesn't exist
    target_dir = os.path.join(ROOT_DIR, project, 'src')
    os.makedirs(target_dir, exist_ok=True)
    
    target_path = os.path.join(target_dir, target_file)
    
    # Create the header comment
    header = create_header_comment(target_file, project, source_files)
    
    # Consolidate the content from source files
    consolidated_content = header
    
    for source_file in source_files:
        source_path = os.path.join(ROOT_DIR, source_file)
        
        # Extract content from the source file
        content = extract_content(source_path)
        
        if content:
            _, ext = os.path.splitext(target_file)
            comment_style = COMMENT_STYLES.get(ext, {'line': '#'})
            
            # Add a section header for this source file
            consolidated_content += f"\n{comment_style['line']} ===== From {source_file} =====\n\n"
            consolidated_content += content
            consolidated_content += "\n\n"
    
    # Write the consolidated content to the target file
    with open(target_path, 'w', encoding='utf-8') as f:
        f.write(consolidated_content)
    
    print(f"Created consolidated file: {target_path}")
    return True

def copy_separate_files(project, files):
    """Copy files that should be kept separate to their target directories"""
    for file in files:
        source_path = os.path.join(ROOT_DIR, file)
        
        # Determine the appropriate target directory based on file type
        if file.endswith('.py'):
            if 'gui' in file:
                target_dir = os.path.join(ROOT_DIR, project, 'gui')
            elif 'test' in file:
                target_dir = os.path.join(ROOT_DIR, project, 'tests')
            else:
                target_dir = os.path.join(ROOT_DIR, project, 'src')
        elif file.endswith('.js'):
            target_dir = os.path.join(ROOT_DIR, project, 'src')
        elif file.endswith('.bat') or file.endswith('.sh'):
            target_dir = os.path.join(ROOT_DIR, project, 'scripts')
        elif file.endswith('.mojo'):
            target_dir = os.path.join(ROOT_DIR, project, 'src')
        else:
            target_dir = os.path.join(ROOT_DIR, project, 'src')
        
        # Create the target directory if it doesn't exist
        os.makedirs(target_dir, exist_ok=True)
        
        # Copy the file to the target directory
        target_path = os.path.join(target_dir, os.path.basename(file))
        
        if os.path.exists(source_path):
            shutil.copy2(source_path, target_path)
            print(f"Copied file: {source_path} -> {target_path}")
        else:
            print(f"Warning: Source file not found: {source_path}")

def process_consolidation():
    """Process the consolidation mappings"""
    consolidated_files = 0
    separate_files = 0
    
    print("Starting file consolidation...")
    
    for project, consolidations in CONSOLIDATION_MAPPINGS.items():
        print(f"\nProcessing {project}...")
        
        for target_file, source_files in consolidations.items():
            if target_file == "keep_separate":
                # Copy files that should be kept separate
                copy_separate_files(project, source_files)
                separate_files += len(source_files)
            else:
                # Consolidate files
                if consolidate_files(project, target_file, source_files):
                    consolidated_files += len(source_files)
    
    print(f"\nConsolidation complete!")
    print(f"Files consolidated: {consolidated_files}")
    print(f"Files kept separate: {separate_files}")

if __name__ == "__main__":
    # Confirm before proceeding
    print("This script will consolidate files according to the defined mappings.")
    print("It will create new consolidated files in the project directories.")
    print("The original files will remain in place.")
    print("\nAre you sure you want to proceed? (y/n)")
    
    response = input().strip().lower()
    if response != 'y':
        print("Operation cancelled.")
        sys.exit(0)
    
    process_consolidation()
