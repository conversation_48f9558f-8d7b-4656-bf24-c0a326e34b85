# Audio Super Resolution with Windows 11 Optimizations

This project provides scripts to enhance audio quality using the AudioSR (Audio Super Resolution) model with Windows 11 optimizations for improved performance.

## Features

- Audio super-resolution using state-of-the-art AI models
- Windows 11 optimizations for better performance
- Support for various audio formats (WAV, MP3, FLAC, etc.)
- Adjustable settings for guidance scale and DDIM steps
- Choice between "basic" and "speech" models

## Requirements

- Python 3.8 or higher
- Windows 11 (for optimizations)
- Internet connection (for model download)

## Installation

1. Make sure Python 3.8+ is installed and added to your PATH
2. Run the installation script:
   ```
   python install_audiosr.py
   ```

This will install AudioSR and all required dependencies.

## Usage

### Process a Specific Audio File

```
python process_audio_sr.py "path/to/audio/file.mp3" --model basic --device cpu --guidance-scale 3.5 --ddim-steps 50 --play-processed
```

Options:
- `--model`: Choose between 'basic' or 'speech' models
- `--device`: 'cpu' or 'cuda' (if you have a compatible GPU)
- `--guidance-scale`: Controls the strength of the enhancement (default: 3.5)
- `--ddim-steps`: Number of denoising steps (higher = better quality but slower, default: 50)
- `--play-original`: Play the original audio before processing
- `--play-processed`: Play the processed audio after processing
- `--no-win11-opt`: Disable Windows 11 optimizations

### Process Any Audio File from Downloads Folder

```
python process_any_audio_sr.py
```

This will list all audio files in your Downloads folder and let you choose which one to process.

### Batch Files

For convenience, several batch files are provided:

- `process_dxrk_rave_sr.bat`: Process the "Dxrk ダーク - RAVE.flac" file with standard settings
- `process_dxrk_rave_sr_fast.bat`: Process the same file with faster settings (fewer DDIM steps)
- `process_any_audio_sr.bat`: Choose any audio file from your Downloads folder to process

## Windows 11 Optimizations

The scripts automatically apply the following Windows 11 optimizations:

- High process priority
- Thread priority optimization
- Performance power mode

These optimizations help improve processing speed and audio quality.

## How It Works

AudioSR uses a diffusion-based model to enhance audio quality. The process involves:

1. Loading the audio file
2. Applying Windows 11 optimizations
3. Building the AI model
4. Processing the audio through the model
5. Saving the enhanced audio as a WAV file

The enhanced audio will have improved clarity, detail, and frequency response.

## Troubleshooting

If you encounter issues:

1. Make sure Python 3.8+ is installed and in your PATH
2. Try running `python install_audiosr.py` to reinstall dependencies
3. For GPU acceleration, ensure you have a compatible GPU with CUDA support
4. If processing is too slow, try reducing the `--ddim-steps` parameter

## Credits

- [AudioSR](https://github.com/haoheliu/versatile_audio_super_resolution) by Haohe Liu
- Windows 11 optimizations by [Your Name]
