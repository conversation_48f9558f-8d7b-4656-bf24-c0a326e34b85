const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const config = require('../config');

class LocalModel {
  constructor() {
    this.modelPath = config.localModel.path;
    this.modelType = config.localModel.modelType;
    this.contextSize = config.localModel.contextSize;
    this.temperature = config.localModel.temperature;
    
    // Check if model file exists
    if (!fs.existsSync(this.modelPath)) {
      console.warn(`Local model file not found at ${this.modelPath}. Local model will not be available.`);
      this.isAvailable = false;
    } else {
      this.isAvailable = true;
    }
    
    // Path to the Python script for local inference
    this.scriptPath = path.join(__dirname, '../python/local_inference.py');
    
    // Create the Python script if it doesn't exist
    this.ensureInferenceScript();
  }

  /**
   * Create the Python script for local model inference if it doesn't exist
   */
  ensureInferenceScript() {
    if (!fs.existsSync(this.scriptPath)) {
      const scriptDir = path.dirname(this.scriptPath);
      if (!fs.existsSync(scriptDir)) {
        fs.mkdirSync(scriptDir, { recursive: true });
      }
      
      const scriptContent = `
import sys
import json
import argparse
from llama_cpp import Llama

def main():
    parser = argparse.ArgumentParser(description='Run inference with local LLM')
    parser.add_argument('--model', type=str, required=True, help='Path to model file')
    parser.add_argument('--prompt', type=str, required=True, help='Prompt for generation')
    parser.add_argument('--max_tokens', type=int, default=512, help='Maximum tokens to generate')
    parser.add_argument('--temperature', type=float, default=0.7, help='Temperature for sampling')
    parser.add_argument('--context_size', type=int, default=2048, help='Context window size')
    
    args = parser.parse_args()
    
    try:
        # Load the model
        llm = Llama(
            model_path=args.model,
            n_ctx=args.context_size,
            n_threads=4,  # Adjust based on your CPU
            n_gpu_layers=0  # Set to higher value if using GPU
        )
        
        # Generate response
        output = llm(
            args.prompt,
            max_tokens=args.max_tokens,
            temperature=args.temperature,
            stop=["</s>", "User:", "System:"],  # Common stop tokens
            echo=False
        )
        
        # Extract and format the response
        if 'choices' in output and len(output['choices']) > 0:
            response = output['choices'][0]['text'].strip()
        else:
            response = output.get('generation', '').strip()
        
        # Parse the response into structured format
        result = parse_response(response)
        
        # Output as JSON
        print(json.dumps(result))
        
    except Exception as e:
        error_result = {
            "error": str(e),
            "analysis": "Error generating solution with local model",
            "approach": "Please try again or use online models",
            "code": "# Error occurred during local inference",
            "time_complexity": "N/A",
            "space_complexity": "N/A"
        }
        print(json.dumps(error_result))
        sys.exit(1)

def parse_response(text):
    # Initialize solution object
    solution = {
        "analysis": "",
        "approach": "",
        "code": "",
        "time_complexity": "",
        "space_complexity": ""
    }
    
    # Extract sections using simple text parsing
    sections = text.split("\\n\\n")
    
    for i, section in enumerate(sections):
        if i == 0 or "analysis" in section.lower() or "problem" in section.lower():
            solution["analysis"] += section.strip() + "\\n"
        elif "approach" in section.lower() or "solution" in section.lower():
            solution["approach"] += section.strip() + "\\n"
        elif "code" in section.lower() or "```" in section:
            solution["code"] += section.strip() + "\\n"
        elif "time" in section.lower() and "complex" in section.lower():
            solution["time_complexity"] += section.strip() + "\\n"
        elif "space" in section.lower() and "complex" in section.lower():
            solution["space_complexity"] += section.strip() + "\\n"
    
    # Clean up and ensure code is properly formatted
    if "```" not in solution["code"] and solution["code"].strip():
        solution["code"] = "```python\\n" + solution["code"] + "\\n```"
    
    return solution

if __name__ == "__main__":
    main()
`;
      
      fs.writeFileSync(this.scriptPath, scriptContent);
      console.log(`Created local inference script at ${this.scriptPath}`);
    }
  }

  /**
   * Generate a quick solution using the local model
   * @param {string} problemText - The coding problem text
   * @returns {Promise<Object>} - Generated solution
   */
  async generateQuickSolution(problemText) {
    if (!this.isAvailable) {
      return {
        error: 'Local model not available',
        analysis: 'Local model file not found. Please check configuration.',
        approach: 'Using online models instead...',
        code: '# Local model not available',
        time_complexity: 'N/A',
        space_complexity: 'N/A'
      };
    }
    
    return new Promise((resolve, reject) => {
      const prompt = `
You are an expert coding assistant. Analyze this problem and provide a solution:

${problemText}

Provide your response in the following format:
1. Problem Analysis
2. Approach
3. Code Solution (with comments)
4. Time Complexity
5. Space Complexity
`;
      
      const pythonProcess = spawn('python', [
        this.scriptPath,
        '--model', this.modelPath,
        '--prompt', prompt,
        '--max_tokens', '512',
        '--temperature', this.temperature.toString(),
        '--context_size', this.contextSize.toString()
      ]);
      
      let output = '';
      let errorOutput = '';
      
      pythonProcess.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      pythonProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        console.error(`Local model error: ${data}`);
      });
      
      pythonProcess.on('close', (code) => {
        if (code !== 0) {
          console.error(`Local model process exited with code ${code}`);
          console.error(`Error output: ${errorOutput}`);
          
          // Return a fallback solution
          resolve({
            error: `Process exited with code ${code}`,
            analysis: 'Error generating solution with local model',
            approach: 'Please wait for online model response...',
            code: '# Error occurred during local inference',
            time_complexity: 'N/A',
            space_complexity: 'N/A'
          });
        } else {
          try {
            const result = JSON.parse(output);
            resolve(result);
          } catch (e) {
            console.error('Error parsing local model output:', e);
            console.error('Raw output:', output);
            
            // Return a fallback solution with the raw output
            resolve({
              error: 'Error parsing output',
              analysis: 'Error parsing local model output',
              approach: 'Please wait for online model response...',
              code: `# Raw output from local model:\n${output}`,
              time_complexity: 'N/A',
              space_complexity: 'N/A'
            });
          }
        }
      });
    });
  }
}

module.exports = LocalModel;
