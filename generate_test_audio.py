"""
Generate a test audio file with a sine wave
"""

import numpy as np
import wave
import struct
import os

def generate_sine_wave(frequency=440, duration=5, sample_rate=44100, amplitude=0.5):
    """Generate a sine wave with the given parameters"""
    # Generate time array
    t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
    
    # Generate sine wave
    sine_wave = amplitude * np.sin(2 * np.pi * frequency * t)
    
    return sine_wave, sample_rate

def save_wave_file(filename, samples, sample_rate):
    """Save the samples as a WAV file"""
    # Convert floating point samples to 16-bit integers
    samples = (samples * 32767).astype(np.int16)
    
    # Create the WAV file
    with wave.open(filename, 'w') as wav_file:
        # Set parameters
        n_channels = 1  # Mono
        sample_width = 2  # 2 bytes (16 bits) per sample
        
        wav_file.setparams((n_channels, sample_width, sample_rate, len(samples), 'NONE', 'not compressed'))
        
        # Write the samples
        for sample in samples:
            wav_file.writeframes(struct.pack('h', sample))
    
    return os.path.abspath(filename)

def main():
    # Generate a sine wave
    print("Generating test audio file...")
    sine_wave, sample_rate = generate_sine_wave(frequency=440, duration=5, sample_rate=44100, amplitude=0.5)
    
    # Save the sine wave as a WAV file
    output_file = "test_sine_wave.wav"
    file_path = save_wave_file(output_file, sine_wave, sample_rate)
    
    print(f"Test audio file generated: {file_path}")
    print(f"Frequency: 440 Hz, Duration: 5 seconds, Sample Rate: 44100 Hz")
    
    return 0

if __name__ == "__main__":
    main()
