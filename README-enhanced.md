# Interview Assistant (Enhanced)

A desktop application designed to assist candidates during technical coding interviews by providing real-time AI-generated solutions while remaining undetectable.

## Features

### Core Functionality
- **Screenshot-to-Solution Generation**: Capture coding problems via ⌘+H (Mac) or Ctrl+H (Windows) screenshot commands, with AI analyzing screenshots to generate solutions featuring commented code and step-by-step reasoning.
- **Real-Time Debugging**: Use ⌘+H/Ctrl+H on existing code to receive AI optimizations, including before/after comparisons and time/space complexity analysis.
- **Multi-Platform Compatibility**: Works seamlessly with Zoom, HackerRank, CoderPad, CodeSignal, and other major interview platforms.

### Undetectability Features
- **Invisible Overlay**: The solution window remains hidden from screen-sharing software and interviewers, appearing only as a translucent overlay on the user's local screen.
- **Cursor-Focused Toggling**: Toggle solution visibility with ⌘+B/Ctrl+B without changing active tabs or cursor position, avoiding suspicion during webcam monitoring.
- **Natural Eye Movement**: Solutions can be positioned over the coding area using arrow keys to maintain eye contact with the screen.

### AI Integration
- **Mistral OCR API**: Advanced OCR for accurate text extraction from screenshots
- **Groq Cloud Integration**: High-quality solutions using Llama 3 and Mixtral models
- **BitNet 1.58 Model**: Hosted on Lightning AI for efficient solution generation
- **Local 300MB Model**: Quick reference solutions while waiting for cloud API responses
- **Model Switching**: Switch between different models with ⌘+M/Ctrl+M
- **Account Rotation**: Automatically rotate between Groq accounts to stay within free tier limits

### User Experience
- **Pre-Built Explanations**: Solutions include concise "thoughts" for verbalizing reasoning and line-by-line code comments to justify implementation choices.
- **Quick & Final Solutions**: Get immediate feedback from local model while waiting for more comprehensive cloud-generated solutions.

## Installation

### Prerequisites
- Node.js (v14 or later)
- Python 3.7 or later
- API keys for:
  - Mistral AI (OCR)
  - Groq Cloud
  - Lightning AI

### Setup
1. Clone the repository
2. Install Node.js dependencies:
   ```
   npm install
   ```
3. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```
4. Download a small local model (e.g., TinyLlama or Phi-2) and place it in the `models` directory
5. Configure API keys in `config.js` or set them as environment variables:
   ```
   MISTRAL_API_KEY=your_mistral_api_key
   GROQ_API_KEY=your_groq_api_key
   LIGHTNING_API_KEY=your_lightning_api_key
   ```

### Running the Application
```
npm start
```

## Usage

1. Start the application
2. Navigate to your coding interview platform
3. When you encounter a problem:
   - Press ⌘+H/Ctrl+H to capture the problem and generate a solution
   - Press ⌘+B/Ctrl+B to toggle the solution overlay visibility
   - Use ⌘+Arrow Keys/Ctrl+Arrow Keys to position the overlay
   - Press ⌘+M/Ctrl+M to switch between different Groq models
   - Press ⌘+A/Ctrl+A to switch between different Groq accounts

## Building for Distribution

```
npm run build
```

This will create platform-specific installers in the `dist` directory.

## Directory Structure

```
interview-assistant/
├── main-enhanced.js         # Main Electron process
├── preload-enhanced.js      # Secure preload script
├── config.js                # Configuration and API keys
├── src/                     # Frontend files
│   ├── index.html           # Main application window
│   ├── overlay-enhanced.html # Solution overlay window
│   ├── styles.css           # Main window styling
│   ├── overlay-enhanced.css # Overlay window styling
│   ├── renderer.js          # Main window logic
│   └── overlay-enhanced.js  # Overlay window logic
├── services/                # Backend services
│   ├── service-manager.js   # Service orchestration
│   ├── mistral-ocr.js       # Mistral OCR integration
│   ├── groq-service.js      # Groq API integration
│   ├── lightning-bitnet.js  # Lightning AI BitNet integration
│   └── local-model.js       # Local model integration
├── python/                  # Python scripts
│   ├── local_inference.py   # Local model inference
│   └── ocr_engine.py        # OCR processing (fallback)
├── models/                  # Local model files
│   └── tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf  # Example local model
├── screenshots/             # Directory for saved screenshots
└── cache/                   # Solution cache directory
```

## License

MIT
