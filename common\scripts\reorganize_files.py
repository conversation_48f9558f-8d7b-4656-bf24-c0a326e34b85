"""
Project Reorganization Script

This script reorganizes the project files according to the defined mappings.
It moves files to their appropriate directories and adds header comments
to make the codebase more maintainable and easier for AI to understand.

Usage:
    python reorganize_files.py

This will move files to their target directories and add header comments.
"""

import os
import shutil
import sys
from datetime import datetime
from file_mapping import FILE_MAPPINGS

# Root directory of the project
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

# File extensions that should receive header comments
COMMENT_EXTENSIONS = {
    '.py': {'start': '"""', 'end': '"""'},
    '.js': {'start': '/**', 'end': ' */'},
    '.bat': {'start': 'REM ', 'end': ''},
    '.sh': {'start': '# ', 'end': ''},
    '.mojo': {'start': '"""', 'end': '"""'},
}

# Project descriptions
PROJECT_DESCRIPTIONS = {
    'audio_processing': 'Audio Processing Suite with Windows 11 Optimizations',
    'interview_assistant': 'Interview Assistant Application',
    'calculator': 'Sci-Fi Calculator Application',
    'samsung_mirroring': 'Samsung Screen Mirroring Application with scrcpy Integration',
    'common': 'Common Utilities and Resources',
}

def add_header_comment(file_path, project, directory):
    """Add a header comment to the file"""
    _, ext = os.path.splitext(file_path)

    if ext not in COMMENT_EXTENSIONS:
        return

    comment_style = COMMENT_EXTENSIONS[ext]

    # Read the file content
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Create the header comment
    header = f"{comment_style['start']}\n"
    header += f"{PROJECT_DESCRIPTIONS.get(project, 'Project')} - {directory.capitalize()}\n\n"
    header += f"File: {os.path.basename(file_path)}\n"
    header += f"Part of: {project}\n"
    header += f"Directory: {directory}\n\n"
    header += f"Description:\n"
    header += f"  This file is part of the {PROJECT_DESCRIPTIONS.get(project, 'Project')}.\n"
    header += f"  It belongs to the {directory} component of the project.\n\n"
    header += f"Related Files:\n"

    # Add related files from the same directory
    related_files = FILE_MAPPINGS.get(project, {}).get(directory, [])
    for related_file in related_files[:5]:  # Limit to 5 related files
        if related_file != os.path.basename(file_path):
            header += f"  - {related_file}\n"

    header += f"\nLast Modified: {datetime.now().strftime('%Y-%m-%d')}\n"

    if comment_style['end']:
        header += f"{comment_style['end']}\n\n"
    else:
        header += "\n"

    # Check if the file already has a similar header
    if content.startswith(comment_style['start']) and "Part of:" in content[:500]:
        # Skip adding header if it already exists
        return

    # Write the header and content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(header + content)

def move_file(source, destination, project, directory):
    """Move a file to its destination directory and add a header comment"""
    # Create the destination directory if it doesn't exist
    os.makedirs(os.path.dirname(destination), exist_ok=True)

    # Check if the source file exists
    if not os.path.exists(source):
        print(f"Warning: Source file not found: {source}")
        return False

    # Check if the destination file already exists
    if os.path.exists(destination):
        print(f"Warning: Destination file already exists: {destination}")
        # Don't overwrite existing files
        return False

    # Copy the file to the destination
    shutil.copy2(source, destination)

    # Add a header comment to the file
    add_header_comment(destination, project, directory)

    print(f"Moved: {source} -> {destination}")
    return True

def reorganize_files():
    """Reorganize files according to the defined mappings"""
    moved_files = 0
    skipped_files = 0

    print("Starting file reorganization...")

    for project, directories in FILE_MAPPINGS.items():
        for directory, files in directories.items():
            for file in files:
                source = os.path.join(ROOT_DIR, file)
                destination = os.path.join(ROOT_DIR, project, directory, file)

                if move_file(source, destination, project, directory):
                    moved_files += 1
                else:
                    skipped_files += 1

    print(f"\nReorganization complete!")
    print(f"Files moved: {moved_files}")
    print(f"Files skipped: {skipped_files}")

if __name__ == "__main__":
    # Confirm before proceeding
    print("This script will reorganize files according to the defined mappings.")
    print("It will copy files to their target directories and add header comments.")
    print("The original files will remain in place.")
    print("\nAre you sure you want to proceed? (y/n)")

    response = input().strip().lower()
    if response != 'y':
        print("Operation cancelled.")
        sys.exit(0)

    reorganize_files()
