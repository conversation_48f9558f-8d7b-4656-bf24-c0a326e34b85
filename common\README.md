# Common Utilities and Resources

This directory contains shared utilities and resources used across multiple projects.

## Directory Structure

- **utils/**: Shared utility functions
  - System utilities
  - File handling
  - Common algorithms

- **scripts/**: Shared scripts
  - Environment setup
  - Installation helpers
  - Build utilities

- **docs/**: Common documentation
  - Project overview
  - Development guidelines
  - Common troubleshooting

## Purpose

The common directory provides a centralized location for code and resources that are used by multiple projects. This helps avoid duplication and ensures consistency across the codebase.

## Usage

To use these common utilities in your project, import them directly from this directory. For example:

```python
from common.utils.system import get_system_info
```

## Contributing

When adding new utilities to this directory, ensure they are truly common across multiple projects. Project-specific code should remain in the respective project directories.
