"""
Audio Upscaler GUI with Windows 11 and Hardware Optimizations
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import soundfile as sf
import platform
import logging
from pathlib import Path
import matplotlib.pyplot as plt

# Setup logging
LOG_FILE = Path(__file__).parent / "audio_upscaler.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_FILE)
    ]
)
logger = logging.getLogger("AudioUpscaler")

class AudioUpscalerGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        
        self.title("Audio Upscaler with Windows 11 Optimizations")
        self.geometry("800x600")
        self.minsize(800, 600)
        
        # Initialize variables
        self.input_file = None
        self.output_file = None
        self.processing = False
        self.windows_optimizer = None
        
        # Initialize optimization settings
        self.use_win11_opt = tk.BooleanVar(value=True)
        self.use_hardware_accel = tk.BooleanVar(value=True)
        self.upscale_factor = tk.IntVar(value=2)
        self.quality_level = tk.IntVar(value=2)  # 1=Low, 2=Medium, 3=High
        
        # Apply Windows 11 optimizations if available
        self._initialize_optimizations()
        
        # Create UI
        self._create_ui()
        self._create_menu()
    
    def _initialize_optimizations(self):
        """Initialize Windows 11 optimizations"""
        try:
            from windows11_optimizations import Windows11Optimizer
            self.windows_optimizer = Windows11Optimizer()
            
            if self.windows_optimizer.is_windows11:
                self.status_var.set("Windows 11 detected. Optimizations available.")
            else:
                self.status_var.set("Not running on Windows 11. Some optimizations may not be available.")
        except ImportError:
            self.status_var.set("Windows 11 optimizations not available.")
            self.windows_optimizer = None
    
    def _create_ui(self):
        """Create the user interface"""
        # Create main frame
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create title
        title_label = ttk.Label(main_frame, text="Audio Upscaler with Windows 11 Optimizations", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Create file selection frame
        file_frame = ttk.LabelFrame(main_frame, text="File Selection")
        file_frame.pack(fill=tk.X, pady=10)
        
        # Input file selection
        input_frame = ttk.Frame(file_frame)
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="Input File:").pack(side=tk.LEFT, padx=(0, 10))
        self.input_entry = ttk.Entry(input_frame)
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(input_frame, text="Browse", command=self._browse_input).pack(side=tk.LEFT, padx=5)
        
        # Output file selection
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(output_frame, text="Output File:").pack(side=tk.LEFT, padx=(0, 10))
        self.output_entry = ttk.Entry(output_frame)
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(output_frame, text="Browse", command=self._browse_output).pack(side=tk.LEFT, padx=5)
        
        # Create settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings")
        settings_frame.pack(fill=tk.X, pady=10)
        
        # Optimization settings
        opt_frame = ttk.Frame(settings_frame)
        opt_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Checkbutton(opt_frame, text="Use Windows 11 Optimizations", variable=self.use_win11_opt).pack(anchor=tk.W, padx=10, pady=2)
        ttk.Checkbutton(opt_frame, text="Use Hardware Acceleration", variable=self.use_hardware_accel).pack(anchor=tk.W, padx=10, pady=2)
        
        # Upscale factor
        factor_frame = ttk.Frame(settings_frame)
        factor_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(factor_frame, text="Upscale Factor:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(factor_frame, text="2x", variable=self.upscale_factor, value=2).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(factor_frame, text="4x", variable=self.upscale_factor, value=4).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(factor_frame, text="8x", variable=self.upscale_factor, value=8).pack(side=tk.LEFT, padx=5)
        
        # Quality level
        quality_frame = ttk.Frame(settings_frame)
        quality_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(quality_frame, text="Quality Level:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(quality_frame, text="Low (Faster)", variable=self.quality_level, value=1).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(quality_frame, text="Medium", variable=self.quality_level, value=2).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(quality_frame, text="High (Slower)", variable=self.quality_level, value=3).pack(side=tk.LEFT, padx=5)
        
        # Create process button
        self.process_button = ttk.Button(main_frame, text="Process Audio", command=self._process_audio)
        self.process_button.pack(pady=10)
        
        # Create log frame
        log_frame = ttk.LabelFrame(main_frame, text="Log")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create log text widget
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create scrollbar for log
        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # Status bar
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))
        
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, anchor=tk.W)
        status_label.pack(side=tk.LEFT)
        
        # Progress bar
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
    
    def _create_menu(self):
        menubar = tk.Menu(self)
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Open File", command=self._browse_input)
        file_menu.add_command(label="Play Normally", command=self._play_file_normally)
        file_menu.add_command(label="Upscale & Download", command=self._process_audio)
        file_menu.add_command(label="Compare Files", command=self._compare_files)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_close)
        menubar.add_cascade(label="File", menu=file_menu)
        self.config(menu=menubar)

    def _log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        logger.info(message)
    
    def _browse_input(self):
        """Browse for input file"""
        file_path = filedialog.askopenfilename(
            title="Select Audio File",
            filetypes=[
                ("Audio Files", "*.wav *.mp3 *.ogg *.flac *.aac *.m4a"),
                ("All Files", "*.*")
            ]
        )
        if file_path:
            self.input_file = file_path
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, file_path)
            
            # Generate default output path
            if not self.output_entry.get():
                input_dir = os.path.dirname(file_path)
                input_basename = os.path.basename(file_path)
                input_name, ext = os.path.splitext(input_basename)
                output_dir = os.path.join(input_dir, "upscaled")
                os.makedirs(output_dir, exist_ok=True)
                output_file = os.path.join(output_dir, f"{input_name}_upscaled.wav")
                
                self.output_file = output_file
                self.output_entry.delete(0, tk.END)
                self.output_entry.insert(0, output_file)
    
    def _browse_output(self):
        """Browse for output file"""
        file_path = filedialog.asksaveasfilename(
            title="Save Output File",
            defaultextension=".wav",
            filetypes=[
                ("WAV Files", "*.wav"),
                ("All Files", "*.*")
            ]
        )
        if file_path:
            self.output_file = file_path
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, file_path)
    
    def _process_audio(self):
        """Process the audio file"""
        if not self.input_file:
            messagebox.showerror("Error", "No input file selected")
            return
        
        if not self.output_file:
            messagebox.showerror("Error", "No output file specified")
            return
        
        if self.processing:
            return
        
        # Start processing
        self.processing = True
        self.process_button.config(state=tk.DISABLED)
        self.progress.start()
        self.status_var.set("Processing audio...")
        
        # Process in a separate thread
        threading.Thread(target=self._process_audio_thread, daemon=True).start()
    
    def _process_audio_thread(self):
        """Thread for processing audio"""
        try:
            # Apply Windows 11 optimizations if enabled
            if self.use_win11_opt.get() and self.windows_optimizer:
                self._log("Applying Windows 11 optimizations...")
                optimizations = self.windows_optimizer.optimize_audio_processing()
                self._log("Optimization results:")
                for key, value in optimizations.items():
                    self._log(f"  {key}: {value}")
                
                # Set up exclusive audio if available
                exclusive_audio = self.windows_optimizer.setup_exclusive_audio()
                self._log(f"Exclusive audio setup: {'Success' if exclusive_audio else 'Failed'}")
                
                # Enable hardware acceleration if available and enabled
                if self.use_hardware_accel.get():
                    hw_accel = self.windows_optimizer.enable_hardware_acceleration()
                    self._log(f"Hardware acceleration: {'Enabled' if hw_accel else 'Disabled'}")
            
            # Load input file
            self._log(f"Loading input file: {self.input_file}")
            audio_data, sample_rate = sf.read(self.input_file)
            self._log(f"Input file loaded: {audio_data.shape}, {sample_rate}Hz")
            
            # Process audio
            self._log("Processing audio...")
            self._log(f"Upscale factor: {self.upscale_factor.get()}x")
            self._log(f"Quality level: {self.quality_level.get()}")
            
            # Get quality parameters
            if self.quality_level.get() == 1:  # Low
                quality_params = {"iterations": 1, "filter_size": 16}
            elif self.quality_level.get() == 2:  # Medium
                quality_params = {"iterations": 2, "filter_size": 32}
            else:  # High
                quality_params = {"iterations": 3, "filter_size": 64}
            
            # Process with scipy if available
            try:
                from scipy import signal
                
                start_time = time.time()
                
                # Determine target sample rate
                target_sample_rate = sample_rate * self.upscale_factor.get()
                self._log(f"Target sample rate: {target_sample_rate}Hz")
                
                # Process audio with resampling
                if audio_data.ndim == 1:
                    # Mono
                    processed_audio = signal.resample_poly(
                        audio_data, 
                        self.upscale_factor.get(), 
                        1, 
                        window=('kaiser', quality_params["filter_size"])
                    )
                else:
                    # Stereo or multi-channel
                    processed_audio = np.zeros((int(audio_data.shape[0] * self.upscale_factor.get()), audio_data.shape[1]))
                    for i in range(audio_data.shape[1]):
                        processed_audio[:, i] = signal.resample_poly(
                            audio_data[:, i], 
                            self.upscale_factor.get(), 
                            1, 
                            window=('kaiser', quality_params["filter_size"])
                        )
                
                # Apply additional processing based on quality level
                for _ in range(quality_params["iterations"]):
                    # Apply some enhancement (simple high-frequency boost for demonstration)
                    if processed_audio.ndim == 1:
                        # Mono
                        spectrum = np.fft.rfft(processed_audio)
                        # Boost high frequencies
                        freq_bins = len(spectrum)
                        boost_start = int(freq_bins * 0.5)  # Boost frequencies above 50%
                        boost_factor = 1.2  # 20% boost
                        spectrum[boost_start:] *= boost_factor
                        processed_audio = np.fft.irfft(spectrum, len(processed_audio))
                    else:
                        # Stereo or multi-channel
                        for i in range(processed_audio.shape[1]):
                            spectrum = np.fft.rfft(processed_audio[:, i])
                            # Boost high frequencies
                            freq_bins = len(spectrum)
                            boost_start = int(freq_bins * 0.5)  # Boost frequencies above 50%
                            boost_factor = 1.2  # 20% boost
                            spectrum[boost_start:] *= boost_factor
                            processed_audio[:, i] = np.fft.irfft(spectrum, len(processed_audio[:, i]))
                
                process_time = time.time() - start_time
                self._log(f"Processing completed in {process_time:.2f} seconds")
                
            except ImportError:
                self._log("scipy not available, using simple interpolation")
                
                # Simple interpolation as fallback
                if audio_data.ndim == 1:
                    # Mono
                    processed_audio = np.interp(
                        np.linspace(0, len(audio_data) - 1, len(audio_data) * self.upscale_factor.get()),
                        np.arange(len(audio_data)),
                        audio_data
                    )
                else:
                    # Stereo or multi-channel
                    processed_audio = np.zeros((int(audio_data.shape[0] * self.upscale_factor.get()), audio_data.shape[1]))
                    for i in range(audio_data.shape[1]):
                        processed_audio[:, i] = np.interp(
                            np.linspace(0, len(audio_data[:, i]) - 1, len(audio_data[:, i]) * self.upscale_factor.get()),
                            np.arange(len(audio_data[:, i])),
                            audio_data[:, i]
                        )
                
                target_sample_rate = sample_rate * self.upscale_factor.get()
            
            # Save output
            self._log(f"Saving output to: {self.output_file}")
            sf.write(self.output_file, processed_audio, target_sample_rate)
            
            # Update UI
            self.status_var.set(f"Processing complete. Saved to: {self.output_file}")
            self._log("Processing complete!")
            messagebox.showinfo("Success", f"Audio processing complete!\nSaved to: {self.output_file}")
            
            # Open output folder
            output_dir = os.path.dirname(self.output_file)
            if platform.system() == "Windows":
                os.startfile(output_dir)
            elif platform.system() == "Darwin":  # macOS
                import subprocess
                subprocess.run(["open", output_dir])
            else:  # Linux
                import subprocess
                subprocess.run(["xdg-open", output_dir])
        
        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
            self._log(f"Error: {str(e)}")
            messagebox.showerror("Error", f"Processing failed: {str(e)}")
        
        finally:
            self.processing = False
            self.process_button.config(state=tk.NORMAL)
            self.progress.stop()

    def _compare_files(self):
        """Compare input and output files visually."""
        if not self.input_file or not self.output_file:
            messagebox.showerror("Error", "Both input and output files must be selected.")
            return
        try:
            self._log("Comparing files...")
            self._plot_waveform(self.input_file, self.output_file)
        except Exception as e:
            self._log(f"Error comparing files: {e}")
            messagebox.showerror("Error", f"Failed to compare files: {e}")

    def _plot_waveform(self, file1, file2):
        """Plots the waveforms of two audio files for comparison."""
        data1, sr1 = sf.read(file1)
        data2, sr2 = sf.read(file2)
        if sr1 != sr2:
            raise ValueError("Sample rates do not match!")

        time_axis1 = np.linspace(0, len(data1) / sr1, num=len(data1))
        time_axis2 = np.linspace(0, len(data2) / sr2, num=len(data2))

        plt.figure(figsize=(10, 6))
        plt.plot(time_axis1, data1, label="Original")
        plt.plot(time_axis2, data2, label="Upscaled", alpha=0.75)
        plt.xlabel("Time (s)")
        plt.ylabel("Amplitude")
        plt.title("Audio Waveform Comparison")
        plt.legend()
        plt.show()

    def _play_file_normally(self):
        """Play the input file using the default system player."""
        if not self.input_file:
            messagebox.showerror("Error", "No input file selected.")
            return
        try:
            logger.info(f"Playing file: {self.input_file}")
            if platform.system() == "Windows":
                os.startfile(self.input_file)
            elif platform.system() == "Darwin":  # macOS
                import subprocess
                subprocess.run(["open", self.input_file])
            else:  # Linux
                import subprocess
                subprocess.run(["xdg-open", self.input_file])
        except Exception as e:
            logger.error(f"Error playing file: {e}")
            messagebox.showerror("Error", f"Failed to play file: {e}")

def main():
    # Check if required packages are installed
    try:
        import numpy
        import soundfile
    except ImportError as e:
        print(f"Error: Required package not found: {e}")
        print("Please install required packages:")
        print("pip install numpy soundfile scipy")
        return 1
    
    # Create and run the application
    app = AudioUpscalerGUI()
    app.mainloop()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
