#include "samsung_protocol.h"

#include <assert.h>
#include <string.h>
#include "util/log.h"

// Create protocol header
void
sc_samsung_protocol_create_header(struct sc_samsung_protocol_header *header,
                                 enum sc_samsung_protocol_type type,
                                 uint8_t command) {
    assert(header);
    
    if (type == SC_SAMSUNG_PROTOCOL_SMART_VIEW) {
        header->magic = SMART_VIEW_MAGIC;
        header->version = SAMSUNG_SMART_VIEW_PROTOCOL_VERSION;
    } else if (type == SC_SAMSUNG_PROTOCOL_DEX) {
        header->magic = DEX_MAGIC;
        header->version = SAMSUNG_DEX_PROTOCOL_VERSION;
    } else {
        // Default to Smart View
        header->magic = SMART_VIEW_MAGIC;
        header->version = SAMSUNG_SMART_VIEW_PROTOCOL_VERSION;
    }
    
    header->command = command;
    header->flags = 0;
}

// Parse protocol header
enum sc_samsung_protocol_type
sc_samsung_protocol_parse_header(const unsigned char *buffer, size_t len,
                                struct sc_samsung_protocol_header *header) {
    assert(buffer && header);
    
    if (len < sizeof(struct sc_samsung_protocol_header)) {
        LOGW("Buffer too small to contain protocol header");
        return SC_SAMSUNG_PROTOCOL_UNKNOWN;
    }
    
    memcpy(header, buffer, sizeof(struct sc_samsung_protocol_header));
    
    if (header->magic == SMART_VIEW_MAGIC) {
        return SC_SAMSUNG_PROTOCOL_SMART_VIEW;
    } else if (header->magic == DEX_MAGIC) {
        return SC_SAMSUNG_PROTOCOL_DEX;
    }
    
    return SC_SAMSUNG_PROTOCOL_UNKNOWN;
}

// Create Smart View hello message
size_t
sc_samsung_protocol_create_smart_view_hello(unsigned char *buffer, size_t len,
                                          const char *device_name,
                                          uint16_t width, uint16_t height) {
    assert(buffer && device_name);
    
    // Ensure buffer is large enough
    if (len < 32) {
        LOGW("Buffer too small for Smart View hello message");
        return 0;
    }
    
    // Clear buffer
    memset(buffer, 0, 32);
    
    // Create header
    struct sc_samsung_protocol_header header;
    sc_samsung_protocol_create_header(&header, SC_SAMSUNG_PROTOCOL_SMART_VIEW,
                                     SMART_VIEW_CMD_HELLO);
    
    // Copy header to buffer
    memcpy(buffer, &header, sizeof(header));
    
    // Add device info
    size_t name_len = strlen(device_name);
    if (name_len > 10) {
        name_len = 10;  // Limit name length
    }
    
    memcpy(buffer + 8, device_name, name_len);
    *(uint16_t *)(buffer + 18) = width;
    *(uint16_t *)(buffer + 20) = height;
    
    return 32;  // Fixed size message
}

// Create DeX init message
size_t
sc_samsung_protocol_create_dex_init(unsigned char *buffer, size_t len,
                                   const char *device_name,
                                   uint16_t width, uint16_t height) {
    assert(buffer && device_name);
    
    // Ensure buffer is large enough
    if (len < 32) {
        LOGW("Buffer too small for DeX init message");
        return 0;
    }
    
    // Clear buffer
    memset(buffer, 0, 32);
    
    // Create header
    struct sc_samsung_protocol_header header;
    sc_samsung_protocol_create_header(&header, SC_SAMSUNG_PROTOCOL_DEX,
                                     DEX_CMD_INIT);
    
    // Copy header to buffer
    memcpy(buffer, &header, sizeof(header));
    
    // Add device info
    size_t name_len = strlen(device_name);
    if (name_len > 10) {
        name_len = 10;  // Limit name length
    }
    
    memcpy(buffer + 8, device_name, name_len);
    *(uint16_t *)(buffer + 18) = width;
    *(uint16_t *)(buffer + 20) = height;
    
    return 32;  // Fixed size message
}

// Create input event message
size_t
sc_samsung_protocol_create_input_event(unsigned char *buffer, size_t len,
                                      enum sc_samsung_protocol_type type,
                                      const struct sc_samsung_input_event *event) {
    assert(buffer && event);
    
    // Ensure buffer is large enough
    if (len < 16) {
        LOGW("Buffer too small for input event message");
        return 0;
    }
    
    // Create header
    struct sc_samsung_protocol_header header;
    uint8_t cmd = (type == SC_SAMSUNG_PROTOCOL_SMART_VIEW) ? 
                  SMART_VIEW_CMD_INPUT : DEX_CMD_INPUT;
    
    sc_samsung_protocol_create_header(&header, type, cmd);
    
    // Copy header to buffer
    memcpy(buffer, &header, sizeof(header));
    
    // Add event data
    memcpy(buffer + 8, event, sizeof(struct sc_samsung_input_event));
    
    return 16;  // Fixed size message
}

// Parse frame data
bool
sc_samsung_protocol_parse_frame(const unsigned char *buffer, size_t len,
                               struct sc_samsung_frame *frame,
                               const unsigned char **frame_data) {
    assert(buffer && frame && frame_data);
    
    // Check if buffer is large enough for header and frame info
    if (len < 8 + sizeof(struct sc_samsung_frame)) {
        LOGW("Buffer too small to contain frame data");
        return false;
    }
    
    // Skip protocol header
    buffer += 8;
    len -= 8;
    
    // Copy frame info
    memcpy(frame, buffer, sizeof(struct sc_samsung_frame));
    
    // Check if buffer contains the entire frame
    if (len < sizeof(struct sc_samsung_frame) + frame->data_size) {
        LOGW("Buffer too small to contain entire frame");
        return false;
    }
    
    // Set frame data pointer
    *frame_data = buffer + sizeof(struct sc_samsung_frame);
    
    return true;
}
