// Configuration for API keys and settings
module.exports = {
  // OCR Services
  mistral: {
    apiKey: process.env.MISTRAL_API_KEY || "your-mistral-api-key",
  },
  
  // LLM Services
  groq: {
    apiKey: process.env.GROQ_API_KEY || "your-groq-api-key",
    accounts: [
      { name: "primary", apiKey: process.env.GROQ_API_KEY_1 || "your-groq-api-key-1" },
      { name: "secondary", apiKey: process.env.GROQ_API_KEY_2 || "your-groq-api-key-2" }
    ],
    models: [
      { name: "llama3-8b-8192", id: "llama3-8b-8192" },
      { name: "llama3-70b-8192", id: "llama3-70b-8192" },
      { name: "mixtral-8x7b-32768", id: "mixtral-8x7b-32768" }
    ],
    defaultModel: "llama3-8b-8192"
  },
  
  // Lightning AI
  lightning: {
    apiKey: process.env.LIGHTNING_API_KEY || "your-lightning-api-key",
    bitnetModelEndpoint: process.env.BITNET_ENDPOINT || "https://lightning.ai/your-bitnet-endpoint"
  },
  
  // Local Model
  localModel: {
    path: "./models/tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf", // Path to the local model file
    modelType: "llama", // Model architecture
    contextSize: 2048, // Context window size
    temperature: 0.7 // Generation temperature
  },
  
  // Application Settings
  app: {
    screenshotDir: "./screenshots",
    solutionCacheDir: "./cache",
    defaultOpacity: 0.9,
    maxHistoryItems: 10
  }
};
