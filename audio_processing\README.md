# Audio Processing Suite with Windows 11 Optimizations

This project provides advanced audio processing capabilities with specific optimizations for Windows 11 to reduce resource usage and latency. It integrates both audio upscaling and audio processing components into a unified system.

## Project Structure

- **src/**: Core source code for audio processing
  - Contains the main implementation files for audio processing algorithms
  - Windows 11 optimization code
  - Audio driver integration
  - Audio upscaling algorithms

- **scripts/**: Utility scripts for running the application
  - Batch files for launching the application
  - Installation scripts
  - Environment setup scripts
  - Audio processing scripts

- **utils/**: Helper utilities
  - Audio format conversion utilities
  - File handling utilities
  - System information gathering
  - Audio playback utilities

- **gui/**: Graphical user interface components
  - Main application GUI
  - Settings dialogs
  - Visualization components
  - AudioSR GUI with drag-and-drop functionality

- **tests/**: Test files
  - Unit tests
  - Integration tests
  - Performance benchmarks
  - Windows 11 optimization tests

- **docs/**: Documentation
  - User guides
  - API documentation
  - Development notes

## Key Features

- Real-time audio upscaling with reduced latency
- Windows 11-specific optimizations for better performance
- Direct interaction with audio drivers
- Hardware acceleration for AMD/NVIDIA GPUs
- Batch processing capabilities
- Audio Super Resolution (AudioSR) integration
- Mojo language integration for high-performance processing

## Related Files

### Core Components
- **windows11_optimizations.py**: Core optimizations for Windows 11
- **realtime_audio_upscaler.py**: Real-time audio processing with reduced latency
- **amd_rocm_accelerator.py**: AMD GPU acceleration for audio processing
- **mojo_audio_upscaler.mojo**: High-performance audio processing in Mojo language
- **mojo_bridge.py**: Python bridge to the Mojo implementation

### GUI Components
- **audio_upscaler_gui.py**: GUI interface for the audio upscaler
- **audiosr_gui.py**: Advanced GUI for AudioSR with drag-and-drop functionality

### Processing Components
- **process_audio_sr.py**: Process audio with AudioSR
- **process_and_play.py**: Process and play audio files
- **process_rave.py**: Process specific audio files

### Utility Components
- **play_audio.py**: Audio playback utilities
- **play_rave.py**: Play specific audio files
- **fix_mono_output.py**: Fix mono output issues

## Usage

See the documentation in the `docs/` directory for detailed usage instructions.
