"""
Simplified test script for Windows 11 optimizations only
"""

import os
import sys
import time
import logging
import platform
import tempfile

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"win11_test_{time.strftime('%Y%m%d_%H%M%S')}.log")

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("Win11Test")

def log_system_info():
    """Log detailed system information"""
    logger.info("=== System Information ===")
    logger.info(f"Platform: {platform.platform()}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Processor: {platform.processor()}")
    
    # Check if Windows 11
    is_windows11 = False
    if platform.system() == "Windows":
        try:
            version = platform.version().split('.')
            if len(version) >= 3 and int(version[2]) >= 22000:
                is_windows11 = True
        except Exception as e:
            logger.error(f"Error checking Windows version: {e}")
    
    logger.info(f"Windows 11: {is_windows11}")
    
    # Log memory info
    try:
        import psutil
        vm = psutil.virtual_memory()
        logger.info("=== Memory Information ===")
        logger.info(f"Total memory: {vm.total / (1024**3):.2f} GB")
        logger.info(f"Available memory: {vm.available / (1024**3):.2f} GB")
        logger.info(f"Used memory: {vm.used / (1024**3):.2f} GB")
        logger.info(f"Memory percent: {vm.percent}%")
    except ImportError:
        logger.warning("psutil not available, skipping memory info")

def test_windows11_optimization():
    """Test Windows 11 optimizations"""
    logger.info("=== Testing Windows 11 Optimizations ===")
    
    try:
        # Check if Windows 11
        is_windows11 = False
        if platform.system() == "Windows":
            try:
                version = platform.version().split('.')
                if len(version) >= 3 and int(version[2]) >= 22000:
                    is_windows11 = True
            except Exception as e:
                logger.error(f"Error checking Windows version: {e}")
        
        if not is_windows11:
            logger.info("Not running on Windows 11, skipping this test")
            return {
                "success": False,
                "error": "Not running on Windows 11"
            }
        
        # Import Windows 11 optimizer
        try:
            from windows11_optimizations import Windows11Optimizer
            logger.info("Windows11Optimizer module imported successfully")
        except ImportError as e:
            logger.error(f"Windows11Optimizer not available: {e}")
            return {
                "success": False,
                "error": f"Windows11Optimizer not available: {e}"
            }
        
        # Initialize Windows 11 optimizer
        logger.info("Initializing Windows 11 optimizer...")
        start_time = time.time()
        optimizer = Windows11Optimizer()
        
        # Log system info from optimizer
        logger.info("Windows 11 system info:")
        for key, value in optimizer.system_info.items():
            logger.info(f"  {key}: {value}")
        
        # Apply optimizations
        logger.info("Applying Windows 11 optimizations...")
        optimizations = optimizer.optimize_audio_processing()
        
        # Log optimization results
        logger.info("Optimization results:")
        for key, value in optimizations.items():
            logger.info(f"  {key}: {value}")
        
        # Test exclusive audio
        logger.info("Setting up exclusive audio...")
        exclusive_audio = optimizer.setup_exclusive_audio()
        logger.info(f"Exclusive audio setup: {'Success' if exclusive_audio else 'Failed'}")
        
        # Test hardware acceleration
        logger.info("Enabling hardware acceleration...")
        hw_accel = optimizer.enable_hardware_acceleration()
        logger.info(f"Hardware acceleration: {'Enabled' if hw_accel else 'Disabled'}")
        
        init_time = time.time() - start_time
        logger.info(f"Windows 11 optimizations completed in {init_time:.2f} seconds")
        
        return {
            "success": True,
            "init_time": init_time,
            "optimizations": optimizations,
            "exclusive_audio": exclusive_audio,
            "hardware_acceleration": hw_accel
        }
        
    except Exception as e:
        logger.error(f"Error in Windows 11 optimization: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }

def main():
    logger.info("Starting Windows 11 optimization test")
    logger.info(f"Log file: {log_file}")
    
    # Log system information
    log_system_info()
    
    # Run Windows 11 optimization test
    result = test_windows11_optimization()
    
    # Print summary
    logger.info("=== Test Summary ===")
    if result['success']:
        logger.info("Windows 11 optimization test: SUCCESS")
        logger.info(f"  Initialization time: {result.get('init_time', 'N/A'):.2f} seconds")
        logger.info("  Optimizations:")
        for key, value in result.get('optimizations', {}).items():
            logger.info(f"    {key}: {value}")
        logger.info(f"  Exclusive audio: {result.get('exclusive_audio', 'N/A')}")
        logger.info(f"  Hardware acceleration: {result.get('hardware_acceleration', 'N/A')}")
    else:
        logger.info(f"Windows 11 optimization test: FAILED - {result.get('error', 'Unknown error')}")
    
    logger.info(f"Complete log available at: {log_file}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
