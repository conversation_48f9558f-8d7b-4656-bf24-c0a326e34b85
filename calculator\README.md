# Sci-Fi Calculator Application

A sci-fi themed calculator with advanced mathematical functions and visual effects.

## Project Structure

- **src/**: Core source code
  - Calculator logic
  - Mathematical functions
  - Error handling

- **gui/**: Graphical user interface components
  - Sci-fi themed UI elements
  - Animation components
  - Visual effects

- **scripts/**: Utility scripts
  - Build scripts
  - Installation scripts
  - Development utilities

- **utils/**: Helper utilities
  - Number formatting
  - Mathematical utilities
  - Theme management

- **tests/**: Test files
  - Unit tests
  - Integration tests
  - UI tests

- **docs/**: Documentation
  - User guides
  - API documentation
  - Development notes

## Key Features

- Sci-fi themed interface with particle animations
- Advanced mathematical functions (trigonometry, logarithms, etc.)
- Memory functions
- Comprehensive error handling
- Visual feedback with animations

## Related Files

- **calculator_scifi_gui.py**: Main GUI implementation with sci-fi theme
- **calculator_improved.py**: Core calculator logic with advanced math functions
- **calculator_test_suite.py**: Test suite for the calculator

## Usage

See the documentation in the `docs/` directory for detailed usage instructions.
