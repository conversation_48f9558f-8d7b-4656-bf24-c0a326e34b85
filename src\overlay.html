<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Solution Overlay</title>
  <link rel="stylesheet" href="overlay.css">
</head>
<body>
  <div class="overlay-container">
    <div class="solution-header">
      <div class="drag-handle"></div>
      <div class="controls">
        <button id="minimize-btn">_</button>
        <button id="close-btn">×</button>
      </div>
    </div>
    
    <div class="solution-content">
      <div class="tabs">
        <button class="tab-btn active" data-tab="analysis">Analysis</button>
        <button class="tab-btn" data-tab="approach">Approach</button>
        <button class="tab-btn" data-tab="solution">Solution</button>
        <button class="tab-btn" data-tab="complexity">Complexity</button>
      </div>
      
      <div class="tab-content">
        <div id="analysis-tab" class="tab-pane active">
          <h3>Problem Analysis</h3>
          <div id="analysis-content" class="content-area"></div>
        </div>
        
        <div id="approach-tab" class="tab-pane">
          <h3>Approach</h3>
          <div id="approach-content" class="content-area"></div>
        </div>
        
        <div id="solution-tab" class="tab-pane">
          <h3>Solution</h3>
          <pre id="solution-content" class="code-area"></pre>
        </div>
        
        <div id="complexity-tab" class="tab-pane">
          <h3>Complexity Analysis</h3>
          <div id="time-complexity" class="complexity-item">
            <h4>Time Complexity</h4>
            <div id="time-content"></div>
          </div>
          <div id="space-complexity" class="complexity-item">
            <h4>Space Complexity</h4>
            <div id="space-content"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="overlay.js"></script>
</body>
</html>
