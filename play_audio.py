"""
Simple script to play an audio file
"""

import os
import sys
import platform
import subprocess

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def main():
    # Check command line arguments
    if len(sys.argv) < 2:
        print("Usage: python play_audio.py <audio_file>")
        return 1
    
    audio_file = sys.argv[1]
    
    # Check if file exists
    if not os.path.isfile(audio_file):
        print(f"Error: File not found: {audio_file}")
        return 1
    
    # Play the audio file
    play_audio_file(audio_file)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
