@echo off
echo Adding Python Scripts directory to PATH and processing audio

REM Set Python paths
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Verify Python and pip are available
echo Python version:
"%PYTHON_PATH%\python.exe" --version

echo.
echo Pip version:
"%PYTHON_PATH%\python.exe" -m pip --version

REM Check if AudioSR is installed
echo.
echo Checking if AudioSR is installed...
"%PYTHON_PATH%\python.exe" -c "try: import audiosr; print('<PERSON><PERSON> is installed.'); except ImportError: print('AudioSR is not installed.')"

REM Process FLAC files
echo.
echo Running FLAC processing script...
"%PYTHON_PATH%\python.exe" process_flac_files.py

echo.
echo Done!
pause
