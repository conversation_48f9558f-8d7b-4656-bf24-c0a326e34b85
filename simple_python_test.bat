@echo on
echo Running Audio Upscaler GUI

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Check if Python exists
if exist "%PYTHON_PATH%\python.exe" (
    echo Found Python at: %PYTHON_PATH%
) else (
    echo Python not found at %PYTHON_PATH%
    goto :end
)

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Add Scripts folder to PATH
set "PATH=%PATH%;C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Check for required packages
echo Checking required packages...
"%PYTHON_PATH%\python.exe" -c "try: import numpy, scipy, soundfile, tkinter; print('All required packages are installed.'); exit(0); except ImportError as e: print(f'Missing package: {e}'); exit(1)"

if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    "%PYTHON_PATH%\python.exe" -m pip install numpy scipy soundfile
)

REM Run the GUI
echo Starting Audio Upscaler GUI...
cd AudioUpscaler
"%PYTHON_PATH%\python.exe" gui\main_gui.py

:end
echo GUI closed.
pause
