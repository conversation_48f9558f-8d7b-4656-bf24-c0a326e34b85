@echo off
echo Running Audio Upscaler Test with Detailed Logging

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Run the test script
echo Running test script...
"%PYTHON_PATH%\python.exe" test_audio_upscaler.py

echo.
echo Test completed.
echo Check the logs directory for detailed test results.
pause
