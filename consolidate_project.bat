@echo off
echo Project Consolidation Script
echo ===========================
echo.
echo This script will consolidate multiple files into fewer, more comprehensive files
echo to streamline the project structure and make it more maintainable.
echo.
echo It will create new consolidated files in the project directories.
echo The original files will remain in place.
echo.

REM Set Python path
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Run the consolidation script
echo Running consolidation script...
"%PYTHON_PATH%\python.exe" common\scripts\consolidate_files.py

echo.
echo Done!
pause
