"""
Simple test for Windows 11 optimizations
"""

# Test the exception handling in Windows11Optimizer
try:
    from windows11_optimizations import Windows11Optimizer
    
    # Create optimizer instance
    optimizer = Windows11Optimizer()
    
    # Print if running on Windows 11
    print(f"Running on Windows 11: {optimizer.is_windows11}")
    
    # Print basic system info
    print(f"OS: {optimizer.system_info['os']}")
    print(f"Version: {optimizer.system_info['version']}")
    
    print("Test successful!")
except Exception as e:
    print(f"Test failed with error: {str(e)}")
