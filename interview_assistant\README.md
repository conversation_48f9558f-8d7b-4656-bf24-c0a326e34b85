# Interview Assistant Application

An Electron-based desktop application designed to assist during technical coding interviews by providing AI-generated solutions through an invisible overlay.

## Project Structure

- **src/**: Core source code
  - OCR implementation
  - AI solution generation
  - Overlay management

- **electron/**: Electron application files
  - Main process code
  - Renderer process code
  - IPC communication

- **scripts/**: Utility scripts
  - Build scripts
  - Installation scripts
  - Development utilities

- **utils/**: Helper utilities
  - Screenshot utilities
  - Text processing
  - Cache management

- **tests/**: Test files
  - Unit tests
  - Integration tests
  - End-to-end tests

- **docs/**: Documentation
  - User guides
  - API documentation
  - Development notes

## Key Features

- Screenshot-to-solution generation using OCR
- Multiple AI models (local, Groq, BitNet)
- Invisible overlay that's hidden from screen sharing
- Keyboard shortcuts for controlling the application
- Real-time debugging assistance

## Related Files

- **main-enhanced.js**: Main Electron application logic
- **preload-enhanced.js**: Preload script for Electron
- **config.js**: Configuration for API keys and settings

## Usage

See the documentation in the `docs/` directory for detailed usage instructions.
