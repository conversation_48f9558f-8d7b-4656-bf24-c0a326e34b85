"""
Audio Preprocessor Module
Handles pre-processing steps for audio upscaling
"""

import os
import time
import logging
import numpy as np
import soundfile as sf
import torch
from typing import Dict, Any, Optional, List, Union, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AudioPreprocessor:
    """
    Audio preprocessor for enhancing audio quality before upscaling
    """
    def __init__(self,
                 device: str = "auto",
                 quality_level: int = 3,
                 reduce_load: bool = True):
        """
        Initialize the audio preprocessor

        Args:
            device: Device to use ("auto", "cuda", or "cpu")
            quality_level: Quality level (1=Low, 2=Medium, 3=High)
            reduce_load: Whether to reduce load on GPU/CPU
        """
        self.device = self._get_device(device)
        self.quality_level = quality_level
        self.reduce_load = reduce_load

        # Initialize parameters based on quality level
        self._init_parameters()

        logger.info(f"Audio Preprocessor initialized on {self.device}")
        logger.info(f"  Quality level: {quality_level}")
        logger.info(f"  Reduced load: {reduce_load}")

    def _get_device(self, device: str) -> torch.device:
        """Determine the appropriate device to use"""
        if device == "auto":
            if torch.cuda.is_available():
                return torch.device("cuda")
            return torch.device("cpu")
        return torch.device(device)

    def _init_parameters(self):
        """Initialize parameters based on quality level"""
        # Common parameters
        self.noise_reduction_threshold = 0.1

        # Quality-specific parameters
        if self.quality_level == 1:  # Low
            self.filter_size = 1024
            self.fft_size = 2048
            self.noise_reduction_strength = 0.5
            self.dc_offset_correction = True
            self.phase_correction = False
        elif self.quality_level == 2:  # Medium
            self.filter_size = 4096
            self.fft_size = 4096
            self.noise_reduction_strength = 0.3
            self.dc_offset_correction = True
            self.phase_correction = True
        else:  # High
            self.filter_size = 8192
            self.fft_size = 8192
            self.noise_reduction_strength = 0.2  # More subtle noise reduction
            self.dc_offset_correction = True
            self.phase_correction = True

    def preprocess_file(self,
                       input_file: str,
                       output_file: Optional[str] = None) -> str:
        """
        Preprocess an audio file

        Args:
            input_file: Path to input audio file
            output_file: Path to output audio file (optional)

        Returns:
            Path to the preprocessed audio file
        """
        # Check if input file exists
        if not os.path.isfile(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # Generate output filename if not provided
        if output_file is None:
            input_dir = os.path.dirname(input_file)
            input_basename = os.path.basename(input_file)
            input_name, input_ext = os.path.splitext(input_basename)
            output_dir = os.path.join(input_dir, "preprocessed")
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{input_name}_preprocessed{input_ext}")

        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Load audio file
        logger.info(f"Loading audio file: {input_file}")
        audio_data, sample_rate = sf.read(input_file)

        # Log audio properties
        duration = len(audio_data) / sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]
        logger.info(f"Audio duration: {duration:.2f} seconds")
        logger.info(f"Audio channels: {channels}")
        logger.info(f"Audio sample rate: {sample_rate}Hz")

        # Process audio
        logger.info(f"Preprocessing audio...")
        start_time = time.time()

        # Process audio based on channels
        if audio_data.ndim == 1:
            # Mono
            processed_audio = self._preprocess_audio(audio_data, sample_rate)
        else:
            # Multi-channel
            processed_audio = np.zeros_like(audio_data)
            for i in range(audio_data.shape[1]):
                processed_audio[:, i] = self._preprocess_audio(audio_data[:, i], sample_rate)

        process_time = time.time() - start_time
        logger.info(f"Preprocessing completed in {process_time:.2f} seconds")

        # Save output
        logger.info(f"Saving output to: {output_file}")
        sf.write(output_file, processed_audio, sample_rate)

        return output_file

    def _preprocess_audio(self,
                         audio_data: np.ndarray,
                         sample_rate: int) -> np.ndarray:
        """
        Preprocess audio data

        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio data

        Returns:
            Preprocessed audio data
        """
        # Convert to tensor and move to device
        audio_tensor = torch.tensor(audio_data, dtype=torch.float32).to(self.device)

        # Apply DC offset correction if enabled
        if self.dc_offset_correction:
            audio_tensor = self._correct_dc_offset(audio_tensor)

        # Apply noise reduction
        audio_tensor = self._reduce_noise(audio_tensor, sample_rate)

        # Apply phase correction if enabled
        if self.phase_correction:
            audio_tensor = self._correct_phase(audio_tensor, sample_rate)

        # Detect and preserve transients
        audio_tensor = self._preserve_transients(audio_tensor, sample_rate)

        # Convert back to numpy
        processed_audio = audio_tensor.cpu().numpy()

        return processed_audio

    def _correct_dc_offset(self, audio_tensor: torch.Tensor) -> torch.Tensor:
        """
        Correct DC offset in audio

        Args:
            audio_tensor: Audio tensor

        Returns:
            Corrected audio tensor
        """
        # Calculate mean (DC offset)
        dc_offset = torch.mean(audio_tensor)

        # Subtract DC offset
        corrected = audio_tensor - dc_offset

        logger.info(f"Corrected DC offset: {dc_offset.item():.6f}")

        return corrected

    def _reduce_noise(self,
                     audio_tensor: torch.Tensor,
                     sample_rate: int) -> torch.Tensor:
        """
        Reduce noise in audio using spectral gating

        Args:
            audio_tensor: Audio tensor
            sample_rate: Sample rate of the audio

        Returns:
            Noise-reduced audio tensor
        """
        # Process in chunks to avoid memory issues
        chunk_size = 8192
        if len(audio_tensor) <= chunk_size:
            return self._reduce_noise_chunk(audio_tensor, sample_rate)

        # Process in chunks with overlap
        overlap = chunk_size // 4
        result = torch.zeros_like(audio_tensor)

        # Process each chunk
        for i in range(0, len(audio_tensor), chunk_size - overlap):
            # Extract chunk
            chunk_end = min(i + chunk_size, len(audio_tensor))
            chunk = audio_tensor[i:chunk_end]

            # Process chunk
            processed_chunk = self._reduce_noise_chunk(chunk, sample_rate)

            # Apply fade in/out for overlap regions
            if i > 0:  # Not the first chunk
                # Create fade-in window for overlap region
                fade_in = torch.linspace(0, 1, overlap, device=self.device)

                # Apply fade-in to current chunk
                if len(processed_chunk) > overlap:
                    processed_chunk[:overlap] *= fade_in

                # Apply fade-out to previous chunk in overlap region
                fade_out = torch.linspace(1, 0, overlap, device=self.device)
                overlap_end = min(i + overlap, len(audio_tensor))
                result[i:overlap_end] *= fade_out[:overlap_end-i]

            # Add chunk to result
            chunk_result_end = min(i + len(processed_chunk), len(result))
            result[i:chunk_result_end] += processed_chunk[:chunk_result_end-i]

        return result

    def _reduce_noise_chunk(self,
                           audio_tensor: torch.Tensor,
                           sample_rate: int) -> torch.Tensor:
        """
        Reduce noise in a chunk of audio using spectral gating

        Args:
            audio_tensor: Audio tensor chunk
            sample_rate: Sample rate of the audio

        Returns:
            Noise-reduced audio tensor chunk
        """
        # Compute FFT
        fft_size = min(self.fft_size, len(audio_tensor))
        fft = torch.fft.rfft(audio_tensor, n=fft_size)

        # Get magnitude and phase
        magnitude = torch.abs(fft)
        phase = torch.angle(fft)

        # Estimate noise profile
        # Sort magnitudes and take the lowest 5% as noise estimate
        sorted_magnitudes, _ = torch.sort(magnitude)
        noise_threshold = sorted_magnitudes[int(len(sorted_magnitudes) * 0.05)]

        # Apply spectral gating
        gate_threshold = noise_threshold * (1.0 + self.noise_reduction_threshold)
        mask = magnitude > gate_threshold

        # Apply soft gating with strength parameter
        gain = torch.ones_like(magnitude)
        gain[~mask] = torch.max(torch.zeros_like(magnitude[~mask]),
                               1.0 - self.noise_reduction_strength * (gate_threshold - magnitude[~mask]) / gate_threshold)

        # Apply gain
        magnitude_reduced = magnitude * gain

        # Reconstruct FFT
        fft_real = magnitude_reduced * torch.cos(phase)
        fft_imag = magnitude_reduced * torch.sin(phase)
        fft_reduced = torch.complex(fft_real, fft_imag)

        # Inverse FFT
        audio_reduced = torch.fft.irfft(fft_reduced, n=fft_size)

        # Trim to original length
        audio_reduced = audio_reduced[:len(audio_tensor)]

        return audio_reduced

    def _correct_phase(self,
                      audio_tensor: torch.Tensor,
                      sample_rate: int) -> torch.Tensor:
        """
        Correct phase issues in audio

        Args:
            audio_tensor: Audio tensor
            sample_rate: Sample rate of the audio

        Returns:
            Phase-corrected audio tensor
        """
        # Process in chunks to avoid memory issues
        chunk_size = 8192
        if len(audio_tensor) <= chunk_size:
            return self._correct_phase_chunk(audio_tensor)

        # Process in chunks with overlap
        overlap = chunk_size // 4
        result = torch.zeros_like(audio_tensor)

        # Process each chunk
        for i in range(0, len(audio_tensor), chunk_size - overlap):
            # Extract chunk
            chunk_end = min(i + chunk_size, len(audio_tensor))
            chunk = audio_tensor[i:chunk_end]

            # Process chunk
            processed_chunk = self._correct_phase_chunk(chunk)

            # Apply fade in/out for overlap regions
            if i > 0:  # Not the first chunk
                # Create fade-in window for overlap region
                fade_in = torch.linspace(0, 1, overlap, device=self.device)

                # Apply fade-in to current chunk
                if len(processed_chunk) > overlap:
                    processed_chunk[:overlap] *= fade_in

                # Apply fade-out to previous chunk in overlap region
                fade_out = torch.linspace(1, 0, overlap, device=self.device)
                overlap_end = min(i + overlap, len(audio_tensor))
                result[i:overlap_end] *= fade_out[:overlap_end-i]

            # Add chunk to result
            chunk_result_end = min(i + len(processed_chunk), len(result))
            result[i:chunk_result_end] += processed_chunk[:chunk_result_end-i]

        return result

    def _correct_phase_chunk(self,
                            audio_tensor: torch.Tensor) -> torch.Tensor:
        """
        Correct phase issues in a chunk of audio

        Args:
            audio_tensor: Audio tensor chunk

        Returns:
            Phase-corrected audio tensor chunk
        """
        # Compute FFT
        fft_size = min(self.fft_size, len(audio_tensor))
        fft = torch.fft.rfft(audio_tensor, n=fft_size)

        # Get magnitude and phase
        magnitude = torch.abs(fft)
        phase = torch.angle(fft)

        # Apply phase correction
        # This is a simplified approach - we're just ensuring phase continuity
        # More advanced phase correction would require more complex algorithms

        # Compute phase derivative
        phase_diff = torch.diff(phase, prepend=phase[:1])

        # Detect phase jumps (greater than π)
        phase_jumps = torch.abs(phase_diff) > torch.pi

        # Correct phase jumps
        if torch.any(phase_jumps):
            # Create corrected phase
            corrected_phase = torch.zeros_like(phase)
            corrected_phase[0] = phase[0]

            # Accumulate phase differences with jump correction
            for i in range(1, len(phase)):
                diff = phase[i] - phase[i-1]

                # Wrap phase difference to [-π, π]
                if diff > torch.pi:
                    diff -= 2 * torch.pi
                elif diff < -torch.pi:
                    diff += 2 * torch.pi

                corrected_phase[i] = corrected_phase[i-1] + diff

            # Use corrected phase
            phase = corrected_phase

        # Reconstruct FFT
        fft_real = magnitude * torch.cos(phase)
        fft_imag = magnitude * torch.sin(phase)
        fft_corrected = torch.complex(fft_real, fft_imag)

        # Inverse FFT
        audio_corrected = torch.fft.irfft(fft_corrected, n=fft_size)

        # Trim to original length
        audio_corrected = audio_corrected[:len(audio_tensor)]

        return audio_corrected

    def _preserve_transients(self,
                            audio_tensor: torch.Tensor,
                            sample_rate: int) -> torch.Tensor:
        """
        Detect and preserve transients in audio

        Args:
            audio_tensor: Audio tensor
            sample_rate: Sample rate of the audio

        Returns:
            Audio tensor with preserved transients
        """
        # Process in chunks to avoid memory issues
        chunk_size = 8192
        if len(audio_tensor) <= chunk_size:
            return self._preserve_transients_chunk(audio_tensor, sample_rate)

        # Process in chunks with overlap
        overlap = chunk_size // 4
        result = torch.zeros_like(audio_tensor)

        # Process each chunk
        for i in range(0, len(audio_tensor), chunk_size - overlap):
            # Extract chunk
            chunk_end = min(i + chunk_size, len(audio_tensor))
            chunk = audio_tensor[i:chunk_end]

            # Process chunk
            processed_chunk = self._preserve_transients_chunk(chunk, sample_rate)

            # Apply fade in/out for overlap regions
            if i > 0:  # Not the first chunk
                # Create fade-in window for overlap region
                fade_in = torch.linspace(0, 1, overlap, device=self.device)

                # Apply fade-in to current chunk
                if len(processed_chunk) > overlap:
                    processed_chunk[:overlap] *= fade_in

                # Apply fade-out to previous chunk in overlap region
                fade_out = torch.linspace(1, 0, overlap, device=self.device)
                overlap_end = min(i + overlap, len(audio_tensor))
                result[i:overlap_end] *= fade_out[:overlap_end-i]

            # Add chunk to result
            chunk_result_end = min(i + len(processed_chunk), len(result))
            result[i:chunk_result_end] += processed_chunk[:chunk_result_end-i]

        return result

    def _preserve_transients_chunk(self,
                                  audio_tensor: torch.Tensor,
                                  sample_rate: int) -> torch.Tensor:
        """
        Detect and preserve transients in a chunk of audio

        Args:
            audio_tensor: Audio tensor chunk
            sample_rate: Sample rate of the audio

        Returns:
            Audio tensor chunk with preserved transients
        """
        # Compute derivative to detect rapid changes
        derivative = torch.diff(audio_tensor, prepend=audio_tensor[:1])

        # Compute envelope of derivative
        envelope = torch.abs(derivative)

        # Smooth envelope
        window_size = int(0.01 * sample_rate)  # 10ms window
        window_size = min(window_size, len(audio_tensor) // 4)  # Ensure window size is reasonable
        if window_size > 1:
            kernel = torch.ones(window_size, device=self.device) / window_size
            smoothed = torch.nn.functional.conv1d(
                envelope.unsqueeze(0).unsqueeze(0),
                kernel.unsqueeze(0).unsqueeze(0),
                padding=window_size//2
            ).squeeze(0).squeeze(0)
        else:
            smoothed = envelope

        # Find peaks in envelope
        # Simple peak detection - more advanced methods could be used
        threshold = torch.std(smoothed, dim=0) * 2.5
        peaks = smoothed > threshold

        # Create transient mask
        transient_mask = torch.zeros_like(audio_tensor, dtype=torch.bool)

        # Mark regions around peaks as transients
        if torch.any(peaks):
            # Find indices of peaks
            peak_indices = torch.where(peaks)[0]

            # Mark regions around peaks
            window_half = int(0.005 * sample_rate)  # 5ms window
            window_half = min(window_half, len(audio_tensor) // 10)  # Ensure window size is reasonable
            for peak in peak_indices:
                start = max(0, peak - window_half)
                end = min(len(audio_tensor), peak + window_half)
                transient_mask[start:end] = True

        # Preserve original signal in transient regions
        # For non-transient regions, use the preprocessed signal

        # For now, we're just returning the audio tensor as is
        # In a more advanced implementation, we could apply different
        # processing to transient and non-transient regions

        return audio_tensor

    def analyze_noise_profile(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        Analyze noise profile of audio

        Args:
            audio_data: Audio data as numpy array

        Returns:
            Dictionary with noise profile analysis
        """
        # Convert to tensor and move to device
        audio_tensor = torch.tensor(audio_data, dtype=torch.float32).to(self.device)

        # Compute FFT
        fft_size = self.fft_size
        fft = torch.fft.rfft(audio_tensor, n=fft_size)

        # Get magnitude
        magnitude = torch.abs(fft)

        # Estimate noise profile
        # Sort magnitudes and take the lowest 5% as noise estimate
        sorted_magnitudes, _ = torch.sort(magnitude)
        noise_threshold = sorted_magnitudes[int(len(sorted_magnitudes) * 0.05)]

        # Calculate noise level in dB
        noise_level_db = 20 * torch.log10(noise_threshold + 1e-10)

        # Calculate signal-to-noise ratio
        signal_level = torch.mean(magnitude)
        signal_level_db = 20 * torch.log10(signal_level + 1e-10)
        snr_db = signal_level_db - noise_level_db

        # Calculate noise spectrum
        noise_mask = magnitude < noise_threshold * 2
        noise_spectrum = torch.zeros_like(magnitude)
        noise_spectrum[noise_mask] = magnitude[noise_mask]

        # Calculate frequency distribution of noise
        freq_bins = torch.arange(len(magnitude), device=self.device) / len(magnitude) * (fft_size // 2)
        noise_weighted_freq = torch.sum(freq_bins * noise_spectrum) / torch.sum(noise_spectrum + 1e-10)

        return {
            'noise_threshold': noise_threshold.item(),
            'noise_level_db': noise_level_db.item(),
            'signal_level_db': signal_level_db.item(),
            'snr_db': snr_db.item(),
            'noise_weighted_freq': noise_weighted_freq.item(),
            'noise_spectrum': noise_spectrum.cpu().numpy()
        }


# Test function
def test_preprocessor():
    """Test the audio preprocessor with a simple sine wave"""
    # Create a simple test signal
    sample_rate = 44100
    duration = 2  # seconds
    t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)

    # Create a test signal with multiple frequencies
    signal = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz
    signal += 0.3 * np.sin(2 * np.pi * 880 * t)  # 880 Hz
    signal += 0.2 * np.sin(2 * np.pi * 1760 * t)  # 1760 Hz

    # Add some noise
    rng = np.random.default_rng(42)  # For reproducibility
    signal += 0.05 * rng.standard_normal(len(t))

    # Add DC offset
    signal += 0.1

    # Save test signal
    test_file = "test_signal.wav"
    sf.write(test_file, signal, sample_rate)

    # Create preprocessor
    preprocessor = AudioPreprocessor(device="auto", quality_level=3)

    # Process test signal
    output_file = preprocessor.preprocess_file(test_file)

    print(f"Preprocessed file saved to: {output_file}")

    # Load processed file
    processed_signal, _ = sf.read(output_file)

    # Analyze original and processed signals
    print("\nOriginal signal:")
    print(f"  Mean (DC offset): {np.mean(signal):.6f}")
    print(f"  Standard deviation: {np.std(signal):.6f}")
    print(f"  Peak amplitude: {np.max(np.abs(signal)):.6f}")

    print("\nProcessed signal:")
    print(f"  Mean (DC offset): {np.mean(processed_signal):.6f}")
    print(f"  Standard deviation: {np.std(processed_signal):.6f}")
    print(f"  Peak amplitude: {np.max(np.abs(processed_signal)):.6f}")


if __name__ == "__main__":
    test_preprocessor()
