"""
Simple script to process the RAVE.flac file
"""

import os
import sys
import time
import numpy as np
import soundfile as sf
import platform
import psutil
import logging
from scipy import signal

def setup_logging():
    """Set up logging to file"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, "audio_processing.log")

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

    return log_file

def get_system_info():
    """Get system information for hardware-level logging"""
    info = {
        "platform": platform.platform(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(logical=False),
        "logical_cpu_count": psutil.cpu_count(logical=True),
        "memory_total": psutil.virtual_memory().total / (1024 ** 3),  # GB
        "memory_available": psutil.virtual_memory().available / (1024 ** 3),  # GB
    }

    return info

def main():
    # Set up logging
    log_file = setup_logging()

    # Log system information
    system_info = get_system_info()
    logging.info("System Information:")
    for key, value in system_info.items():
        logging.info(f"  {key}: {value}")

    # File to process - use a simplified path to avoid encoding issues
    downloads_dir = os.path.join(os.path.expanduser("~"), "Downloads")

    # Look for files containing "RAVE" and ending with ".flac"
    input_file = None
    for file in os.listdir(downloads_dir):
        if "RAVE" in file and file.endswith(".flac"):
            input_file = os.path.join(downloads_dir, file)
            break

    if input_file is None:
        logging.error("RAVE.flac file not found in Downloads folder")
        return 1

    # Check if file exists
    if not os.path.isfile(input_file):
        logging.error(f"Error: File not found: {input_file}")
        return 1

    logging.info(f"Processing file: {input_file}")

    # Create output directory in the current script directory to avoid path issues
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output")
    os.makedirs(output_dir, exist_ok=True)

    # Create output path with a simple name
    output_file = os.path.join(output_dir, "RAVE_upscaled.wav")
    logging.info(f"Output will be saved to: {output_file}")

    # Monitor CPU and memory usage
    cpu_percent_start = psutil.cpu_percent(interval=0.1)
    memory_start = psutil.virtual_memory().percent
    logging.info(f"Initial CPU usage: {cpu_percent_start}%")
    logging.info(f"Initial memory usage: {memory_start}%")

    # Load input file
    logging.info("Loading audio file...")
    try:
        audio_data, sample_rate = sf.read(input_file)
        logging.info(f"Loaded audio: {audio_data.shape}, {sample_rate}Hz")

        # Log audio file properties
        duration = len(audio_data) / sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]
        logging.info(f"Audio duration: {duration:.2f} seconds")
        logging.info(f"Audio channels: {channels}")
        logging.info(f"Audio sample rate: {sample_rate}Hz")
    except Exception as e:
        logging.error(f"Error loading audio file: {e}")
        return 1

    # Process the file
    logging.info("Processing audio...")
    start_time = time.time()
    process_start_cpu = psutil.cpu_percent(interval=0.1)
    process_start_memory = psutil.virtual_memory().percent

    # Upscale factor and quality
    upscale_factor = 2
    filter_size = 64  # Higher quality filter for smoother results

    # Processing parameters
    high_boost = 0.05  # Reduced from 0.2 (20%) to 0.05 (5%)
    boost_start_freq = 0.7  # Only boost very high frequencies (above 70% instead of 50%)

    # Add a subtle warmth to the low end
    low_boost = 0.03  # 3% boost
    low_freq_end = 0.2  # Boost frequencies below 20%

    logging.info(f"Processing parameters:")
    logging.info(f"  Upscale factor: {upscale_factor}x")
    logging.info(f"  Filter size: {filter_size}")
    logging.info(f"  High frequency boost: {high_boost * 100:.1f}%")
    logging.info(f"  Low frequency boost: {low_boost * 100:.1f}%")

    try:
        # Process audio with resampling
        if audio_data.ndim == 1:
            # Mono
            processed_audio = signal.resample_poly(
                audio_data,
                upscale_factor,
                1,
                window=('kaiser', filter_size)
            )
        else:
            # Stereo or multi-channel
            processed_audio = np.zeros((int(audio_data.shape[0] * upscale_factor), audio_data.shape[1]))
            for i in range(audio_data.shape[1]):
                processed_audio[:, i] = signal.resample_poly(
                    audio_data[:, i],
                    upscale_factor,
                    1,
                    window=('kaiser', filter_size)
                )

        # Apply more nuanced frequency enhancement
        if processed_audio.ndim == 1:
            # Mono
            spectrum = np.fft.rfft(processed_audio)
            freq_bins = len(spectrum)

            # Boost high frequencies (more subtle)
            high_boost_start = int(freq_bins * boost_start_freq)
            high_boost_factor = 1.0 + high_boost

            # Create a gradual transition for high boost
            high_boost_range = freq_bins - high_boost_start
            high_boost_curve = np.linspace(1.0, high_boost_factor, high_boost_range)
            spectrum[high_boost_start:] *= high_boost_curve

            # Add warmth to low frequencies
            low_boost_end = int(freq_bins * low_freq_end)
            low_boost_factor = 1.0 + low_boost

            # Create a gradual transition for low boost
            low_boost_curve = np.linspace(low_boost_factor, 1.0, low_boost_end)
            spectrum[1:low_boost_end+1] *= low_boost_curve  # Skip DC component (index 0)

            # Apply back to time domain
            processed_audio = np.fft.irfft(spectrum, len(processed_audio))
        else:
            # Stereo or multi-channel
            for i in range(processed_audio.shape[1]):
                spectrum = np.fft.rfft(processed_audio[:, i])
                freq_bins = len(spectrum)

                # Boost high frequencies (more subtle)
                high_boost_start = int(freq_bins * boost_start_freq)
                high_boost_factor = 1.0 + high_boost

                # Create a gradual transition for high boost
                high_boost_range = freq_bins - high_boost_start
                high_boost_curve = np.linspace(1.0, high_boost_factor, high_boost_range)
                spectrum[high_boost_start:] *= high_boost_curve

                # Add warmth to low frequencies
                low_boost_end = int(freq_bins * low_freq_end)
                low_boost_factor = 1.0 + low_boost

                # Create a gradual transition for low boost
                low_boost_curve = np.linspace(low_boost_factor, 1.0, low_boost_end)
                spectrum[1:low_boost_end+1] *= low_boost_curve  # Skip DC component (index 0)

                # Apply back to time domain
                processed_audio[:, i] = np.fft.irfft(spectrum, len(processed_audio[:, i]))

        # Apply a gentle limiter to prevent clipping
        max_val = np.max(np.abs(processed_audio))
        if max_val > 0.95:  # If close to clipping
            logging.info(f"Applying limiter (peak value: {max_val:.3f})")
            # Apply soft knee limiting
            scale_factor = 0.95 / max_val
            processed_audio *= scale_factor
            logging.info(f"New peak value: {np.max(np.abs(processed_audio)):.3f}")

        process_time = time.time() - start_time
        process_end_cpu = psutil.cpu_percent(interval=0.1)
        process_end_memory = psutil.virtual_memory().percent

        logging.info(f"Processing completed in {process_time:.2f} seconds")
        logging.info(f"CPU usage during processing: {process_start_cpu}% -> {process_end_cpu}%")
        logging.info(f"Memory usage during processing: {process_start_memory}% -> {process_end_memory}%")

        # Create output filenames with simpler names to avoid encoding issues
        output_file = os.path.join(output_dir, "upscaled.wav")
        output_file_natural = os.path.join(output_dir, "natural.wav")

        # First, create the natural version (original sample rate)
        logging.info(f"Creating a version with original sample rate")

        # Apply the frequency enhancements to the original audio
        natural_audio = audio_data.copy()

        # Apply the same frequency enhancements to the natural version
        if natural_audio.ndim == 1:
            # Mono
            spectrum = np.fft.rfft(natural_audio)
            freq_bins = len(spectrum)

            # Boost high frequencies (more subtle)
            high_boost_start = int(freq_bins * boost_start_freq)
            high_boost_factor = 1.0 + high_boost

            # Create a gradual transition for high boost
            high_boost_range = freq_bins - high_boost_start
            high_boost_curve = np.linspace(1.0, high_boost_factor, high_boost_range)
            spectrum[high_boost_start:] *= high_boost_curve

            # Add warmth to low frequencies
            low_boost_end = int(freq_bins * low_freq_end)
            low_boost_factor = 1.0 + low_boost

            # Create a gradual transition for low boost
            low_boost_curve = np.linspace(low_boost_factor, 1.0, low_boost_end)
            spectrum[1:low_boost_end+1] *= low_boost_curve  # Skip DC component (index 0)

            # Apply back to time domain
            natural_audio = np.fft.irfft(spectrum, len(natural_audio))
        else:
            # Stereo or multi-channel
            for i in range(natural_audio.shape[1]):
                spectrum = np.fft.rfft(natural_audio[:, i])
                freq_bins = len(spectrum)

                # Boost high frequencies (more subtle)
                high_boost_start = int(freq_bins * boost_start_freq)
                high_boost_factor = 1.0 + high_boost

                # Create a gradual transition for high boost
                high_boost_range = freq_bins - high_boost_start
                high_boost_curve = np.linspace(1.0, high_boost_factor, high_boost_range)
                spectrum[high_boost_start:] *= high_boost_curve

                # Add warmth to low frequencies
                low_boost_end = int(freq_bins * low_freq_end)
                low_boost_factor = 1.0 + low_boost

                # Create a gradual transition for low boost
                low_boost_curve = np.linspace(low_boost_factor, 1.0, low_boost_end)
                spectrum[1:low_boost_end+1] *= low_boost_curve  # Skip DC component (index 0)

                # Apply back to time domain
                natural_audio[:, i] = np.fft.irfft(spectrum, len(natural_audio[:, i]))

        # Apply a gentle limiter to prevent clipping
        max_val = np.max(np.abs(natural_audio))
        if max_val > 0.95:  # If close to clipping
            logging.info(f"Applying limiter to natural version (peak value: {max_val:.3f})")
            # Apply soft knee limiting
            scale_factor = 0.95 / max_val
            natural_audio *= scale_factor
            logging.info(f"New peak value for natural version: {np.max(np.abs(natural_audio)):.3f}")

        # Save the natural version first
        try:
            logging.info(f"Saving natural version to: {output_file_natural}")
            save_start_time = time.time()
            sf.write(output_file_natural, natural_audio, sample_rate)
            save_time = time.time() - save_start_time
            logging.info(f"Natural version saved in {save_time:.2f} seconds (sample rate: {sample_rate}Hz)")
            natural_version_saved = True
        except Exception as e:
            logging.error(f"Error saving natural version: {e}")
            natural_version_saved = False

        # Now try to save the upscaled version
        try:
            logging.info(f"Saving upscaled version to: {output_file}")
            save_start_time = time.time()
            sf.write(output_file, processed_audio, sample_rate * upscale_factor)
            save_time = time.time() - save_start_time
            logging.info(f"Upscaled version saved in {save_time:.2f} seconds (sample rate: {sample_rate * upscale_factor}Hz)")
            upscaled_version_saved = True
        except Exception as e:
            logging.error(f"Error saving upscaled version: {e}")
            upscaled_version_saved = False

        # Get file sizes if files were saved successfully
        input_size_mb = os.path.getsize(input_file) / (1024 * 1024)
        logging.info(f"Input file size: {input_size_mb:.2f} MB")

        if natural_version_saved:
            natural_size_mb = os.path.getsize(output_file_natural) / (1024 * 1024)
            logging.info(f"Natural version file size: {natural_size_mb:.2f} MB")

        if upscaled_version_saved:
            upscaled_size_mb = os.path.getsize(output_file) / (1024 * 1024)
            logging.info(f"Upscaled version file size: {upscaled_size_mb:.2f} MB")

        logging.info("Processing complete!")
        logging.info(f"Original sample rate: {sample_rate}Hz")
        logging.info(f"Upscaled sample rate: {sample_rate * upscale_factor}Hz")
        logging.info(f"Upscale factor: {upscale_factor}x")

        # Try to play the natural version file if it was saved successfully
        if natural_version_saved:
            try:
                logging.info("Playing natural version file...")
                if sys.platform == "win32":
                    os.startfile(output_file_natural)
                    logging.info("Natural version file opened in default media player")

                    # Also provide the option to play the high-res version if it was saved
                    if upscaled_version_saved:
                        logging.info(f"\nTwo versions have been created:")
                        logging.info(f"1. Natural version (44.1kHz): {output_file_natural}")
                        logging.info(f"2. High-resolution version (88.2kHz): {output_file}")
                        logging.info(f"\nThe natural version is playing now. To play the high-resolution version, open:")
                        logging.info(f"{output_file}")
                else:
                    logging.info("Automatic playback not supported on this platform")
            except Exception as e:
                logging.error(f"Error playing file: {e}")
        elif upscaled_version_saved:
            # If natural version failed but upscaled version succeeded, try to play that
            try:
                logging.info("Playing upscaled version file...")
                if sys.platform == "win32":
                    os.startfile(output_file)
                    logging.info("Upscaled version file opened in default media player")
                else:
                    logging.info("Automatic playback not supported on this platform")
            except Exception as e:
                logging.error(f"Error playing file: {e}")

        logging.info(f"Log file created at: {log_file}")

        return 0

    except Exception as e:
        logging.error(f"Error processing audio: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())
