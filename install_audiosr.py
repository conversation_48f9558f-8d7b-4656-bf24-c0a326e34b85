"""
<PERSON><PERSON><PERSON> to install the AudioSR module and its dependencies
"""

import sys
import subprocess
import arg<PERSON><PERSON>

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("Warning: Python 3.8 or higher is recommended.")
        return False
    elif sys.version_info.major > 3 or sys.version_info.minor > 11:
        print("Warning: This script is tested with Python 3.8-3.11.")
        print(f"Your Python version is {sys.version_info.major}.{sys.version_info.minor}")
        return False
    return True

def install_package(package):
    """Install a package using pip"""
    print(f"Installing {package}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing {package}: {e}")
        return False

def install_audiosr():
    """Install AudioSR and its dependencies"""
    print("Installing AudioSR and dependencies...")

    # Install PyTorch first
    # Using the same command for all platforms for simplicity
    # For specific CUDA versions or optimizations, this could be customized
    torch_packages = ["torch", "torchvision", "torchaudio"]

    # Install PyTorch packages one by one
    for package in torch_packages:
        if not install_package(package):
            print(f"Failed to install {package}. Please install it manually.")
            return False

    # Install other dependencies
    dependencies = [
        "numpy",
        "scipy",
        "librosa",
        "soundfile",
        "diffusers",
        "transformers",
        "accelerate"
    ]

    for dep in dependencies:
        if not install_package(dep):
            print(f"Failed to install {dep}. Please install it manually.")

    # Install AudioSR from GitHub
    print("Installing AudioSR from GitHub...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install",
            "git+https://github.com/haoheliu/versatile_audio_super_resolution.git"
        ])
        print("AudioSR installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing AudioSR: {e}")
        print("Please install it manually:")
        print("pip install git+https://github.com/haoheliu/versatile_audio_super_resolution.git")
        return False

def main():
    parser = argparse.ArgumentParser(description="Install AudioSR and its dependencies")
    parser.add_argument('--skip-checks', action='store_true', help="Skip Python version check")

    args = parser.parse_args()

    # Check Python version
    if not args.skip_checks and not check_python_version():
        if input("Continue anyway? (y/n): ").lower() != 'y':
            sys.exit(1)

    # Install AudioSR
    if install_audiosr():
        print("\nInstallation complete! You can now use AudioSR.")
        print("Try running: python process_audio_sr.py <input_file>")
    else:
        print("\nInstallation failed. Please check the error messages and try again.")
        sys.exit(1)

    return 0

if __name__ == "__main__":
    sys.exit(main())
