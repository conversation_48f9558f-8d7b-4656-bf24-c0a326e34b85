# Samsung Screen Mirroring Application with scrcpy Integration

A comprehensive tool that enables screen mirroring for Samsung devices (particularly the A22) by exploiting vulnerabilities or using alternative connection methods. This project integrates scrcpy for high-quality screen mirroring and provides a unified interface for interacting with Samsung devices.

## Project Structure

- **src/**: Core source code
  - Exploit implementation
  - Screen mirroring logic
  - Device communication
  - scrcpy integration code

- **scrcpy/**: scrcpy integration components
  - scrcpy binaries and libraries
  - Custom scrcpy configurations
  - Integration scripts

- **gui/**: Graphical user interface components
  - Control interface
  - Device selection
  - Connection management
  - Screen interaction tools

- **scripts/**: Utility scripts
  - Exploit scripts
  - Installation scripts
  - Development utilities
  - Connection scripts

- **utils/**: Helper utilities
  - ADB wrappers
  - Device detection
  - Error handling
  - USB connection utilities

- **docs/**: Documentation
  - User guides
  - Technical documentation
  - Security considerations
  - Connection methods

## Key Features

- Mimics HDMI-to-OTG connections for Samsung devices
- Integrates with scrcpy for high-quality screen mirroring
- GUI for interacting with the Samsung device
- Exploits Samsung TTS vulnerability to enable USB debugging
- Works without requiring user interaction on the device
- Multiple connection methods (USB, wireless, exploit)
- Support for Samsung A22 and other Samsung devices

## Related Files

### Core Components
- **samsung_exploit.bat**: Windows batch script for exploiting Samsung devices
- **samsung_exploit.sh**: Linux/macOS shell script for the same purpose
- **main.js**: Main application logic with scrcpy integration

### Documentation
- **README_SAMSUNG_EXPLOIT.md**: Documentation for the exploit method
- **README_SAMSUNG_OTG.md**: Documentation for OTG connection method
- **README_SAMSUNG_USB.md**: Documentation for USB connection method

## Usage

See the documentation in the `docs/` directory for detailed usage instructions.

## Security Considerations

This application uses a known vulnerability in Samsung's TTS engine. While it's being used here for legitimate screen mirroring purposes, it could potentially be used for malicious purposes. Use this tool responsibly and only on devices you own or have permission to access.

## Limitations

- This exploit may not work on all Samsung devices or OS versions
- Samsung may patch this vulnerability in future updates
- The exploit requires temporary installation of a vulnerable TTS app
