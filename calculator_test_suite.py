import time
import random
import decimal
from calculator_improved import Calculator, CalculatorError

def run_basic_tests(calc):
    """Run basic arithmetic tests"""
    print("\n=== BASIC ARITHMETIC TESTS ===")

    test_cases = [
        # Basic operations
        ("2 + 3", "5"),
        ("5-3", "2"),  # No spaces to test lexer
        ("4 * 5", "20"),
        ("20 / 4", "5"),
        ("2 ^ 3", "8"),

        # Order of operations
        ("2 + 3 * 4", "14"),
        ("(2 + 3) * 4", "20"),
        ("2 * 3 + 4 * 5", "26"),
        ("2 + 3 * 4 ^ 2", "50"),
        ("(2 + 3) * (4 + 5)", "45"),

        # Negative numbers
        ("-5 + 3", "-2"),
        ("5 + -3", "2"),
        ("-5 * -3", "15"),
        ("-5 * 3", "-15"),
        ("5 * -3", "-15"),

        # Decimal numbers
        ("0.5 + 0.5", "1"),
        ("0.1 + 0.2", "0.3"),
        ("0.3 * 0.3", "0.09"),
        ("1 / 3", "0.3333333333333333333333333333333"),
        ("0.1 * 0.1", "0.01"),

        # Scientific notation
        ("2.5e3", "2500"),
        ("2.5e-3", "0.0025"),
        ("2.5e3 + 3.5e2", "2850"),
        ("2.5e-3 * 1000", "2.5"),
        ("1e10 / 1e5", "100000"),
    ]

    run_test_cases(calc, test_cases)

def run_function_tests(calc):
    """Run tests for mathematical functions"""
    print("\n=== FUNCTION TESTS ===")

    test_cases = [
        # Basic function tests
        ("sin(0)", "0"),
        ("cos(0)", "1"),
        ("tan(0)", "0"),
        ("log(100)", "2"),
        ("ln(1)", "0"),
        ("sqrt(16)", "4"),
        ("abs(-5)", "5"),

        # Nested functions
        ("sin(cos(0))", "0.8414709848078965066525023216303"),
        ("log(sqrt(100))", "1"),
        ("sqrt(log(10000))", "2"),
        ("abs(sin(-3.14159))", "0.0000026535897933379732"),

        # Functions with expressions
        ("sin(3.14159 / 2)", "1"),
        ("cos(3.14159 * 2)", "1"),
        ("log(10 ^ 3)", "3"),
        ("sqrt(5 ^ 2 + 12 ^ 2)", "13"),
        ("abs(5 - 10)", "5"),
    ]

    run_test_cases(calc, test_cases)

def run_implicit_multiplication_tests(calc):
    """Run tests for implicit multiplication"""
    print("\n=== IMPLICIT MULTIPLICATION TESTS ===")

    test_cases = [
        # Number followed by parenthesis
        ("2(3)", "6"),
        ("2(3+4)", "14"),
        ("5(2-1)", "5"),
        ("10(20)(30)", "6000"),

        # Parenthesis followed by parenthesis
        ("(2)(3)", "6"),
        ("(2+3)(4+5)", "45"),
        ("(2*3)(4*5)", "120"),
        ("(2+3)(4+5)(1+2)", "135"),

        # Number followed by function
        ("2sin(0)", "0"),
        ("2cos(0)", "2"),
        ("3log(1000)", "9"),
        ("4sqrt(16)", "16"),

        # Parenthesis followed by function
        ("(2)sin(0)", "0"),
        ("(2+3)cos(0)", "5"),
        ("(2*3)log(100)", "12"),

        # Function followed by function
        ("sin(0)cos(0)", "0"),
        ("log(10)log(10)", "1"),
        ("sqrt(4)sqrt(9)", "6"),

        # Complex combinations
        ("2(3+4)sin(0)cos(0)", "0"),
        ("2sin(3.14159/2)log(100)", "4"),
        ("(1+2)(3+4)sqrt(16)log(10000)", "84"),
        ("2(3)(4)(5)", "120"),
        ("2sin(0)(3+4)", "0"),
        ("(sin(0)+1)(cos(0)+2)", "3"),
    ]

    run_test_cases(calc, test_cases)

def run_edge_case_tests(calc):
    """Run tests for edge cases and potential problems"""
    print("\n=== EDGE CASE TESTS ===")

    test_cases = [
        # Very large numbers
        ("1e20 + 1e20", "2e+20"),
        ("1e20 * 1e20", "1e+40"),
        ("1e20 / 1e10", "1e+10"),

        # Very small numbers
        ("1e-20 + 1e-20", "2e-20"),
        ("1e-20 * 1e-20", "1e-40"),
        ("1e-20 / 1e10", "1e-30"),

        # Precision tests
        ("0.1 + 0.2 - 0.3", "0"),
        ("0.1 * 0.1 * 0.1 * 0.1", "0.0001"),
        ("1 / 3 * 3", "1"),

        # Operator precedence edge cases
        ("2 ^ 3 ^ 2", "512"),  # Right-associative: 2^(3^2)
        ("2 * 3 ^ 2", "18"),   # Exponentiation before multiplication
        ("2 + 3 * 4 ^ 2 - 1", "49"),

        # Parentheses edge cases
        ("((((2 + 3)) * 4))", "20"),
        ("(((2 + 3) * (4 + 5)))", "45"),

        # Mixed operations
        ("2 + 3 * 4 / 2 - 1", "7"),
        ("2 ^ 3 * 4 + 5 / 5", "33"),
        ("(2 + 3 * 4) ^ 2 / 2", "98"),

        # Whitespace variations
        ("2+3", "5"),
        ("2 +3", "5"),
        ("2+ 3", "5"),
        ("2    +    3", "5"),

        # Decimal point edge cases
        ("0.0 + 0.0", "0"),
        (".5 + .5", "1"),
        ("1. + 2.", "3"),
    ]

    run_test_cases(calc, test_cases)

def run_error_tests(calc):
    """Run tests that should produce errors"""
    print("\n=== ERROR HANDLING TESTS ===")

    error_cases = [
        # Division by zero
        "1 / 0",
        "5 / (2 - 2)",
        "1 / (sin(3.14159))",

        # Invalid syntax
        "2 + + 3",
        "2 * * 3",
        "2 + 3 )",
        "( 2 + 3",
        "2 + 3 + ",
        "+ 2 + 3",

        # Domain errors
        "sqrt(-1)",
        "log(-1)",
        "log(0)",
        "ln(-1)",
        "ln(0)",

        # Empty expressions
        "",
        "    ",

        # Invalid characters
        "2 + 3 $ 4",
        "hello",
        "2 > 3",
    ]

    for expr in error_cases:
        try:
            result = calc.evaluate(expr)
            print(f"❌ {expr} should have raised an error but got {result}")
        except CalculatorError as e:
            print(f"✅ {expr} correctly raised: {e.message}")

def generate_number(max_value=10):
    """Generate a random number as a string"""
    return str(random.randint(1, max_value))

def generate_binary_operation(depth, max_value):
    """Generate a binary operation expression"""
    operations = ['+', '-', '*', '/', '^']
    left = generate_complex_expression(depth - 1, max_value)
    right = generate_complex_expression(depth - 1, max_value)
    op = random.choice(operations)

    # Avoid division by zero or negative sqrt
    if op == '/' and right == '0':
        right = '1'
    if op == '^' and int(left) > 5:  # Avoid too large numbers
        left = '2'

    return f"({left} {op} {right})"

def generate_function_call(depth, max_value):
    """Generate a function call expression"""
    functions = ['sin', 'cos', 'log', 'sqrt', 'abs']
    func = random.choice(functions)
    expr = generate_complex_expression(depth - 1, max_value)

    # Avoid domain errors
    if func in ['log', 'sqrt'] and expr.startswith('-'):
        expr = expr[1:]  # Remove negative sign
    if func in ['log'] and expr == '0':
        expr = '1'

    return f"{func}({expr})"

def generate_parenthesized_expr(depth, max_value):
    """Generate a parenthesized expression"""
    return f"({generate_complex_expression(depth - 1, max_value)})"

def generate_complex_expression(depth=5, max_value=10):
    """Generate a complex expression with many operations"""
    # Base case: return a number
    if depth <= 0 or random.random() < 0.3:
        return generate_number(max_value)

    # Choose expression type based on random value
    r = random.random()
    if r < 0.4:  # Binary operation
        return generate_binary_operation(depth, max_value)
    elif r < 0.7:  # Function call
        return generate_function_call(depth, max_value)
    else:  # Parenthesized expression
        return generate_parenthesized_expr(depth, max_value)

def evaluate_expression(calc, expr, index):
    """Evaluate an expression and print the result"""
    try:
        start_time = time.time()
        result = calc.evaluate(expr)
        end_time = time.time()

        print(f"✅ Complex expression {index+1}: {expr} = {result}")
        print(f"   Evaluation time: {(end_time - start_time)*1000:.2f} ms")
    except CalculatorError as e:
        print(f"❌ Complex expression {index+1}: {expr} raised error: {e.message}")

def run_stress_tests(calc):
    """Run stress tests with complex expressions"""
    print("\n=== STRESS TESTS ===")

    # Generate and test 10 complex expressions
    for i in range(10):
        expr = generate_complex_expression()
        evaluate_expression(calc, expr, i)

def run_performance_tests(calc):
    """Run performance tests"""
    print("\n=== PERFORMANCE TESTS ===")

    # Test expressions of increasing complexity
    expressions = [
        "2 + 3",
        "2 + 3 * 4",
        "2 + 3 * 4 ^ 2",
        "(2 + 3) * (4 + 5)",
        "sin(cos(tan(0.5)))",
        "sqrt(1 + sqrt(2 + sqrt(3 + sqrt(4))))",
        "2(3)(4)(5)(6)(7)",
        "sin(0)cos(0)tan(0)log(10)ln(10)sqrt(10)",
        "((((2 + 3) * 4) / 2) ^ 2) + ((5 * 6) / (7 + 8))",
    ]

    for expr in expressions:
        # Run the expression multiple times to get a good average
        iterations = 100
        start_time = time.time()

        for _ in range(iterations):
            result = calc.evaluate(expr)

        end_time = time.time()
        avg_time = (end_time - start_time) * 1000 / iterations

        print(f"Expression: {expr}")
        print(f"Result: {result}")
        print(f"Average evaluation time: {avg_time:.4f} ms")
        print()

    # Test cache performance
    print("Cache performance test:")
    expr = "sin(cos(tan(0.5))) + sqrt(1 + sqrt(2 + sqrt(3 + sqrt(4))))"

    # First evaluation (no cache)
    start_time = time.time()
    calc.evaluate(expr)  # Result not needed, just measuring time
    first_time = (time.time() - start_time) * 1000

    # Second evaluation (with cache)
    start_time = time.time()
    calc.evaluate(expr)  # Result not needed, just measuring time
    second_time = (time.time() - start_time) * 1000

    print(f"Expression: {expr}")
    print(f"First evaluation: {first_time:.4f} ms")
    print(f"Second evaluation: {second_time:.4f} ms")
    print(f"Speedup: {first_time / second_time:.2f}x")

def run_test_cases(calc, test_cases):
    """Run a list of test cases and report results"""
    passed = 0
    failed = 0

    for expr, expected in test_cases:
        try:
            result = calc.evaluate(expr)

            # Convert to string for comparison, handling different formats
            result_str = str(result)

            # Check if the result matches the expected value
            # For floating point, we need to be careful about precision
            if result_str == expected or (
                    # Handle cases where expected is an integer but result has decimal point
                    expected.isdigit() and result == int(expected) and float(result).is_integer()):
                print(f"✅ {expr} = {result}")
                passed += 1
            else:
                print(f"❌ {expr} = {result}, expected {expected}")
                failed += 1

        except CalculatorError as e:
            print(f"❌ {expr} raised error: {e.message}")
            failed += 1

    print(f"\nResults: {passed} passed, {failed} failed")
    return passed, failed

def run_all_tests():
    """Run all test suites"""
    # Set higher precision for decimal calculations
    decimal.getcontext().prec = 32

    # Create calculator instance
    calc = Calculator()

    # Run all test suites
    run_basic_tests(calc)
    run_function_tests(calc)
    run_implicit_multiplication_tests(calc)
    run_edge_case_tests(calc)
    run_error_tests(calc)
    run_stress_tests(calc)
    run_performance_tests(calc)

    print("\n=== TEST SUMMARY ===")
    print("All test suites completed.")

if __name__ == "__main__":
    run_all_tests()
