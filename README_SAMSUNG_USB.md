# scrcpy for Samsung A22 without ADB

This modified version of scrcpy allows you to connect to a Samsung A22 (and potentially other Samsung devices) without requiring USB debugging to be enabled.

## How It Works

This version implements several protocols to trick the Samsung device into treating your Windows PC as a display:

1. **Samsung Smart View Protocol** - Emulates Samsung's screen mirroring protocol
2. **Samsung DeX Protocol** - Emulates the DeX desktop mode protocol
3. **HDMI-CEC Protocol** - Emulates an HDMI display connection
4. **USB HID Protocol** - Allows input control without ADB

## Requirements

- A Samsung A22 or similar Samsung device with the latest OS update
- Windows PC with USB port
- USB cable to connect the device

## Building from Source

1. Install the required dependencies:
   ```
   # For Ubuntu/Debian
   sudo apt install libusb-1.0-0-dev libsdl2-dev ffmpeg libavformat-dev libavcodec-dev libavutil-dev

   # For Windows (using MSYS2)
   pacman -S mingw-w64-x86_64-libusb mingw-w64-x86_64-SDL2 mingw-w64-x86_64-ffmpeg
   ```

2. Apply the patches to the scrcpy source code:
   ```
   # Copy all the new files to their respective directories
   cp app/src/usb/usb_display.h app/src/usb/
   cp app/src/usb/usb_display.c app/src/usb/
   cp app/src/usb/usb_connection.h app/src/usb/
   cp app/src/usb/usb_connection.c app/src/usb/
   cp app/src/usb/samsung_protocol.h app/src/usb/
   cp app/src/usb/samsung_protocol.c app/src/usb/

   # Modify the options.h file
   # Add the connection_mode enum and field to struct sc_options

   # Patch the main.c file with the content from main_patch.c
   # Add the USB display connection option

   # Update the meson.build file with the content from meson_patch.build
   # Add the new source files and libusb dependency
   ```

3. Build scrcpy:
   ```
   meson setup build --buildtype=release
   ninja -C build
   ```

## Usage

Run scrcpy with the `--samsung-usb` option to use the direct USB connection method:

```
scrcpy --samsung-usb
```

This will attempt to connect to your Samsung device without using ADB or requiring USB debugging.

## Troubleshooting

1. **Device not detected**
   - Make sure your device is connected via USB
   - Try different USB ports or cables
   - Ensure the device is unlocked

2. **Connection fails**
   - Some Samsung devices may have different protocol implementations
   - Try running with verbose logging: `scrcpy --samsung-usb -v`

3. **No display output**
   - The device might not be responding to the protocol
   - Try toggling the screen on the device
   - Try disconnecting and reconnecting the USB cable

## Limitations

- This is an experimental feature and may not work with all Samsung devices
- Audio support may be limited or unavailable
- Performance may be lower than with the standard ADB connection
- Some features like clipboard sharing may not work

## How It Bypasses USB Debugging

This implementation uses several techniques to bypass the need for USB debugging:

1. It emulates standard display protocols that Samsung devices recognize
2. It uses USB HID for input, which doesn't require special permissions
3. It exploits the fact that Samsung devices automatically trust certain display types
4. It implements the Smart View protocol which is normally used for wireless screen mirroring

## Security Considerations

This method essentially exploits the trust that Samsung devices place in external displays. While this allows for convenient screen mirroring without USB debugging, it also means that any device that can emulate these protocols could potentially access your screen content.

Always be cautious when connecting your device to unknown computers or USB ports.

## Credits

Based on the original scrcpy project by Genymobile: https://github.com/Genymobile/scrcpy
