"""
Process FLAC files in Downloads folder
"""

import os
import sys
import time
import platform
import subprocess

def list_flac_files(directory):
    """List all FLAC files in the directory"""
    flac_files = []
    
    for file in os.listdir(directory):
        if file.lower().endswith('.flac'):
            flac_files.append(os.path.join(directory, file))
    
    return flac_files

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def process_audio_file(input_file):
    """Process audio file with AudioSR"""
    try:
        # Try to import AudioSR
        try:
            from audiosr import build_model, super_resolution
            import soundfile as sf
            import numpy as np
        except ImportError as e:
            print(f"Error: Required module not found: {e}")
            print("Please install AudioSR and its dependencies manually.")
            return None
        
        # Apply Windows 11 optimizations
        try:
            from windows11_optimizations import Windows11Optimizer
            print("Applying Windows 11 optimizations...")
            optimizer = Windows11Optimizer()
            optimizations = optimizer.optimize_audio_processing()
            print("Optimization results:")
            for key, value in optimizations.items():
                print(f"  {key}: {value}")
        except ImportError:
            print("Windows 11 optimizations not available, continuing without them.")
        
        # Generate output filename
        input_dir = os.path.dirname(input_file)
        input_basename = os.path.basename(input_file)
        input_name, ext = os.path.splitext(input_basename)
        output_dir = os.path.join(input_dir, "audiosr_output")
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, f"{input_name}_SR.wav")
        
        # Build model
        print("Building model 'basic' on cpu...")
        start_time = time.time()
        model = build_model(model_name="basic", device="cpu")
        model_load_time = time.time() - start_time
        print(f"Model loaded in {model_load_time:.2f} seconds")
        
        # Process audio
        print(f"Processing audio: {input_file}")
        print("Parameters: guidance_scale=3.5, ddim_steps=10")
        process_start = time.time()
        
        waveform = super_resolution(
            model,
            input_file,
            guidance_scale=3.5,
            ddim_steps=10  # Fast mode
        )
        
        process_time = time.time() - process_start
        print(f"Audio processed in {process_time:.2f} seconds")
        
        # Save output
        print(f"Saving output to: {output_file}")
        sf.write(output_file, waveform, 48000)  # AudioSR outputs at 48kHz
        
        return output_file
        
    except Exception as e:
        print(f"Error processing audio: {e}")
        return None

def main():
    # Get Downloads folder
    downloads_dir = os.path.expanduser("~/Downloads")
    print(f"Searching for FLAC files in: {downloads_dir}")
    
    # List FLAC files
    flac_files = list_flac_files(downloads_dir)
    
    if not flac_files:
        print("No FLAC files found in Downloads folder.")
        return 1
    
    # Print available FLAC files
    print("Available FLAC files:")
    for i, file in enumerate(flac_files):
        print(f"{i+1}. {os.path.basename(file)}")
    
    # Ask user which file to process
    try:
        choice = int(input("\nEnter the number of the file to process (or 0 to exit): "))
        if choice == 0:
            return 0
        if choice < 1 or choice > len(flac_files):
            print("Invalid choice")
            return 1
        
        input_file = flac_files[choice-1]
        
        # Process audio
        print(f"\nProcessing: {os.path.basename(input_file)}")
        output_file = process_audio_file(input_file)
        
        if output_file is None:
            print("Audio processing failed.")
            return 1
        
        # Play the processed file
        print("\n=== Playing Processed Audio ===")
        play_audio_file(output_file)
        
    except ValueError:
        print("Invalid input")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
