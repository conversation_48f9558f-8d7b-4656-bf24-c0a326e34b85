@echo off
echo Processing "Dxrk ダーク - RAVE.flac" with Audio Super Resolution

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Check if AudioSR is installed
echo Checking if AudioSR is installed...
"%PYTHON_PATH%\python.exe" -c "import audiosr" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo AudioSR is not installed. Installing now...
    "%PYTHON_PATH%\python.exe" install_audiosr.py
)

REM Process the audio file
echo.
echo Processing audio file...
"%PYTHON_PATH%\python.exe" process_audio_sr.py "C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac" --model basic --device cpu --guidance-scale 3.5 --ddim-steps 30 --play-processed

echo.
echo Done!
pause
