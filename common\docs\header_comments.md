# Header Comments for AI Understanding

This document explains the standardized header comment format used in this project to make the codebase more maintainable and easier for AI to understand.

## Purpose

Header comments serve several important purposes:

1. **Project Context**: They identify which project and component a file belongs to
2. **File Purpose**: They explain what the file does and its role in the project
3. **Related Files**: They list other files that work together with this file
4. **Maintenance**: They track when the file was last modified

## Header Comment Format

Each source file includes a standardized header comment with the following structure:

### Python Files

```python
"""
Project Name - Component

File: example_file.py
Part of: project_name
Directory: component_directory

Description:
  This file is part of the Project Name.
  It belongs to the component_directory component of the project.

Related Files:
  - related_file1.py
  - related_file2.py
  - related_file3.py

Last Modified: YYYY-MM-DD
"""

# Actual code starts here
```

### JavaScript Files

```javascript
/**
 * Project Name - Component
 *
 * File: example-file.js
 * Part of: project_name
 * Directory: component_directory
 *
 * Description:
 *   This file is part of the Project Name.
 *   It belongs to the component_directory component of the project.
 *
 * Related Files:
 *   - related-file1.js
 *   - related-file2.js
 *   - related-file3.js
 *
 * Last Modified: YYYY-MM-DD
 */

// Actual code starts here
```

### Batch Files

```batch
REM Project Name - Component
REM
REM File: example_file.bat
REM Part of: project_name
REM Directory: component_directory
REM
REM Description:
REM   This file is part of the Project Name.
REM   It belongs to the component_directory component of the project.
REM
REM Related Files:
REM   - related_file1.bat
REM   - related_file2.bat
REM   - related_file3.bat
REM
REM Last Modified: YYYY-MM-DD

@echo off
REM Actual code starts here
```

## Benefits for AI Understanding

These standardized header comments help AI systems in several ways:

1. **Context Awareness**: AI can quickly understand which project and component a file belongs to
2. **Relationship Mapping**: AI can identify related files that work together
3. **Purpose Identification**: AI can understand the purpose of the file without analyzing the entire codebase
4. **Maintenance Tracking**: AI can identify when files were last modified

## Implementation

The header comments are automatically added by the `reorganize_files.py` script when files are moved to their appropriate directories. The script:

1. Identifies the file type based on its extension
2. Generates an appropriate header comment based on the file type
3. Adds the header comment to the beginning of the file
4. Lists related files from the same directory

## Customization

If you need to customize the header comment for a specific file, you can edit it manually. However, try to maintain the same structure to ensure consistency across the codebase.
