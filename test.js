const { PythonShell } = require('python-shell');
const path = require('path');
const fs = require('fs');

// Test OCR functionality
function testOCR() {
  console.log('Testing OCR functionality...');
  
  // Create test image with text
  // In a real test, you would use a real image file
  const testImagePath = path.join(__dirname, 'test-image.png');
  
  // Check if Python scripts exist
  const ocrScriptPath = path.join(__dirname, 'python', 'ocr_engine.py');
  if (!fs.existsSync(ocrScriptPath)) {
    console.error(`OCR script not found at ${ocrScriptPath}`);
    return;
  }
  
  // Run OCR script
  const options = {
    mode: 'text',
    pythonPath: 'python', // Adjust based on your Python installation
    pythonOptions: ['-u'],
    scriptPath: path.join(__dirname, 'python'),
    args: [testImagePath]
  };
  
  PythonShell.run('ocr_engine.py', options, (err, results) => {
    if (err) {
      console.error('OCR test error:', err);
      return;
    }
    
    console.log('OCR test results:', results);
  });
}

// Test solution generation
function testSolutionGeneration() {
  console.log('Testing solution generation...');
  
  const testProblem = 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.';
  
  // Check if Python script exists
  const solutionScriptPath = path.join(__dirname, 'python', 'solution_generator.py');
  if (!fs.existsSync(solutionScriptPath)) {
    console.error(`Solution generator script not found at ${solutionScriptPath}`);
    return;
  }
  
  // Run solution generator script
  const options = {
    mode: 'json',
    pythonPath: 'python', // Adjust based on your Python installation
    pythonOptions: ['-u'],
    scriptPath: path.join(__dirname, 'python'),
    args: [testProblem]
  };
  
  PythonShell.run('solution_generator.py', options, (err, results) => {
    if (err) {
      console.error('Solution generation test error:', err);
      return;
    }
    
    console.log('Solution generation test results:', JSON.stringify(results, null, 2));
  });
}

// Run tests
console.log('Running tests...');
// testOCR(); // Uncomment to test OCR (requires a test image)
testSolutionGeneration();
