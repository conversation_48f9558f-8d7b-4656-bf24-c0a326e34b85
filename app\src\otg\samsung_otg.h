#ifndef SC_SAMSUNG_OTG_H
#define SC_SAMSUNG_OTG_H

#include <stdbool.h>
#include <stdint.h>
#include "common.h"
#include "util/intr.h"

// Samsung-specific OTG mode
struct sc_samsung_otg {
    // Device info
    char *device_name;
    uint16_t width;
    uint16_t height;
    
    // Connection state
    bool connected;
    
    // Display protocol state
    bool display_connected;
    
    // HID state
    bool hid_registered;
};

// Initialize Samsung OTG mode
bool
sc_samsung_otg_init(struct sc_samsung_otg *otg);

// Connect to Samsung device
bool
sc_samsung_otg_connect(struct sc_samsung_otg *otg, struct sc_intr *intr);

// Disconnect from device
void
sc_samsung_otg_disconnect(struct sc_samsung_otg *otg);

// Clean up resources
void
sc_samsung_otg_destroy(struct sc_samsung_otg *otg);

// Register HID device
bool
sc_samsung_otg_register_hid(struct sc_samsung_otg *otg);

// Send HID event
bool
sc_samsung_otg_send_hid_event(struct sc_samsung_otg *otg, 
                             const unsigned char *buffer, size_t len);

// Enable display mode
bool
sc_samsung_otg_enable_display(struct sc_samsung_otg *otg);

// Check if connected
bool
sc_samsung_otg_is_connected(struct sc_samsung_otg *otg);

#endif // SC_SAMSUNG_OTG_H
