const { app, BrowserWindow, globalShortcut, ipcMain, screen } = require('electron');
const path = require('path');
const fs = require('fs');
const screenshot = require('electron-screenshot-app');
const ServiceManager = require('./services/service-manager');
const config = require('./config');

// Keep references to prevent garbage collection
let mainWindow;
let overlayWindow;
let isOverlayVisible = false;
let serviceManager;

// Create main application window
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    },
    title: 'Interview Assistant',
    icon: path.join(__dirname, 'assets/icon.png')
  });

  mainWindow.loadFile('src/index.html');
  
  // Open DevTools in development
  // mainWindow.webContents.openDevTools();

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Create transparent overlay window
function createOverlayWindow() {
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;
  
  overlayWindow = new BrowserWindow({
    width: 400,
    height: 300,
    x: width - 450,
    y: height - 350,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  overlayWindow.loadFile('src/overlay.html');
  overlayWindow.setIgnoreMouseEvents(true);
  overlayWindow.setOpacity(0); // Initially hidden
  
  overlayWindow.on('closed', () => {
    overlayWindow = null;
  });
}

// Initialize app
app.whenReady().then(() => {
  // Initialize service manager
  serviceManager = new ServiceManager();
  
  // Override the emitSolutionEvent method to send events to renderer
  serviceManager.emitSolutionEvent = (eventName, solution) => {
    if (overlayWindow && !overlayWindow.isDestroyed()) {
      overlayWindow.webContents.send(eventName, solution);
    }
  };
  
  createMainWindow();
  createOverlayWindow();
  
  // Register global shortcuts
  registerShortcuts();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
      createOverlayWindow();
    }
  });
});

// Register global keyboard shortcuts
function registerShortcuts() {
  // ⌘+H / Ctrl+H: Capture screenshot and generate solution
  globalShortcut.register('CommandOrControl+H', captureAndProcess);
  
  // ⌘+B / Ctrl+B: Toggle solution overlay visibility
  globalShortcut.register('CommandOrControl+B', toggleOverlay);
  
  // Arrow keys for positioning overlay (when visible)
  globalShortcut.register('CommandOrControl+Up', () => moveOverlay('up'));
  globalShortcut.register('CommandOrControl+Down', () => moveOverlay('down'));
  globalShortcut.register('CommandOrControl+Left', () => moveOverlay('left'));
  globalShortcut.register('CommandOrControl+Right', () => moveOverlay('right'));
  
  // Model switching shortcuts
  globalShortcut.register('CommandOrControl+M', rotateGroqModel);
  globalShortcut.register('CommandOrControl+A', rotateGroqAccount);
}

// Capture screenshot and process it
async function captureAndProcess() {
  try {
    // Update status
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('status-update', 'Capturing screenshot...');
    }
    
    // Capture screenshot
    const timestamp = Date.now();
    const screenshotPath = path.join(
      serviceManager.screenshotDir, 
      `screenshot_${timestamp}.png`
    );
    
    screenshot.saveScreenshot('fullscreen', screenshotPath, async (error, complete) => {
      if (error) {
        console.error('Screenshot error:', error);
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('status-update', `Screenshot error: ${error.message}`);
        }
        return;
      }
      
      if (complete) {
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('status-update', 'Processing screenshot with OCR...');
        }
        
        try {
          // Process the screenshot with Mistral OCR
          const extractedText = await serviceManager.processScreenshot(screenshotPath);
          
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('status-update', 'Generating solution...');
          }
          
          // Generate solution
          generateSolution(extractedText);
        } catch (ocrError) {
          console.error('OCR processing error:', ocrError);
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('status-update', `OCR error: ${ocrError.message}`);
          }
        }
      }
    });
  } catch (error) {
    console.error('Error capturing screenshot:', error);
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('status-update', `Error: ${error.message}`);
    }
  }
}

// Generate solution using AI
async function generateSolution(problemText) {
  try {
    // Generate solution with service manager
    const solution = await serviceManager.generateSolution(problemText, {
      useLocalModel: true,
      useGroq: true,
      useBitNet: true,
      cacheResults: true
    });
    
    // Send solution to overlay window
    if (overlayWindow && !overlayWindow.isDestroyed()) {
      overlayWindow.webContents.send('solution-ready', solution);
    }
    
    // Update status
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('status-update', 'Solution ready!');
    }
    
    // Make overlay visible
    if (!isOverlayVisible) {
      toggleOverlay();
    }
  } catch (error) {
    console.error('Solution generation error:', error);
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('status-update', `Solution error: ${error.message}`);
    }
  }
}

// Toggle overlay visibility
function toggleOverlay() {
  isOverlayVisible = !isOverlayVisible;
  
  if (overlayWindow && !overlayWindow.isDestroyed()) {
    if (isOverlayVisible) {
      overlayWindow.setOpacity(config.app.defaultOpacity);
      overlayWindow.setIgnoreMouseEvents(false, { forward: true });
    } else {
      overlayWindow.setOpacity(0);
      overlayWindow.setIgnoreMouseEvents(true);
    }
    
    // Send visibility state to overlay window
    overlayWindow.webContents.send('overlay-visibility-changed', isOverlayVisible);
  }
}

// Move overlay window
function moveOverlay(direction) {
  if (!isOverlayVisible || !overlayWindow || overlayWindow.isDestroyed()) return;
  
  const [x, y] = overlayWindow.getPosition();
  const step = 10;
  
  switch (direction) {
    case 'up':
      overlayWindow.setPosition(x, y - step);
      break;
    case 'down':
      overlayWindow.setPosition(x, y + step);
      break;
    case 'left':
      overlayWindow.setPosition(x - step, y);
      break;
    case 'right':
      overlayWindow.setPosition(x + step, y);
      break;
  }
}

// Rotate Groq model
function rotateGroqModel() {
  if (!serviceManager) return;
  
  serviceManager.rotateGroqModel();
  
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('status-update', 'Switched to next Groq model');
  }
}

// Rotate Groq account
function rotateGroqAccount() {
  if (!serviceManager) return;
  
  serviceManager.rotateGroqAccount();
  
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('status-update', 'Switched to next Groq account');
  }
}

// IPC communication handlers
ipcMain.on('toggle-overlay', toggleOverlay);

ipcMain.on('set-overlay-opacity', (event, opacity) => {
  config.app.defaultOpacity = opacity;
  
  if (isOverlayVisible && overlayWindow && !overlayWindow.isDestroyed()) {
    overlayWindow.setOpacity(opacity);
  }
});

ipcMain.on('switch-groq-model', (event, modelName) => {
  if (!serviceManager) return;
  
  serviceManager.switchGroqModel(modelName);
  
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('status-update', `Switched to Groq model: ${modelName}`);
  }
});

// Quit app when all windows are closed (except on macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Unregister shortcuts when app is about to quit
app.on('will-quit', () => {
  globalShortcut.unregisterAll();
});
