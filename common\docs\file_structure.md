# Project File Structure

This document explains the file structure of the project and how it's organized.

## Overview

The project is organized into four main components, each with its own directory:

1. **Audio Processing Suite** (`audio_processing/`)
2. **Interview Assistant Application** (`interview_assistant/`)
3. **Calculator Application** (`calculator/`)
4. **Samsung Screen Mirroring** (`samsung_helper/`)

Additionally, there's a `common/` directory for shared utilities and resources.

## Directory Structure

Each project directory follows a similar structure:

```
project_name/
├── src/           # Core source code
├── scripts/       # Utility scripts
├── utils/         # Helper utilities
├── gui/           # GUI components (if applicable)
├── tests/         # Test files
└── docs/          # Documentation
```

## File Organization

### Audio Processing Suite

- **src/**: Core audio processing code
  - Windows 11 optimization code
  - Audio processing algorithms
  - Hardware acceleration

- **gui/**: GUI interfaces
  - Audio upscaler GUI
  - AudioSR GUI

- **scripts/**: Batch files and scripts
  - Installation scripts
  - Processing scripts

- **utils/**: Helper utilities
  - Audio playback utilities
  - File handling

- **tests/**: Test files
  - Windows 11 optimization tests
  - Audio processing tests

- **docs/**: Documentation
  - README files
  - User guides

### Interview Assistant Application

- **src/**: Core application code
  - OCR implementation
  - AI solution generation

- **electron/**: Electron-specific code
  - Main process
  - Renderer process
  - IPC communication

- **scripts/**: Utility scripts
  - Build scripts
  - Installation scripts

- **utils/**: Helper utilities
  - Screenshot utilities
  - Text processing

- **tests/**: Test files
  - Unit tests
  - Integration tests

- **docs/**: Documentation
  - README files
  - User guides

### Calculator Application

- **src/**: Core calculator logic
  - Mathematical functions
  - Error handling

- **gui/**: GUI interfaces
  - Sci-fi themed UI
  - Animation components

- **scripts/**: Utility scripts
  - Build scripts
  - Installation scripts

- **utils/**: Helper utilities
  - Number formatting
  - Theme management

- **tests/**: Test files
  - Unit tests
  - UI tests

- **docs/**: Documentation
  - README files
  - User guides

### Samsung Screen Mirroring

- **src/**: Core implementation
  - Exploit code
  - Screen mirroring logic

- **gui/**: GUI interfaces
  - Control interface
  - Device selection

- **scripts/**: Utility scripts
  - Exploit scripts
  - Installation scripts

- **utils/**: Helper utilities
  - ADB wrappers
  - Device detection

- **tests/**: Test files
  - Device compatibility tests
  - Integration tests

- **docs/**: Documentation
  - README files
  - Technical documentation

### Common Utilities

- **utils/**: Shared utility functions
  - System utilities
  - File handling

- **scripts/**: Shared scripts
  - Environment setup
  - Build utilities

- **docs/**: Common documentation
  - Project overview
  - Development guidelines

## File Naming Conventions

- Python files: `lowercase_with_underscores.py`
- JavaScript files: `camelCase.js`
- Batch files: `descriptive_name.bat`
- Shell scripts: `descriptive_name.sh`
- Documentation: `UPPERCASE_NAME.md` or `Title_Case.md`

## Header Comments

All source files include a standardized header comment that provides:

- The project and component the file belongs to
- A brief description of the file's purpose
- Related files that work together with this file
- Last modification date

This helps with maintainability and makes it easier for AI to understand the codebase.
