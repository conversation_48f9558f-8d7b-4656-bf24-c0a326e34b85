#include "samsung_otg.h"

#include <assert.h>
#include <string.h>
#include <libusb.h>
#include "util/log.h"

// Samsung USB VID
#define SAMSUNG_VID 0x04e8

// AOA protocol constants
#define AOA_GET_PROTOCOL        51
#define AOA_SEND_STRING         52
#define AOA_START               53
#define AOA_REGISTER_HID        54
#define AOA_UNREGISTER_HID      55
#define AOA_SET_HID_REPORT_DESC 56
#define AOA_SEND_HID_EVENT      57
#define AOA_SET_AUDIO_MODE      58

// AOA string indices
#define AOA_STRING_MANUFACTURER 0
#define AOA_STRING_MODEL        1
#define AOA_STRING_DESCRIPTION  2
#define AOA_STRING_VERSION      3
#define AOA_STRING_URI          4
#define AOA_STRING_SERIAL       5

// DisplayPort Alt Mode constants
#define DP_ALT_MODE_VID 0xFF01
#define DP_ALT_MODE_INDEX 0x0001

// USB transfer timeout in milliseconds
#define USB_TRANSFER_TIMEOUT 5000

// HID report descriptor for a simple mouse and keyboard
static const unsigned char hid_report_desc[] = {
    // Mouse report descriptor
    0x05, 0x01,        // Usage Page (Generic Desktop)
    0x09, 0x02,        // Usage (Mouse)
    0xA1, 0x01,        // Collection (Application)
    0x09, 0x01,        //   Usage (Pointer)
    0xA1, 0x00,        //   Collection (Physical)
    0x05, 0x09,        //     Usage Page (Button)
    0x19, 0x01,        //     Usage Minimum (Button 1)
    0x29, 0x03,        //     Usage Maximum (Button 3)
    0x15, 0x00,        //     Logical Minimum (0)
    0x25, 0x01,        //     Logical Maximum (1)
    0x95, 0x03,        //     Report Count (3)
    0x75, 0x01,        //     Report Size (1)
    0x81, 0x02,        //     Input (Data, Variable, Absolute)
    0x95, 0x01,        //     Report Count (1)
    0x75, 0x05,        //     Report Size (5)
    0x81, 0x03,        //     Input (Constant, Variable, Absolute)
    0x05, 0x01,        //     Usage Page (Generic Desktop)
    0x09, 0x30,        //     Usage (X)
    0x09, 0x31,        //     Usage (Y)
    0x15, 0x81,        //     Logical Minimum (-127)
    0x25, 0x7F,        //     Logical Maximum (127)
    0x75, 0x08,        //     Report Size (8)
    0x95, 0x02,        //     Report Count (2)
    0x81, 0x06,        //     Input (Data, Variable, Relative)
    0xC0,              //   End Collection
    0xC0,              // End Collection

    // Keyboard report descriptor
    0x05, 0x01,        // Usage Page (Generic Desktop)
    0x09, 0x06,        // Usage (Keyboard)
    0xA1, 0x01,        // Collection (Application)
    0x05, 0x07,        //   Usage Page (Keyboard)
    0x19, 0xE0,        //   Usage Minimum (Keyboard LeftControl)
    0x29, 0xE7,        //   Usage Maximum (Keyboard Right GUI)
    0x15, 0x00,        //   Logical Minimum (0)
    0x25, 0x01,        //   Logical Maximum (1)
    0x75, 0x01,        //   Report Size (1)
    0x95, 0x08,        //   Report Count (8)
    0x81, 0x02,        //   Input (Data, Variable, Absolute)
    0x95, 0x01,        //   Report Count (1)
    0x75, 0x08,        //   Report Size (8)
    0x81, 0x03,        //   Input (Constant, Variable, Absolute)
    0x95, 0x06,        //   Report Count (6)
    0x75, 0x08,        //   Report Size (8)
    0x15, 0x00,        //   Logical Minimum (0)
    0x25, 0x65,        //   Logical Maximum (101)
    0x05, 0x07,        //   Usage Page (Keyboard)
    0x19, 0x00,        //   Usage Minimum (Reserved (no event indicated))
    0x29, 0x65,        //   Usage Maximum (Keyboard Application)
    0x81, 0x00,        //   Input (Data, Array, Absolute)
    0xC0               // End Collection
};

// Static variables for USB connection
static libusb_context *usb_ctx = NULL;
static libusb_device_handle *device_handle = NULL;
static uint8_t interface_number = 0;

// Initialize Samsung OTG mode
bool
sc_samsung_otg_init(struct sc_samsung_otg *otg) {
    assert(otg);
    
    // Initialize libusb if not already initialized
    if (!usb_ctx) {
        int ret = libusb_init(&usb_ctx);
        if (ret < 0) {
            LOGE("Failed to initialize libusb: %s", libusb_error_name(ret));
            return false;
        }
        
        // Set debug level
#ifdef SC_DEBUG
        libusb_set_option(usb_ctx, LIBUSB_OPTION_LOG_LEVEL, LIBUSB_LOG_LEVEL_INFO);
#endif
    }
    
    otg->device_name = strdup("Samsung Device");
    otg->width = 1920;
    otg->height = 1080;
    otg->connected = false;
    otg->display_connected = false;
    otg->hid_registered = false;
    
    return true;
}

// Find Samsung device
static bool
find_samsung_device(void) {
    libusb_device **device_list;
    ssize_t device_count = libusb_get_device_list(usb_ctx, &device_list);
    if (device_count < 0) {
        LOGE("Failed to get USB device list: %s", 
             libusb_error_name((int) device_count));
        return false;
    }
    
    bool found = false;
    
    for (ssize_t i = 0; i < device_count; i++) {
        libusb_device *device = device_list[i];
        struct libusb_device_descriptor desc;
        
        int ret = libusb_get_device_descriptor(device, &desc);
        if (ret < 0) {
            LOGW("Failed to get device descriptor: %s", 
                 libusb_error_name(ret));
            continue;
        }
        
        // Check if this is a Samsung device
        if (desc.idVendor == SAMSUNG_VID) {
            LOGI("Found Samsung device (VID: %04x, PID: %04x)", 
                 desc.idVendor, desc.idProduct);
            
            // Try to open the device
            ret = libusb_open(device, &device_handle);
            if (ret < 0) {
                LOGW("Failed to open Samsung device: %s", 
                     libusb_error_name(ret));
                continue;
            }
            
            // Find a suitable interface
            struct libusb_config_descriptor *config;
            ret = libusb_get_active_config_descriptor(device, &config);
            if (ret < 0) {
                LOGW("Failed to get config descriptor: %s", 
                     libusb_error_name(ret));
                libusb_close(device_handle);
                device_handle = NULL;
                continue;
            }
            
            // Look for a suitable interface
            for (int j = 0; j < config->bNumInterfaces; j++) {
                const struct libusb_interface *interface = &config->interface[j];
                
                for (int k = 0; k < interface->num_altsetting; k++) {
                    const struct libusb_interface_descriptor *iface_desc = 
                        &interface->altsetting[k];
                    
                    // We'll use the first interface we find
                    interface_number = iface_desc->bInterfaceNumber;
                    
                    // Detach kernel driver if necessary
                    if (libusb_kernel_driver_active(device_handle, interface_number) == 1) {
                        ret = libusb_detach_kernel_driver(device_handle, interface_number);
                        if (ret < 0) {
                            LOGW("Failed to detach kernel driver: %s", 
                                 libusb_error_name(ret));
                            continue;
                        }
                    }
                    
                    ret = libusb_claim_interface(device_handle, interface_number);
                    if (ret < 0) {
                        LOGW("Failed to claim interface: %s", 
                             libusb_error_name(ret));
                        continue;
                    }
                    
                    found = true;
                    break;
                }
                
                if (found) break;
            }
            
            libusb_free_config_descriptor(config);
            
            if (found) break;
            
            // If we didn't find a suitable interface, close the device and continue
            libusb_close(device_handle);
            device_handle = NULL;
        }
    }
    
    libusb_free_device_list(device_list, 1);
    return found;
}

// Get AOA protocol version
static int
get_aoa_protocol(void) {
    uint16_t protocol;
    int ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_IN | LIBUSB_REQUEST_TYPE_VENDOR,
        AOA_GET_PROTOCOL,
        0, 0,
        (unsigned char *)&protocol, sizeof(protocol),
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGE("Failed to get AOA protocol version: %s", libusb_error_name(ret));
        return -1;
    }
    
    return protocol;
}

// Send AOA string
static bool
send_aoa_string(uint8_t index, const char *str) {
    int ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_OUT | LIBUSB_REQUEST_TYPE_VENDOR,
        AOA_SEND_STRING,
        0, index,
        (unsigned char *)str, strlen(str) + 1,
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGE("Failed to send AOA string: %s", libusb_error_name(ret));
        return false;
    }
    
    return true;
}

// Start AOA mode
static bool
start_aoa_mode(void) {
    int ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_OUT | LIBUSB_REQUEST_TYPE_VENDOR,
        AOA_START,
        0, 0,
        NULL, 0,
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGE("Failed to start AOA mode: %s", libusb_error_name(ret));
        return false;
    }
    
    return true;
}

// Enable DisplayPort Alt Mode
static bool
enable_dp_alt_mode(void) {
    // This is a simplified implementation - in a real implementation,
    // we would need to follow the USB Type-C Alt Mode specification
    // to properly negotiate DisplayPort Alt Mode
    
    // For now, we'll just send a vendor-specific control transfer
    // that might trigger the device to enable its display output
    
    int ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_OUT | LIBUSB_REQUEST_TYPE_VENDOR,
        0x01, // Vendor-specific request
        DP_ALT_MODE_VID, DP_ALT_MODE_INDEX,
        NULL, 0,
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGW("Failed to enable DP Alt Mode: %s", libusb_error_name(ret));
        // Continue anyway, as this might not be supported
        return false;
    }
    
    return true;
}

// Connect to Samsung device
bool
sc_samsung_otg_connect(struct sc_samsung_otg *otg, struct sc_intr *intr) {
    assert(otg);
    
    if (otg->connected) {
        // Already connected
        return true;
    }
    
    // Find and open Samsung device
    if (!find_samsung_device()) {
        LOGE("No compatible Samsung device found");
        return false;
    }
    
    // Get AOA protocol version
    int protocol = get_aoa_protocol();
    if (protocol < 0) {
        LOGE("Failed to get AOA protocol version");
        sc_samsung_otg_disconnect(otg);
        return false;
    }
    
    LOGI("Device supports AOA protocol version %d", protocol);
    
    if (protocol < 2) {
        LOGW("Device supports AOA protocol version %d, but version 2 is required for HID support", protocol);
        // Continue anyway, as we might still be able to use some features
    }
    
    // Send AOA identification strings
    // Note: We're intentionally not sending manufacturer and model strings
    // to avoid triggering the accessory app dialog
    send_aoa_string(AOA_STRING_DESCRIPTION, "Samsung Display Adapter");
    send_aoa_string(AOA_STRING_VERSION, "1.0");
    send_aoa_string(AOA_STRING_URI, "https://github.com/Genymobile/scrcpy");
    send_aoa_string(AOA_STRING_SERIAL, "0123456789");
    
    // Start AOA mode
    if (!start_aoa_mode()) {
        LOGE("Failed to start AOA mode");
        sc_samsung_otg_disconnect(otg);
        return false;
    }
    
    // We need to close and reopen the device after starting AOA mode
    // because the device will disconnect and reconnect with a different USB configuration
    libusb_close(device_handle);
    device_handle = NULL;
    
    // Wait for the device to reconnect
    LOGI("Waiting for device to reconnect in AOA mode...");
    sleep(2);
    
    // Find the device again
    if (!find_samsung_device()) {
        LOGE("Failed to find device after starting AOA mode");
        return false;
    }
    
    // Register HID device
    if (!sc_samsung_otg_register_hid(otg)) {
        LOGW("Failed to register HID device");
        // Continue anyway, as we might still be able to use some features
    }
    
    // Enable display mode
    if (!sc_samsung_otg_enable_display(otg)) {
        LOGW("Failed to enable display mode");
        // Continue anyway, as we might still be able to use some features
    }
    
    otg->connected = true;
    LOGI("Successfully connected to Samsung device in OTG mode");
    
    return true;
}

// Disconnect from device
void
sc_samsung_otg_disconnect(struct sc_samsung_otg *otg) {
    assert(otg);
    
    if (device_handle) {
        // Release interface
        libusb_release_interface(device_handle, interface_number);
        libusb_close(device_handle);
        device_handle = NULL;
    }
    
    otg->connected = false;
    otg->display_connected = false;
    otg->hid_registered = false;
}

// Clean up resources
void
sc_samsung_otg_destroy(struct sc_samsung_otg *otg) {
    assert(otg);
    
    sc_samsung_otg_disconnect(otg);
    
    free(otg->device_name);
    otg->device_name = NULL;
    
    if (usb_ctx) {
        libusb_exit(usb_ctx);
        usb_ctx = NULL;
    }
}

// Register HID device
bool
sc_samsung_otg_register_hid(struct sc_samsung_otg *otg) {
    assert(otg);
    
    if (!device_handle) {
        LOGE("No device handle available");
        return false;
    }
    
    // Register HID device
    int ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_OUT | LIBUSB_REQUEST_TYPE_VENDOR,
        AOA_REGISTER_HID,
        1, // HID ID
        sizeof(hid_report_desc), // Report descriptor length
        NULL, 0,
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGE("Failed to register HID device: %s", libusb_error_name(ret));
        return false;
    }
    
    // Send HID report descriptor
    ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_OUT | LIBUSB_REQUEST_TYPE_VENDOR,
        AOA_SET_HID_REPORT_DESC,
        1, // HID ID
        0, // Offset
        (unsigned char *)hid_report_desc, sizeof(hid_report_desc),
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGE("Failed to send HID report descriptor: %s", libusb_error_name(ret));
        return false;
    }
    
    otg->hid_registered = true;
    LOGI("Successfully registered HID device");
    
    return true;
}

// Send HID event
bool
sc_samsung_otg_send_hid_event(struct sc_samsung_otg *otg, 
                             const unsigned char *buffer, size_t len) {
    assert(otg);
    
    if (!device_handle || !otg->hid_registered) {
        LOGE("HID device not registered");
        return false;
    }
    
    int ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_OUT | LIBUSB_REQUEST_TYPE_VENDOR,
        AOA_SEND_HID_EVENT,
        1, // HID ID
        0, // Unused
        (unsigned char *)buffer, len,
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGE("Failed to send HID event: %s", libusb_error_name(ret));
        return false;
    }
    
    return true;
}

// Enable display mode
bool
sc_samsung_otg_enable_display(struct sc_samsung_otg *otg) {
    assert(otg);
    
    if (!device_handle) {
        LOGE("No device handle available");
        return false;
    }
    
    // Try to enable DisplayPort Alt Mode
    if (!enable_dp_alt_mode()) {
        LOGW("Failed to enable DisplayPort Alt Mode");
        // Continue anyway, as we might still be able to use some features
    }
    
    // Send HDMI-CEC commands to trick the device into thinking it's connected to a display
    // This is a simplified implementation - in a real implementation,
    // we would need to follow the HDMI-CEC specification
    
    // For now, we'll just send a vendor-specific control transfer
    // that might trigger the device to enable its display output
    
    int ret = libusb_control_transfer(
        device_handle,
        LIBUSB_ENDPOINT_OUT | LIBUSB_REQUEST_TYPE_VENDOR,
        0x02, // Vendor-specific request
        0x0001, 0x0000,
        NULL, 0,
        USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGW("Failed to send HDMI-CEC commands: %s", libusb_error_name(ret));
        // Continue anyway, as this might not be supported
        return false;
    }
    
    otg->display_connected = true;
    LOGI("Successfully enabled display mode");
    
    return true;
}

// Check if connected
bool
sc_samsung_otg_is_connected(struct sc_samsung_otg *otg) {
    assert(otg);
    return otg->connected;
}
