<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Interview Assistant</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>Interview Assistant</h1>
    </header>
    
    <main>
      <div class="instructions">
        <h2>How to Use</h2>
        <ul>
          <li><strong>⌘+H / Ctrl+H:</strong> Capture screenshot and generate solution</li>
          <li><strong>⌘+B / Ctrl+B:</strong> Toggle solution visibility</li>
          <li><strong>⌘+Arrow Keys / Ctrl+Arrow Keys:</strong> Move solution overlay</li>
        </ul>
      </div>
      
      <div class="status">
        <h2>Status</h2>
        <p id="status-text">Ready</p>
      </div>
      
      <div class="settings">
        <h2>Settings</h2>
        <div class="setting-group">
          <label for="overlay-opacity">Overlay Opacity:</label>
          <input type="range" id="overlay-opacity" min="0.1" max="1.0" step="0.1" value="0.9">
        </div>
      </div>
    </main>
    
    <footer>
      <p>Interview Assistant v1.0.0</p>
    </footer>
  </div>
  
  <script src="renderer.js"></script>
</body>
</html>
