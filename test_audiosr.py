#!/usr/bin/env python3
"""
Test script for AudioSR to verify that our fixes work.
"""

import os
import sys
import argparse
import numpy as np
import soundfile as sf
import tempfile

def create_test_audio():
    """Create a simple stereo test audio file."""
    print("Creating test audio file...")
    
    # Create a simple stereo sine wave
    duration = 3  # seconds
    sample_rate = 16000
    t = np.linspace(0, duration, int(duration * sample_rate), endpoint=False)
    
    # Left channel: 440 Hz sine
    left = 0.5 * np.sin(2 * np.pi * 440 * t)
    
    # Right channel: 880 Hz sine
    right = 0.5 * np.sin(2 * np.pi * 880 * t)
    
    # Combine into stereo
    stereo = np.column_stack((left, right))
    
    # Save to a temporary file
    temp_dir = tempfile.gettempdir()
    test_file = os.path.join(temp_dir, "audiosr_test.wav")
    sf.write(test_file, stereo, sample_rate)
    
    print(f"Test audio created: {test_file}")
    return test_file

def test_audiosr(test_file):
    """Test AudioSR with the test file."""
    try:
        from audiosr import build_model, super_resolution
    except ImportError:
        print("Error: AudioSR module not found. Please make sure it's installed.")
        return False
    
    print("Loading AudioSR model...")
    model = build_model(model_name="basic", device="cpu")
    
    print("Processing test audio...")
    output = super_resolution(
        model,
        test_file,
        guidance_scale=3.5,
        ddim_steps=10  # Use fewer steps for faster testing
    )
    
    # Check if output is stereo
    if len(output.shape) > 1 and output.shape[1] == 2:
        print("Success! Output is stereo.")
        
        # Save the output for verification
        output_file = os.path.join(tempfile.gettempdir(), "audiosr_test_output.wav")
        sf.write(output_file, output, 48000)
        print(f"Output saved to: {output_file}")
        
        return True
    else:
        print(f"Error: Output is not stereo. Shape: {output.shape}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Test AudioSR fixes")
    parser.add_argument('--input', help="Path to input audio file (optional)")
    
    args = parser.parse_args()
    
    if args.input:
        test_file = args.input
        print(f"Using provided test file: {test_file}")
    else:
        test_file = create_test_audio()
    
    if test_audiosr(test_file):
        print("Test passed!")
        return 0
    else:
        print("Test failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
