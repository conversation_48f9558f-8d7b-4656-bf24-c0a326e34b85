"""
Real-time Audio Upscaler
Processes system audio in real-time with upscaling
"""

import os
import sys
import time
import threading
import queue
import numpy as np
import tkinter as tk
from tkinter import ttk
import argparse

APP_NAME = "Real-time Audio Upscaler"

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import core modules
from core.audio_processor import AudioProcessor

class RealTimeAudioUpscaler:
    """Real-time audio upscaler for system audio"""

    def __init__(self, upscale_factor=2, quality_level=1, use_win11_opt=True, use_hardware_accel=True):
        self.upscale_factor = upscale_factor
        self.quality_level = quality_level
        self.use_win11_opt = use_win11_opt
        self.use_hardware_accel = use_hardware_accel

        self.audio_processor = AudioProcessor(
            use_win11_opt=self.use_win11_opt,
            use_hardware_accel=self.use_hardware_accel
        )

        self.input_buffer = queue.Queue()
        self.output_buffer = queue.Queue()

        self.running = False
        self.stop_requested = False
        self.stream = None

        self.stats = {
            'processed_chunks': 0,
            'total_processing_time': 0,
            'avg_processing_time': 0,
            'max_processing_time': 0,
            'buffer_underruns': 0,
            'start_time': 0,
        }

    def start(self):
        """Start real-time audio processing"""
        if self.running:
            print(f"{APP_NAME} is already running")
            return False

        try:
            import sounddevice as sd
            devices = sd.query_devices()
            default_input, default_output = sd.default.device
            print(f"Default input device: {devices[default_input]['name']}")
            print(f"Default output device: {devices[default_output]['name']}")

            self.sample_rate = int(devices[default_output]['default_samplerate'])
            self.channels = min(devices[default_input]['max_input_channels'],
                                devices[default_output]['max_output_channels'])
            self.blocksize = 1024

            print(f"Using sample rate: {self.sample_rate}Hz")
            print(f"Using channels: {self.channels}")
            print(f"Using blocksize: {self.blocksize}")

            self.input_buffer = queue.Queue()
            self.output_buffer = queue.Queue()

            self.stats = {
                'processed_chunks': 0,
                'total_processing_time': 0,
                'avg_processing_time': 0,
                'max_processing_time': 0,
                'buffer_underruns': 0,
                'start_time': time.time()
            }

            self.stop_requested = False
            self.processing_thread = threading.Thread(target=self._processing_thread, daemon=True)
            self.processing_thread.start()

            self.stream = sd.Stream(
                device=(default_input, default_output),
                samplerate=self.sample_rate,
                blocksize=self.blocksize,
                channels=self.channels,
                callback=self._audio_callback
            )
            self.stream.start()
            self.running = True

            print(f"{APP_NAME} started")
            return True

        except ImportError:
            print("Error: sounddevice module not found. Install with: pip install sounddevice")
            return False
        except Exception as e:
            print(f"Error starting {APP_NAME}: {e}")
            return False

    def stop(self):
        """Stop real-time audio processing"""
        if not self.running:
            print(f"{APP_NAME} is not running")
            return False

        try:
            self.stop_requested = True
            if self.stream:
                self.stream.stop()
                self.stream.close()
                self.stream = None
            if hasattr(self, 'processing_thread') and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=1.0)
            self.running = False

            duration = time.time() - self.stats['start_time']
            print(f"\n{APP_NAME} stopped")
            print(f"Ran for {duration:.2f} seconds")
            print(f"Processed {self.stats['processed_chunks']} chunks")
            print(f"Average processing time: {self.stats['avg_processing_time']*1000:.2f} ms")
            print(f"Maximum processing time: {self.stats['max_processing_time']*1000:.2f} ms")
            print(f"Buffer underruns: {self.stats['buffer_underruns']}")
            return True

        except Exception as e:
            print(f"Error stopping {APP_NAME}: {e}")
            return False

    def _audio_callback(self, indata, outdata, _, __, status):
        """
        Callback for audio stream.
        Args:
            indata: Input audio data
            outdata: Output audio buffer to fill
            _: Unused frames parameter
            __: Unused time_info parameter
            status: Stream status
        """
        if status:
            print(f"Status: {status}")
        self.input_buffer.put(indata.copy())
        try:
            if not self.output_buffer.empty():
                outdata[:] = self.output_buffer.get_nowait()
            else:
                outdata[:] = indata
                self.stats['buffer_underruns'] += 1
        except queue.Empty:
            outdata[:] = indata
            self.stats['buffer_underruns'] += 1

    def _resample(self, processed_chunk, target_shape):
        """Helper function to resample array to target shape using scipy if available."""
        try:
            from scipy import signal
            if processed_chunk.ndim == 1:
                return signal.resample(processed_chunk, target_shape[0])
            resampled = np.zeros(target_shape)
            for i in range(target_shape[1]):
                resampled[:, i] = signal.resample(processed_chunk[:, i], target_shape[0])
            return resampled
        except ImportError:
            # Fallback resampling
            if processed_chunk.ndim == 1:
                return np.interp(
                    np.linspace(0, len(processed_chunk) - 1, target_shape[0]),
                    np.arange(len(processed_chunk)),
                    processed_chunk
                )
            resampled = np.zeros(target_shape)
            for i in range(target_shape[1]):
                resampled[:, i] = np.interp(
                    np.linspace(0, len(processed_chunk[:, i]) - 1, target_shape[0]),
                    np.arange(len(processed_chunk[:, i])),
                    processed_chunk[:, i]
                )
            return resampled

    def _process_audio_chunk(self, audio_chunk):
        """Process a single audio chunk and update statistics."""
        start_time = time.time()
        processed = self.audio_processor.process_realtime(
            audio_chunk,
            self.sample_rate,
            upscale_factor=self.upscale_factor,
            quality_level=self.quality_level
        )
        # If the processed chunk size is not the same, resample it
        if processed.shape[0] != audio_chunk.shape[0]:
            processed = self._resample(processed, audio_chunk.shape)
        process_time = time.time() - start_time

        self.stats['processed_chunks'] += 1
        self.stats['total_processing_time'] += process_time
        self.stats['avg_processing_time'] = self.stats['total_processing_time'] / self.stats['processed_chunks']
        self.stats['max_processing_time'] = max(self.stats['max_processing_time'], process_time)

        if self.stats['processed_chunks'] % 100 == 0:
            print(f"Processing time: {process_time*1000:.2f}ms, "
                  f"Avg: {self.stats['avg_processing_time']*1000:.2f}ms, "
                  f"Max: {self.stats['max_processing_time']*1000:.2f}ms, "
                  f"Underruns: {self.stats['buffer_underruns']}")

        return processed

    def _processing_thread(self):
        """Thread for processing audio chunks"""
        print("Processing thread started")
        while not self.stop_requested:
            try:
                if not self.input_buffer.empty():
                    audio_chunk = self.input_buffer.get_nowait()
                    processed_chunk = self._process_audio_chunk(audio_chunk)
                    self.output_buffer.put(processed_chunk)
            except Exception as e:
                print(f"Error processing audio: {e}")
            time.sleep(0.001)
        print("Processing thread stopped")


class RealTimeAudioUpscalerGUI(tk.Tk):
    """GUI for Real-time Audio Upscaler"""

    def __init__(self):
        super().__init__()
        self.title(APP_NAME)
        self.geometry("500x400")
        self.minsize(500, 400)

        self.upscale_factor = tk.IntVar(value=2)
        self.quality_level = tk.IntVar(value=1)
        self.use_win11_opt = tk.BooleanVar(value=True)
        self.use_hardware_accel = tk.BooleanVar(value=True)

        self.upscaler = None

        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        title_label = ttk.Label(self.main_frame, text=APP_NAME, font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20))

        settings_frame = ttk.LabelFrame(self.main_frame, text="Settings")
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        settings_grid = ttk.Frame(settings_frame)
        settings_grid.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(settings_grid, text="Upscale Factor:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="2x", variable=self.upscale_factor, value=2).grid(row=0, column=1, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="4x", variable=self.upscale_factor, value=4).grid(row=0, column=2, padx=5, pady=5)

        ttk.Label(settings_grid, text="Quality Level:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="Low (Better Performance)", variable=self.quality_level, value=1).grid(row=1, column=1, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="Medium", variable=self.quality_level, value=2).grid(row=1, column=2, padx=5, pady=5)

        ttk.Checkbutton(settings_grid, text="Use Windows 11 Optimizations", variable=self.use_win11_opt).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        ttk.Checkbutton(settings_grid, text="Use Hardware Acceleration", variable=self.use_hardware_accel).grid(row=2, column=2, padx=5, pady=5)

        self.control_button = ttk.Button(self.main_frame, text="Start Processing", command=self._toggle_processing)
        self.control_button.pack(pady=20)

        status_frame = ttk.LabelFrame(self.main_frame, text="Status")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.status_frame = ttk.Frame(status_frame)
        self.status_frame.pack(fill=tk.X, pady=10)
        self.status_label = ttk.Label(self.status_frame, text="Status: Not Running")
        self.status_label.pack(side=tk.LEFT, padx=5)
        self.status_indicator = tk.Canvas(self.status_frame, width=20, height=20, bg=self.cget('bg'), highlightthickness=0)
        self.status_indicator.pack(side=tk.LEFT)
        self.status_indicator.create_oval(5, 5, 15, 15, fill="red", tags="indicator")

        self.stats_text = tk.Text(status_frame, wrap=tk.WORD, height=8, width=60)
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.stats_text.insert(tk.END, "Statistics will appear here when processing starts...")

        self._update_stats()
        self.protocol("WM_DELETE_WINDOW", self._on_close)

    def _toggle_processing(self):
        """Toggle audio processing"""
        if self._is_processing():
            self._stop_processing()
        else:
            self._start_processing()

    def _is_processing(self):
        """Check if audio processing is running"""
        return self.upscaler and self.upscaler.running

    def _stop_processing(self):
        """Stop audio processing"""
        if self.upscaler is not None:
            self.upscaler.stop()
            self.upscaler = None
        self.control_button.config(text="Start Processing")
        self.status_label.config(text="Status: Not Running")
        self.status_indicator.itemconfig("indicator", fill="red")
        self._set_settings_state(tk.NORMAL)

    def _start_processing(self):
        """Start audio processing"""
        self.upscaler = RealTimeAudioUpscaler(
            upscale_factor=self.upscale_factor.get(),
            quality_level=self.quality_level.get(),
            use_win11_opt=self.use_win11_opt.get(),
            use_hardware_accel=self.use_hardware_accel.get()
        )
        if self.upscaler.start():
            self.control_button.config(text="Stop Processing")
            self.status_label.config(text="Status: Running")
            self.status_indicator.itemconfig("indicator", fill="green")
            self._set_settings_state(tk.DISABLED)
        else:
            self.upscaler = None

    def _find_settings_frame(self):
        """Find the settings frame in the main frame"""
        for child in self.main_frame.winfo_children():
            if isinstance(child, ttk.LabelFrame) and child.cget("text") == "Settings":
                return child
        return None

    def _update_widget_states(self, parent, state):
        """Update the state of all radio buttons and checkboxes in a widget"""
        for widget in parent.winfo_children():
            if isinstance(widget, (ttk.Radiobutton, ttk.Checkbutton)):
                widget.config(state=state)
            elif hasattr(widget, 'winfo_children'):
                self._update_widget_states(widget, state)

    def _set_settings_state(self, state):
        """Enable or disable the settings controls"""
        settings_frame = self._find_settings_frame()
        if settings_frame:
            self._update_widget_states(settings_frame, state)

    def _update_stats(self):
        """Update statistics display periodically"""
        if self.upscaler and self.upscaler.running:
            self.stats_text.delete(1.0, tk.END)
            stats = self.upscaler.stats
            duration = time.time() - stats['start_time']
            self.stats_text.insert(tk.END, f"Running for: {duration:.1f} seconds\n")
            self.stats_text.insert(tk.END, f"Processed chunks: {stats['processed_chunks']}\n")
            self.stats_text.insert(tk.END, f"Average processing time: {stats['avg_processing_time']*1000:.2f} ms\n")
            self.stats_text.insert(tk.END, f"Maximum processing time: {stats['max_processing_time']*1000:.2f} ms\n")
            self.stats_text.insert(tk.END, f"Buffer underruns: {stats['buffer_underruns']}\n")
            latency = stats['avg_processing_time'] * 1000
            if latency < 10:
                latency_status = "Excellent"
            elif latency < 20:
                latency_status = "Good"
            elif latency < 30:
                latency_status = "Acceptable"
            else:
                latency_status = "High"
            self.stats_text.insert(tk.END, f"\nLatency: {latency:.2f} ms ({latency_status})")
        self.after(500, self._update_stats)

    def _on_close(self):
        """Handle window close event"""
        if self.upscaler and self.upscaler.running:
            self.upscaler.stop()
        self.destroy()


def main():
    """Main function to run the application"""
    parser = argparse.ArgumentParser(description=APP_NAME)
    parser.add_argument("--no-gui", action="store_true", help="Run without GUI")
    parser.add_argument("--upscale-factor", type=int, default=2, choices=[2, 4], help="Upscale factor")
    parser.add_argument("--quality", type=int, default=1, choices=[1, 2, 3], help="Quality level (1=Low, 2=Medium, 3=High)")
    parser.add_argument("--no-win11-opt", action="store_true", help="Disable Windows 11 optimizations")
    parser.add_argument("--no-hardware-accel", action="store_true", help="Disable hardware acceleration")

    args = parser.parse_args()

    if args.no_gui:
        upscaler = RealTimeAudioUpscaler(
            upscale_factor=args.upscale_factor,
            quality_level=args.quality,
            use_win11_opt=not args.no_win11_opt,
            use_hardware_accel=not args.no_hardware_accel
        )
        print(f"Starting {APP_NAME}...")
        if upscaler.start():
            print("Press Ctrl+C to stop")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print(f"\nStopping {APP_NAME}...")
                upscaler.stop()
        else:
            print(f"Failed to start {APP_NAME}")
    else:
        app = RealTimeAudioUpscalerGUI()
        app.mainloop()


if __name__ == "__main__":
    main()

