from __future__ import annotations
from typing import Dict, List, Union, Iterator
from decimal import Decimal, getcontext, InvalidOperation
from enum import Enum, auto
from dataclasses import dataclass
import math
import re

# Error message constants
ERROR_INVALID_EXPRESSION = "Invalid expression"
ERROR_DIVISION_BY_ZERO = "Division by zero"
ERROR_MISMATCHED_PARENTHESES = "Mismatched parentheses"
ERROR_EMPTY_EXPRESSION = "Empty expression"

# Increase precision for better accuracy
getcontext().prec = 32

class TokenType(Enum):
    NUMBER = auto()
    OPERATOR = auto()
    FUNCTION = auto()
    LPAREN = auto()
    RPAREN = auto()
    COMMA = auto()
    EOF = auto()

@dataclass(frozen=True)
class Token:
    type: TokenType
    value: str
    position: int

class CalculatorError(Exception):
    def __init__(self, message: str, position: int):
        self.message = message
        self.position = position
        super().__init__(f"{message} at position {position}")

class DomainError(CalculatorError):
    pass

class Lexer:
    PATTERNS = [
        (TokenType.NUMBER, r'-?(?:\d*\.)?\d+(?:[eE][+-]?\d+)?'),  # Numbers with optional decimal point and scientific notation
        (TokenType.FUNCTION, r'(sin|cos|tan|log|ln|sqrt|abs)'),
        (TokenType.OPERATOR, r'[+\-*/^]'),
        (TokenType.LPAREN, r'\('),
        (TokenType.RPAREN, r'\)'),
        (TokenType.COMMA, r','),
    ]

    def __init__(self, text: str):
        self.text = text  # Spaces already removed in preprocessing
        self.pos = 0
        self._token_regex = '|'.join(f'(?P<{name.name}>{pattern})'
                                    for name, pattern in self.PATTERNS)
        self._pattern = re.compile(self._token_regex)

    def tokenize(self) -> Iterator[Token]:
        while self.pos < len(self.text):
            match = self._pattern.match(self.text, self.pos)
            if not match:
                raise CalculatorError(f"Invalid character '{self.text[self.pos]}'", self.pos)

            token_type = TokenType[match.lastgroup]
            value = match.group()
            yield Token(token_type, value, self.pos)
            self.pos = match.end()

class Calculator:
    OPERATIONS = {
        '+': (1, True, lambda x, y: x + y),
        '-': (1, True, lambda x, y: x - y),
        '*': (2, True, lambda x, y: x * y),
        '/': (2, True, lambda x, y: x / y),
        '^': (3, False, lambda x, y: x ** y if float(y).is_integer() else
              Decimal(str(float(x) ** float(y))))
    }

    def __init__(self):
        self._cache: Dict[str, Decimal] = {}

    def _to_decimal(self, value: str, pos: int) -> Decimal:
        try:
            return Decimal(value)
        except InvalidOperation:
            raise CalculatorError(f"Invalid number format: {value}", pos)

    def _apply_function(self, name: str, x: Decimal, pos: int) -> Decimal:
        try:
            float_x = float(x)
            result = None

            if name == 'sin':
                result = math.sin(float_x)
            elif name == 'cos':
                result = math.cos(float_x)
            elif name == 'tan':
                result = math.tan(float_x)
            elif name == 'log':
                if float_x <= 0:
                    raise DomainError("Log argument must be positive", pos)
                result = math.log10(float_x)
            elif name == 'ln':
                if float_x <= 0:
                    raise DomainError("Ln argument must be positive", pos)
                result = math.log(float_x)
            elif name == 'sqrt':
                if float_x < 0:
                    raise DomainError("Square root of negative number", pos)
                result = math.sqrt(float_x)
            elif name == 'abs':
                return abs(x)  # Can use Decimal directly

            return Decimal(str(result))

        except (ValueError, InvalidOperation) as e:
            raise CalculatorError(f"Function error in {name}: {str(e)}", pos)

    def _preprocess_expression(self, expression: str) -> str:
        """Preprocess the expression to handle various syntax issues."""
        # Remove all spaces
        expression = expression.replace(" ", "")

        # Handle decimal points without leading zeros (.5 -> 0.5)
        expression = re.sub(r'(?<!\d)\.(?=\d)', '0.', expression)

        # Handle trailing decimal points (5. -> 5.0)
        expression = re.sub(r'(\d)\.(\D|$)', r'\1.0\2', expression)

        # Handle scientific notation (2.5e-3 -> 0.0025)
        def replace_scientific(match):
            base = float(match.group(1))
            exp = int(match.group(2))
            return str(base * (10 ** exp))

        expression = re.sub(r'(\d+\.?\d*)[eE]([+-]?\d+)', replace_scientific, expression)

        # Handle subtraction vs. negative numbers
        # Replace - with + - for easier parsing when it's a subtraction operation
        # But don't do this for negative numbers at the start or after another operator or opening parenthesis
        expression = re.sub(r'(?<!^)(?<![+\-*/^(])\-', '+-', expression)

        # Define function names for easier reference
        functions = "sin|cos|tan|log|ln|sqrt|abs"

        # Pattern for number followed by opening parenthesis: 2(3)
        pattern1 = r'(\d)(?=\()'

        # Pattern for number followed by function: 2sin(3)
        pattern2 = r'(\d)(?=(' + functions + '))'

        # Pattern for closing parenthesis followed by opening parenthesis or number: (2)(3) or (2)3
        pattern3 = r'(\))(?=[\(\d])'

        # Pattern for closing parenthesis followed by function: (2)sin(3)
        pattern4 = r'(\))(?=(' + functions + '))'

        # Insert multiplication operators for implicit multiplication
        expression = re.sub(pattern1, r'\1*', expression)
        expression = re.sub(pattern2, r'\1*', expression)
        expression = re.sub(pattern3, r'\1*', expression)
        expression = re.sub(pattern4, r'\1*', expression)

        return expression

    def evaluate(self, expression: str) -> Decimal:
        if not expression.strip():
            raise CalculatorError(ERROR_EMPTY_EXPRESSION, 0)

        # Check cache before preprocessing
        if expression in self._cache:
            return self._cache[expression]

        # Preprocess the expression
        processed_expr = self._preprocess_expression(expression)

        # Check cache again after preprocessing
        if processed_expr in self._cache:
            return self._cache[processed_expr]

        try:
            tokens = list(Lexer(processed_expr).tokenize())
            result = self._evaluate_rpn(self._to_rpn(tokens))

            # Cache both original and processed expressions
            self._cache[expression] = result
            if expression != processed_expr:
                self._cache[processed_expr] = result

            return result
        except CalculatorError as e:
            # Add more context to the error message
            if e.message == ERROR_INVALID_EXPRESSION:
                raise CalculatorError(f"Invalid expression: '{expression}'.", e.position)
            raise

    def _should_pop_operator(self, current_op: str, stack_op: str) -> bool:
        """Determine if we should pop an operator based on precedence rules."""
        current_precedence, current_left_assoc = self.OPERATIONS[current_op][0:2]
        stack_precedence = self.OPERATIONS[stack_op][0]

        if current_left_assoc:
            return current_precedence <= stack_precedence
        else:
            return current_precedence < stack_precedence

    def _handle_operator(self, token: Token, output: list, operators: list) -> None:
        """Process an operator token in the shunting yard algorithm."""
        while (operators and operators[-1].type == TokenType.OPERATOR and
               self._should_pop_operator(token.value, operators[-1].value)):
            output.append(operators.pop())
        operators.append(token)

    def _handle_right_paren(self, token: Token, output: list, operators: list) -> None:
        """Process a right parenthesis token in the shunting yard algorithm."""
        # Pop operators until we find the matching left parenthesis
        while operators and operators[-1].type != TokenType.LPAREN:
            output.append(operators.pop())

        # Check for mismatched parentheses
        if not operators:
            raise CalculatorError(ERROR_MISMATCHED_PARENTHESES, token.position)

        operators.pop()  # Remove LPAREN

        # If there's a function before the parenthesis, add it to output
        if operators and operators[-1].type == TokenType.FUNCTION:
            output.append(operators.pop())

    def _to_rpn(self, tokens: List[Token]) -> List[Union[Token, Decimal]]:
        """Convert infix notation to Reverse Polish Notation using the shunting yard algorithm."""
        output = []
        operators = []

        # Process each token
        for token in tokens:
            if token.type == TokenType.NUMBER:
                # Numbers go directly to output
                output.append(self._to_decimal(token.value, token.position))

            elif token.type == TokenType.FUNCTION:
                # Functions go to the operator stack
                operators.append(token)

            elif token.type == TokenType.OPERATOR:
                # Handle operators according to precedence rules
                self._handle_operator(token, output, operators)

            elif token.type == TokenType.LPAREN:
                # Left parentheses go to the operator stack
                operators.append(token)

            elif token.type == TokenType.RPAREN:
                # Right parentheses require special handling
                self._handle_right_paren(token, output, operators)

        # Process any remaining operators
        while operators:
            op = operators.pop()
            if op.type == TokenType.LPAREN:
                raise CalculatorError(ERROR_MISMATCHED_PARENTHESES, op.position)
            output.append(op)

        return output

    def _evaluate_operator(self, operator: str, a: Decimal, b: Decimal, position: int) -> Decimal:
        """Evaluate a binary operator with two operands."""
        try:
            # Check for division by zero
            if operator == '/' and b == 0:
                raise DomainError(ERROR_DIVISION_BY_ZERO, position)

            # Apply the operation
            return self.OPERATIONS[operator][2](a, b)

        except (InvalidOperation, OverflowError) as e:
            raise CalculatorError(f"Arithmetic error: {str(e)}", position)

    def _evaluate_function_token(self, token: Token, stack: list) -> None:
        """Evaluate a function token and push the result to the stack."""
        if not stack:
            raise CalculatorError(f"Missing argument for {token.value}", token.position)

        x = stack.pop()
        result = self._apply_function(token.value, x, token.position)
        stack.append(result)

    def _evaluate_operator_token(self, token: Token, stack: list) -> None:
        """Evaluate an operator token and push the result to the stack."""
        if len(stack) < 2:
            raise CalculatorError(ERROR_INVALID_EXPRESSION, token.position)

        b = stack.pop()
        a = stack.pop()

        result = self._evaluate_operator(token.value, a, b, token.position)
        stack.append(result)

    def _evaluate_rpn(self, rpn: List[Union[Token, Decimal]]) -> Decimal:
        """Evaluate an expression in Reverse Polish Notation."""
        stack = []

        # Process each token in the RPN expression
        for token in rpn:
            if isinstance(token, Decimal):
                # Numbers go directly to the stack
                stack.append(token)
            elif token.type == TokenType.OPERATOR:
                # Evaluate operators
                self._evaluate_operator_token(token, stack)
            elif token.type == TokenType.FUNCTION:
                # Evaluate functions
                self._evaluate_function_token(token, stack)

        # Check if we have a valid result
        if len(stack) != 1:
            raise CalculatorError(ERROR_INVALID_EXPRESSION, 0)

        return stack[0]

def run_tests():
    calc = Calculator()

    test_cases = [
        ("2 + 2", "4"),
        ("2 * (3 + 4)", "14"),
        ("2 ^ 3", "8"),
        ("sqrt(16)", "4.0"),
        ("sin(0)", "0.0"),
        ("log(100)", "2.0"),
        ("2.5e-3 * 1e3", "2.50000"),  # Scientific notation
        ("-2 * -3", "6"),
        ("abs(-42)", "42"),
        # Implicit multiplication test cases
        ("2(3+4)", "14"),
        ("(2+3)(4+5)", "45"),
        ("2log(100)", "4.0"),
        ("sin(0)cos(0)", "0.00"),
        ("5(2)(3)", "30"),
        ("15(log(10))", "15.0"),
    ]

    error_cases = [
        "1/0",  # Division by zero
        "2 + * 3",  # Invalid syntax
        "sqrt(-1)",  # Domain error
        "log(-1)",  # Domain error
        "(",  # Mismatched parentheses
        ")",  # Mismatched parentheses
        "",  # Empty expression
    ]

    print("Running normal test cases...")
    for expr, expected in test_cases:
        try:
            result = calc.evaluate(expr)
            assert str(result) == expected, f"Failed: {expr} = {result}, expected {expected}"
            print(f"✓ {expr} = {result}")
        except AssertionError as e:
            print(f"✗ {e}")

    print("\nRunning error test cases...")
    for expr in error_cases:
        try:
            result = calc.evaluate(expr)
            print(f"✗ {expr} should have raised an error but got {result}")
        except CalculatorError as e:
            print(f"✓ {expr} correctly raised: {e.message}")

if __name__ == "__main__":
    run_tests()