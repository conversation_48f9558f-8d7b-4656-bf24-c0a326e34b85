* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: transparent;
  color: #f5f5f5;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

body.visible {
  opacity: 1;
}

.overlay-container {
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.solution-header {
  display: flex;
  flex-direction: column;
  padding: 10px;
  background-color: rgba(20, 20, 20, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.drag-handle {
  width: 100px;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin: 0 auto 10px;
  cursor: move;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
}

.status-indicator {
  padding: 3px 8px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

.status-quick {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.status-final {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.status-error {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.model-indicator {
  padding: 3px 8px;
  border-radius: 4px;
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.controls {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
}

.control-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

#copy-btn {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

#copy-btn:hover {
  background-color: rgba(33, 150, 243, 0.3);
}

.solution-content {
  flex: 1;
  overflow: auto;
  padding: 15px;
}

.tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
}

.tab-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
}

.tab-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.tab-btn.active {
  background-color: rgba(52, 152, 219, 0.3);
  color: #fff;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #3498db;
}

h4 {
  font-size: 16px;
  margin-bottom: 8px;
  color: #2ecc71;
}

.content-area {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  line-height: 1.5;
}

.code-area {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  font-family: 'Consolas', 'Monaco', monospace;
  white-space: pre-wrap;
  overflow-x: auto;
}

.complexity-item {
  margin-bottom: 15px;
}

.footer {
  padding: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(20, 20, 20, 0.8);
}

.model-switcher {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.model-btn {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.model-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.model-btn.active {
  background-color: rgba(33, 150, 243, 0.3);
  color: #2196f3;
  border-color: #2196f3;
}

/* Syntax highlighting */
.keyword {
  color: #569cd6;
}

.string {
  color: #ce9178;
}

.comment {
  color: #6a9955;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
