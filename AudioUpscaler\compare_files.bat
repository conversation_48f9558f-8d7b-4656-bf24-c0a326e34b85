@echo off
echo Audio File Comparison Tool
echo ------------------------
echo.
echo This tool compares original and upscaled audio files to verify the upscaling quality.
echo.

REM Set Python path
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Check if required packages are installed
echo Checking required packages...
"%PYTHON_PATH%\python.exe" -c "try: import numpy, soundfile, scipy, matplotlib; print('All required packages are installed.'); exit(0); except ImportError as e: print(f'Missing package: {e}'); exit(1)"

if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    "%PYTHON_PATH%\python.exe" -m pip install numpy soundfile scipy matplotlib
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install required packages.
        echo Please install them manually:
        echo pip install numpy soundfile scipy matplotlib
        pause
        exit /b 1
    )
)

REM Run the comparison tool
echo Starting File Comparison Tool...
"%PYTHON_PATH%\python.exe" utils\file_comparison_tool.py

echo.
echo Comparison tool closed.
pause
