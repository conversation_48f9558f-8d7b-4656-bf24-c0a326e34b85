"""
Simple test script for the audio upscaling system
"""

import os
import numpy as np
import soundfile as sf
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_test_signal():
    """Generate a test signal"""
    # Create time vector
    sample_rate = 44100
    duration = 2  # seconds
    t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
    
    # Create a test signal with multiple frequencies
    signal = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz (A4)
    signal += 0.3 * np.sin(2 * np.pi * 880 * t)  # 880 Hz (A5)
    signal += 0.2 * np.sin(2 * np.pi * 1760 * t)  # 1760 Hz (A6)
    
    # Add some noise
    rng = np.random.default_rng(42)  # For reproducibility
    signal += 0.05 * rng.standard_normal(len(t))
    
    # Add DC offset
    signal += 0.1
    
    # Create stereo signal
    stereo_signal = np.column_stack((signal, signal * 0.8))
    
    # Save test signal
    test_file = "test_signal.wav"
    sf.write(test_file, stereo_signal, sample_rate)
    
    logger.info(f"Generated test signal: {test_file}")
    
    return test_file

def test_preprocessor():
    """Test the audio preprocessor"""
    logger.info("Testing audio preprocessor...")
    
    # Generate test signal
    test_file = generate_test_signal()
    
    try:
        # Import preprocessor
        from audio_preprocessor import AudioPreprocessor
        
        # Create preprocessor
        preprocessor = AudioPreprocessor(device="cpu", quality_level=1)
        
        # Process test signal
        output_file = preprocessor.preprocess_file(test_file)
        
        logger.info(f"Preprocessed file saved to: {output_file}")
        
        return output_file
    except Exception as e:
        logger.error(f"Error testing preprocessor: {e}")
        return None

def test_upscaler():
    """Test the audio upscaler"""
    logger.info("Testing audio upscaler...")
    
    # Generate test signal
    test_file = generate_test_signal()
    
    try:
        # Import upscaler
        from audio_upscaler import AudioUpscaler
        
        # Create upscaler
        upscaler = AudioUpscaler(quality_level=1, use_gpu=False)
        
        # Process test signal
        output_file = upscaler.upscale_file(test_file, target_sample_rate=48000, target_bit_depth=24)
        
        logger.info(f"Upscaled file saved to: {output_file}")
        
        return output_file
    except Exception as e:
        logger.error(f"Error testing upscaler: {e}")
        return None

def test_postprocessor():
    """Test the audio postprocessor"""
    logger.info("Testing audio postprocessor...")
    
    # Generate test signal
    test_file = generate_test_signal()
    
    try:
        # Import postprocessor
        from audio_postprocessor import AudioPostprocessor
        
        # Create postprocessor
        postprocessor = AudioPostprocessor(device="cpu", quality_level=1)
        
        # Process test signal
        output_file = postprocessor.postprocess_file(test_file, target_lufs=-16)
        
        logger.info(f"Postprocessed file saved to: {output_file}")
        
        return output_file
    except Exception as e:
        logger.error(f"Error testing postprocessor: {e}")
        return None

def test_complete_system():
    """Test the complete audio upscaling system"""
    logger.info("Testing complete audio upscaling system...")
    
    # Generate test signal
    test_file = generate_test_signal()
    
    try:
        # Import upscaling system
        from audio_upscaling_system import AudioUpscalingSystem
        
        # Create upscaling system
        system = AudioUpscalingSystem(quality_level=1, use_gpu=False)
        
        # Process test signal
        output_file = system.process_file(
            test_file,
            target_sample_rate=48000,
            target_bit_depth=24,
            target_lufs=-16
        )
        
        logger.info(f"Processed file saved to: {output_file}")
        
        return output_file
    except Exception as e:
        logger.error(f"Error testing complete system: {e}")
        return None

def main():
    """Main function"""
    logger.info("Starting simple test...")
    
    # Test preprocessor
    preprocessor_output = test_preprocessor()
    
    # Test upscaler
    upscaler_output = test_upscaler()
    
    # Test postprocessor
    postprocessor_output = test_postprocessor()
    
    # Test complete system
    system_output = test_complete_system()
    
    # Print summary
    logger.info("\nTest Summary:")
    logger.info(f"Preprocessor: {'Success' if preprocessor_output else 'Failed'}")
    logger.info(f"Upscaler: {'Success' if upscaler_output else 'Failed'}")
    logger.info(f"Postprocessor: {'Success' if postprocessor_output else 'Failed'}")
    logger.info(f"Complete System: {'Success' if system_output else 'Failed'}")
    
    return 0

if __name__ == "__main__":
    main()
