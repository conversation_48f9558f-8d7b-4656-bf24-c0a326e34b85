"""
High-Quality Audio Upscaler
Prioritizes quality over processing time
"""

import os
import sys
import time
import argparse
import logging
import numpy as np
import soundfile as sf
import torch
from typing import Dict, Any, Optional, List, Union, Tuple
from pathlib import Path

# Import our GPU processor
from gpu_audio_processor import GPUAudioProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("audio_upscaler.log")
    ]
)
logger = logging.getLogger(__name__)

class AudioUpscaler:
    """
    High-quality audio upscaler that prioritizes quality over processing time
    """
    def __init__(self,
                 quality_level: int = 3,
                 use_gpu: bool = True,
                 use_win11_opt: bool = True,
                 reduce_load: bool = True):
        """
        Initialize the audio upscaler

        Args:
            quality_level: Quality level (1=Low, 2=Medium, 3=High)
            use_gpu: Whether to use GPU acceleration
            use_win11_opt: Whether to use Windows 11 optimizations
            reduce_load: Whether to reduce load on GPU/CPU
        """
        self.quality_level = quality_level
        self.use_gpu = use_gpu
        self.use_win11_opt = use_win11_opt
        self.reduce_load = reduce_load

        # Initialize GPU processor
        if self.use_gpu:
            try:
                self.processor = GPUAudioProcessor(
                    device="auto",
                    use_win11_opt=self.use_win11_opt,
                    quality_level=self.quality_level,
                    reduce_load=self.reduce_load
                )
                logger.info(f"GPU processor initialized on {self.processor.device}")
            except Exception as e:
                logger.warning(f"Could not initialize GPU processor: {e}")
                logger.warning("Falling back to CPU processing")
                self.use_gpu = False

        # Initialize quality parameters
        self._init_quality_params()

        logger.info(f"Audio Upscaler initialized")
        logger.info(f"  Quality level: {quality_level}")
        logger.info(f"  Using GPU: {use_gpu}")
        logger.info(f"  Windows 11 optimizations: {use_win11_opt}")
        logger.info(f"  Reduced load: {reduce_load}")

    def _init_quality_params(self):
        """Initialize quality parameters based on quality level"""
        if self.quality_level == 1:  # Low
            self.params = {
                "filter_size": 64,
                "fft_size": 2048,
                "overlap": 4,
                "iterations": 1,
                "sample_rate_multiplier": 2,
                "bit_depth": 24
            }
        elif self.quality_level == 2:  # Medium
            self.params = {
                "filter_size": 128,
                "fft_size": 4096,
                "overlap": 8,
                "iterations": 2,
                "sample_rate_multiplier": 4,
                "bit_depth": 24
            }
        else:  # High
            self.params = {
                "filter_size": 256,
                "fft_size": 8192,
                "overlap": 16,
                "iterations": 3,
                "sample_rate_multiplier": 4,
                "bit_depth": 32
            }

    def upscale_file(self,
                    input_file: str,
                    output_file: Optional[str] = None,
                    target_sample_rate: Optional[int] = None,
                    target_bit_depth: Optional[int] = None) -> str:
        """
        Upscale an audio file

        Args:
            input_file: Path to input audio file
            output_file: Path to output audio file (optional)
            target_sample_rate: Target sample rate (optional)
            target_bit_depth: Target bit depth (optional)

        Returns:
            Path to the upscaled audio file
        """
        # Check if input file exists
        if not os.path.isfile(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # Generate output filename if not provided
        if output_file is None:
            input_dir = os.path.dirname(input_file)
            input_basename = os.path.basename(input_file)
            input_name, input_ext = os.path.splitext(input_basename)
            output_dir = os.path.join(input_dir, "upscaled")
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{input_name}_upscaled.wav")

        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Load audio file
        logger.info(f"Loading audio file: {input_file}")
        audio_data, sample_rate = sf.read(input_file)

        # Log audio properties
        duration = len(audio_data) / sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]
        logger.info(f"Audio duration: {duration:.2f} seconds")
        logger.info(f"Audio channels: {channels}")
        logger.info(f"Audio sample rate: {sample_rate}Hz")

        # Determine target sample rate
        if target_sample_rate is None:
            target_sample_rate = sample_rate * self.params["sample_rate_multiplier"]

        # Determine target bit depth
        if target_bit_depth is None:
            target_bit_depth = self.params["bit_depth"]

        logger.info(f"Target sample rate: {target_sample_rate}Hz")
        logger.info(f"Target bit depth: {target_bit_depth}-bit")

        # Process audio
        logger.info(f"Processing audio...")
        start_time = time.time()

        if self.use_gpu:
            # Use GPU processor
            try:
                upscale_factor = target_sample_rate // sample_rate
                processed_audio = self.processor._process_audio(audio_data, sample_rate, upscale_factor)
            except Exception as e:
                logger.error(f"Error using GPU processor: {e}")
                logger.warning("Falling back to CPU processing")
                processed_audio = self._process_audio_cpu(audio_data, sample_rate, target_sample_rate)
        else:
            # Use CPU processing
            processed_audio = self._process_audio_cpu(audio_data, sample_rate, target_sample_rate)

        process_time = time.time() - start_time
        logger.info(f"Processing completed in {process_time:.2f} seconds")

        # Save output
        logger.info(f"Saving output to: {output_file}")

        # Determine subtype based on bit depth
        if target_bit_depth == 16:
            subtype = 'PCM_16'
        elif target_bit_depth == 24:
            subtype = 'PCM_24'
        else:
            subtype = 'FLOAT'  # 32-bit float

        sf.write(output_file, processed_audio, target_sample_rate, subtype=subtype)

        return output_file

    def _process_audio_cpu(self,
                          audio_data: np.ndarray,
                          sample_rate: int,
                          target_sample_rate: int) -> np.ndarray:
        """
        Process audio using CPU

        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio data
            target_sample_rate: Target sample rate

        Returns:
            Processed audio data
        """
        try:
            # Handle mono vs stereo
            if audio_data.ndim == 1:
                # Mono
                # Resample
                resampled = self._resample_cpu(audio_data, sample_rate, target_sample_rate)

                # Apply spectral enhancement
                enhanced = self._enhance_spectral_cpu(resampled, target_sample_rate)

                # Apply dynamic range enhancement
                processed = self._enhance_dynamics_cpu(enhanced, target_sample_rate)

                return processed
            else:
                # Stereo or multi-channel
                # Calculate output size
                output_size = int(audio_data.shape[0] * target_sample_rate / sample_rate)
                processed = np.zeros((output_size, audio_data.shape[1]))

                # Process each channel
                for i in range(audio_data.shape[1]):
                    # Resample
                    resampled = self._resample_cpu(audio_data[:, i], sample_rate, target_sample_rate)

                    # Apply spectral enhancement
                    enhanced = self._enhance_spectral_cpu(resampled, target_sample_rate)

                    # Apply dynamic range enhancement
                    processed[:, i] = self._enhance_dynamics_cpu(enhanced, target_sample_rate)

                return processed
        except Exception as e:
            logger.error(f"Error in CPU processing: {e}")
            # Fallback to simple resampling
            try:
                return self._resample_cpu(audio_data, sample_rate, target_sample_rate)
            except Exception as e2:
                logger.error(f"Error in fallback resampling: {e2}")
                # Return original audio as last resort
                return audio_data

    def _resample_cpu(self,
                     audio_data: np.ndarray,
                     orig_sr: int,
                     target_sr: int) -> np.ndarray:
        """
        Resample audio using CPU

        Args:
            audio_data: Audio data as numpy array
            orig_sr: Original sample rate
            target_sr: Target sample rate

        Returns:
            Resampled audio data
        """
        try:
            from scipy import signal

            # Process in chunks for large audio files
            if len(audio_data) > 100000:  # Approximately 2 seconds at 44.1kHz
                return self._resample_cpu_chunked(audio_data, orig_sr, target_sr)

            # Use scipy's resample_poly for high-quality resampling
            gcd = np.gcd(orig_sr, target_sr)
            up = target_sr // gcd
            down = orig_sr // gcd

            resampled = signal.resample_poly(
                audio_data,
                up,
                down,
                window=('kaiser', self.params["filter_size"])
            )

            return resampled
        except Exception as e:
            logger.error(f"Error in resample_poly: {e}")
            try:
                # Fallback to numpy's interp
                logger.warning("Using numpy interp as fallback")

                # Simple interpolation as fallback
                orig_length = len(audio_data)
                target_length = int(orig_length * target_sr / orig_sr)

                resampled = np.interp(
                    np.linspace(0, orig_length - 1, target_length),
                    np.arange(orig_length),
                    audio_data
                )

                return resampled
            except Exception as e2:
                logger.error(f"Error in numpy interp: {e2}")
                # Return original audio as last resort
                return audio_data

    def _resample_cpu_chunked(self,
                            audio_data: np.ndarray,
                            orig_sr: int,
                            target_sr: int) -> np.ndarray:
        """
        Resample audio using CPU in chunks

        Args:
            audio_data: Audio data as numpy array
            orig_sr: Original sample rate
            target_sr: Target sample rate

        Returns:
            Resampled audio data
        """
        try:
            from scipy import signal

            # Calculate output size
            output_size = int(len(audio_data) * target_sr / orig_sr)
            resampled = np.zeros(output_size)

            # Calculate GCD for resampling ratio
            gcd = np.gcd(orig_sr, target_sr)
            up = target_sr // gcd
            down = orig_sr // gcd

            # Process in chunks
            chunk_size = 8192
            overlap = 512  # Overlap to avoid boundary artifacts

            # Calculate number of chunks
            num_chunks = (len(audio_data) + chunk_size - 1) // chunk_size

            for i in range(num_chunks):
                # Calculate chunk boundaries
                start = i * chunk_size
                end = min(start + chunk_size + overlap, len(audio_data))

                # Extract chunk
                chunk = audio_data[start:end]

                # Resample chunk
                chunk_resampled = signal.resample_poly(
                    chunk,
                    up,
                    down,
                    window=('kaiser', self.params["filter_size"])
                )

                # Calculate output boundaries
                out_start = int(start * target_sr / orig_sr)
                out_end = int(end * target_sr / orig_sr)
                out_end = min(out_end, output_size)

                # Apply fade-in/out for overlapping regions
                if i > 0:
                    # Calculate overlap in output samples
                    overlap_samples = int(overlap * target_sr / orig_sr)

                    # Create fade-in window
                    fade_in = np.linspace(0, 1, overlap_samples)

                    # Apply fade-in to current chunk
                    if len(chunk_resampled) > overlap_samples:
                        chunk_resampled[:overlap_samples] *= fade_in

                # Add chunk to output
                out_length = min(len(chunk_resampled), out_end - out_start)
                resampled[out_start:out_start + out_length] = chunk_resampled[:out_length]

            return resampled
        except Exception as e:
            logger.error(f"Error in chunked resampling: {e}")
            # Fallback to simple resampling
            return self._resample_cpu(audio_data, orig_sr, target_sr)

    def _enhance_spectral_cpu(self,
                             audio_data: np.ndarray,
                             sample_rate: int) -> np.ndarray:
        """
        Enhance spectral content using CPU

        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio data

        Returns:
            Enhanced audio data
        """
        try:
            from scipy import signal

            # Process in chunks to avoid memory issues
            chunk_size = 8192

            # Handle mono vs stereo
            if audio_data.ndim == 1:
                # Mono
                return self._enhance_spectral_chunk(audio_data)
            else:
                # Stereo or multi-channel
                enhanced = np.zeros_like(audio_data)
                for i in range(audio_data.shape[1]):
                    enhanced[:, i] = self._enhance_spectral_chunk(audio_data[:, i])
                return enhanced
        except Exception as e:
            logger.error(f"Error in spectral enhancement: {e}")
            return audio_data

    def _enhance_spectral_chunk(self, audio_chunk: np.ndarray) -> np.ndarray:
        """
        Enhance spectral content of an audio chunk

        Args:
            audio_chunk: Audio chunk as numpy array

        Returns:
            Enhanced audio chunk
        """
        try:
            # Compute FFT
            fft_size = min(self.params["fft_size"], len(audio_chunk))
            fft = np.fft.rfft(audio_chunk, n=fft_size)

            # Get magnitude and phase
            magnitude = np.abs(fft)
            phase = np.angle(fft)

            # Apply frequency-dependent boost/cut
            freq_bins = len(magnitude)

            # High frequency boost (more subtle)
            high_boost = 0.03  # 3% boost for high frequencies
            high_boost_start = int(freq_bins * 0.75)  # Start at 75% of frequency range
            high_boost_factor = 1.0 + high_boost

            # Create a gradual transition for high boost
            high_boost_range = freq_bins - high_boost_start
            high_boost_curve = np.linspace(1.0, high_boost_factor, high_boost_range)
            magnitude[high_boost_start:] *= high_boost_curve

            # Low frequency boost (more subtle)
            low_boost = 0.02  # 2% boost for low frequencies
            low_boost_end = int(freq_bins * 0.15)  # Boost frequencies below 15%
            low_boost_factor = 1.0 + low_boost

            # Create a gradual transition for low boost
            low_boost_curve = np.linspace(low_boost_factor, 1.0, low_boost_end)
            magnitude[1:low_boost_end+1] *= low_boost_curve  # Skip DC component (index 0)

            # Reconstruct FFT
            fft_real = magnitude * np.cos(phase)
            fft_imag = magnitude * np.sin(phase)
            fft = fft_real + 1j * fft_imag

            # Inverse FFT
            enhanced = np.fft.irfft(fft, n=fft_size)

            # Trim to original length
            enhanced = enhanced[:len(audio_chunk)]

            return enhanced
        except Exception as e:
            logger.error(f"Error in spectral enhancement chunk: {e}")
            return audio_chunk

    def _enhance_dynamics_cpu(self,
                             audio_data: np.ndarray,
                             sample_rate: int) -> np.ndarray:
        """
        Enhance dynamic range using CPU

        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio data

        Returns:
            Enhanced audio data
        """
        try:
            # Handle mono vs stereo
            if audio_data.ndim == 1:
                # Mono
                return self._enhance_dynamics_chunk(audio_data, sample_rate)
            else:
                # Stereo or multi-channel
                enhanced = np.zeros_like(audio_data)
                for i in range(audio_data.shape[1]):
                    enhanced[:, i] = self._enhance_dynamics_chunk(audio_data[:, i], sample_rate)
                return enhanced
        except Exception as e:
            logger.error(f"Error in dynamics enhancement: {e}")
            return audio_data

    def _enhance_dynamics_chunk(self,
                               audio_chunk: np.ndarray,
                               sample_rate: int) -> np.ndarray:
        """
        Enhance dynamic range of an audio chunk

        Args:
            audio_chunk: Audio chunk as numpy array
            sample_rate: Sample rate of the audio data

        Returns:
            Enhanced audio chunk
        """
        try:
            # Calculate attack and release times in samples
            attack_time = 0.005  # 5ms
            release_time = 0.1  # 100ms
            attack_samples = int(attack_time * sample_rate)
            release_samples = int(release_time * sample_rate)

            # Compute envelope
            # Square the signal and apply smoothing
            energy = audio_chunk ** 2

            # Create attack and release filters
            attack_coef = np.exp(-2.2 / max(1, attack_samples))
            release_coef = np.exp(-2.2 / max(1, release_samples))

            # Initialize envelope
            envelope = np.zeros_like(energy)
            envelope[0] = energy[0]

            # Apply envelope following with different attack/release times
            for i in range(1, len(energy)):
                if energy[i] > envelope[i-1]:  # Attack phase
                    envelope[i] = attack_coef * envelope[i-1] + (1 - attack_coef) * energy[i]
                else:  # Release phase
                    envelope[i] = release_coef * envelope[i-1] + (1 - release_coef) * energy[i]

            # Take square root to get amplitude envelope
            amplitude_env = np.sqrt(envelope + 1e-10)

            # Compute derivative of envelope to detect transients
            env_diff = np.diff(amplitude_env, prepend=amplitude_env[0])

            # Detect transients where envelope is rising quickly
            transient_threshold = 0.08
            transient_mask = env_diff > transient_threshold * np.max(amplitude_env)

            # Apply transient boost
            transient_boost = 0.08  # 8% boost for transients
            enhanced = audio_chunk.copy()
            enhanced[transient_mask] *= (1.0 + transient_boost)

            # Apply dynamic range expansion
            threshold = 0.2
            ratio = 0.7  # 0.7:1 expansion ratio
            threshold_amp = threshold * np.max(amplitude_env)

            # Compute gain for expansion
            gain = np.ones_like(audio_chunk)
            mask = amplitude_env < threshold_amp
            if np.any(mask):
                gain[mask] = (amplitude_env[mask] / threshold_amp) ** (ratio - 1.0)
                enhanced[mask] *= gain[mask]

            return enhanced
        except Exception as e:
            logger.error(f"Error in dynamics enhancement chunk: {e}")
            return audio_chunk

    def batch_process(self,
                     input_dir: str,
                     output_dir: Optional[str] = None,
                     file_types: List[str] = ['.wav', '.flac', '.mp3'],
                     recursive: bool = False) -> List[str]:
        """
        Batch process audio files in a directory

        Args:
            input_dir: Input directory
            output_dir: Output directory (optional)
            file_types: List of file extensions to process
            recursive: Whether to process subdirectories

        Returns:
            List of processed file paths
        """
        # Check if input directory exists
        if not os.path.isdir(input_dir):
            raise FileNotFoundError(f"Input directory not found: {input_dir}")

        # Generate output directory if not provided
        if output_dir is None:
            output_dir = os.path.join(input_dir, "upscaled")

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Find audio files
        audio_files = []
        if recursive:
            for root, _, files in os.walk(input_dir):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in file_types):
                        audio_files.append(os.path.join(root, file))
        else:
            for file in os.listdir(input_dir):
                if any(file.lower().endswith(ext) for ext in file_types):
                    audio_files.append(os.path.join(input_dir, file))

        logger.info(f"Found {len(audio_files)} audio files to process")

        # Process each file
        processed_files = []
        for i, file in enumerate(audio_files):
            logger.info(f"Processing file {i+1}/{len(audio_files)}: {file}")

            # Generate output path
            rel_path = os.path.relpath(file, input_dir)
            output_path = os.path.join(output_dir, rel_path)
            output_dir_path = os.path.dirname(output_path)
            os.makedirs(output_dir_path, exist_ok=True)

            # Change extension to .wav
            output_path = os.path.splitext(output_path)[0] + '.wav'

            try:
                # Process file
                processed_file = self.upscale_file(file, output_path)
                processed_files.append(processed_file)
                logger.info(f"Successfully processed: {file} -> {processed_file}")
            except Exception as e:
                logger.error(f"Error processing {file}: {e}")

        return processed_files

    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze an audio file

        Args:
            file_path: Path to audio file

        Returns:
            Dictionary with analysis results
        """
        # Check if file exists
        if not os.path.isfile(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        # Load audio file
        logger.info(f"Analyzing audio file: {file_path}")
        audio_data, sample_rate = sf.read(file_path)

        # Basic properties
        duration = len(audio_data) / sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]

        # Convert to mono for analysis if stereo
        if audio_data.ndim > 1:
            analysis_data = np.mean(audio_data, axis=1)
        else:
            analysis_data = audio_data

        # Calculate peak level
        peak_level = np.max(np.abs(analysis_data))
        peak_db = 20 * np.log10(peak_level + 1e-10)

        # Calculate RMS level
        rms_level = np.sqrt(np.mean(analysis_data ** 2))
        rms_db = 20 * np.log10(rms_level + 1e-10)

        # Calculate dynamic range (difference between peak and RMS)
        dynamic_range = peak_db - rms_db

        # Calculate crest factor (peak / RMS)
        crest_factor = peak_level / (rms_level + 1e-10)
        crest_factor_db = 20 * np.log10(crest_factor)

        # Calculate percentiles for loudness distribution
        sorted_abs = np.sort(np.abs(analysis_data))
        percentile_10 = sorted_abs[int(len(sorted_abs) * 0.1)]
        percentile_90 = sorted_abs[int(len(sorted_abs) * 0.9)]

        # Calculate loudness range (difference between 10th and 90th percentiles)
        loudness_range = 20 * np.log10((percentile_90 + 1e-10) / (percentile_10 + 1e-10))

        # Spectral analysis
        try:
            from scipy import signal

            # Compute spectrogram
            f, t, Sxx = signal.spectrogram(
                analysis_data,
                fs=sample_rate,
                nperseg=2048,
                noverlap=1024,
                scaling='spectrum'
            )

            # Convert to dB
            Sxx_db = 10 * np.log10(Sxx + 1e-10)

            # Calculate spectral centroid
            spectral_centroid = np.sum(f[:, np.newaxis] * Sxx, axis=0) / np.sum(Sxx, axis=0)
            spectral_centroid_mean = np.mean(spectral_centroid)

            # Calculate spectral bandwidth
            spectral_bandwidth = np.sqrt(np.sum(((f[:, np.newaxis] - spectral_centroid) ** 2) * Sxx, axis=0) / np.sum(Sxx, axis=0))
            spectral_bandwidth_mean = np.mean(spectral_bandwidth)

            # Calculate spectral rolloff
            cumsum = np.cumsum(Sxx, axis=0)
            rolloff_threshold = 0.85
            spectral_rolloff = np.zeros_like(t)
            for i in range(len(t)):
                rolloff_point = np.where(cumsum[:, i] >= rolloff_threshold * cumsum[-1, i])[0][0]
                spectral_rolloff[i] = f[rolloff_point]
            spectral_rolloff_mean = np.mean(spectral_rolloff)

            spectral_analysis = {
                'spectral_centroid': spectral_centroid_mean,
                'spectral_bandwidth': spectral_bandwidth_mean,
                'spectral_rolloff': spectral_rolloff_mean
            }
        except Exception as e:
            logger.warning(f"Could not perform spectral analysis: {e}")
            spectral_analysis = {}

        return {
            'file_path': file_path,
            'duration': duration,
            'channels': channels,
            'sample_rate': sample_rate,
            'peak_level_db': peak_db,
            'rms_level_db': rms_db,
            'dynamic_range_db': dynamic_range,
            'crest_factor_db': crest_factor_db,
            'loudness_range_db': loudness_range,
            'percentile_10': 20 * np.log10(percentile_10 + 1e-10),
            'percentile_90': 20 * np.log10(percentile_90 + 1e-10),
            **spectral_analysis
        }


def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description='High-Quality Audio Upscaler')

    # Input/output options
    parser.add_argument('input', help='Input audio file or directory')
    parser.add_argument('-o', '--output', help='Output audio file or directory')
    parser.add_argument('-b', '--batch', action='store_true', help='Batch process directory')
    parser.add_argument('-r', '--recursive', action='store_true', help='Process subdirectories recursively')
    parser.add_argument('-t', '--types', default='.wav,.flac,.mp3', help='File types to process (comma-separated)')

    # Quality options
    parser.add_argument('-q', '--quality', type=int, choices=[1, 2, 3], default=3, help='Quality level (1=Low, 2=Medium, 3=High)')
    parser.add_argument('-s', '--sample-rate', type=int, help='Target sample rate')
    parser.add_argument('-d', '--bit-depth', type=int, choices=[16, 24, 32], help='Target bit depth')

    # Processing options
    parser.add_argument('--cpu', action='store_true', help='Force CPU processing')
    parser.add_argument('--no-win11-opt', action='store_true', help='Disable Windows 11 optimizations')
    parser.add_argument('--full-load', action='store_true', help='Use full CPU/GPU load')

    # Analysis options
    parser.add_argument('-a', '--analyze', action='store_true', help='Analyze audio file without processing')

    args = parser.parse_args()

    try:
        # Create upscaler
        upscaler = AudioUpscaler(
            quality_level=args.quality,
            use_gpu=not args.cpu,
            use_win11_opt=not args.no_win11_opt,
            reduce_load=not args.full_load
        )

        # Check if input is a file or directory
        if os.path.isfile(args.input):
            if args.analyze:
                # Analyze file
                analysis = upscaler.analyze_file(args.input)

                # Print analysis results
                print("\nAudio Analysis Results:")
                print(f"File: {analysis['file_path']}")
                print(f"Duration: {analysis['duration']:.2f} seconds")
                print(f"Channels: {analysis['channels']}")
                print(f"Sample Rate: {analysis['sample_rate']} Hz")
                print(f"Peak Level: {analysis['peak_level_db']:.2f} dB")
                print(f"RMS Level: {analysis['rms_level_db']:.2f} dB")
                print(f"Dynamic Range: {analysis['dynamic_range_db']:.2f} dB")
                print(f"Crest Factor: {analysis['crest_factor_db']:.2f} dB")
                print(f"Loudness Range: {analysis['loudness_range_db']:.2f} dB")

                if 'spectral_centroid' in analysis:
                    print(f"Spectral Centroid: {analysis['spectral_centroid']:.2f} Hz")
                    print(f"Spectral Bandwidth: {analysis['spectral_bandwidth']:.2f} Hz")
                    print(f"Spectral Rolloff: {analysis['spectral_rolloff']:.2f} Hz")
            else:
                # Process file
                output_file = upscaler.upscale_file(
                    args.input,
                    args.output,
                    args.sample_rate,
                    args.bit_depth
                )
                print(f"\nProcessed file saved to: {output_file}")
        elif os.path.isdir(args.input) and args.batch:
            # Batch process directory
            file_types = args.types.split(',')
            processed_files = upscaler.batch_process(
                args.input,
                args.output,
                file_types,
                args.recursive
            )
            print(f"\nProcessed {len(processed_files)} files")
        else:
            if not os.path.exists(args.input):
                print(f"Error: Input path does not exist: {args.input}")
            elif os.path.isdir(args.input) and not args.batch:
                print(f"Error: Input is a directory but --batch flag is not set")
            else:
                print(f"Error: Invalid input path: {args.input}")
    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
