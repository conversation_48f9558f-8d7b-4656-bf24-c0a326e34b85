@echo off
echo Running Windows 11 Optimizations Test...
python windows11_optimizations.py
if %ERRORLEVEL% NEQ 0 (
    echo Test failed with error code %ERRORLEVEL%
    echo Trying alternative Python command...
    py windows11_optimizations.py
    if %ERRORLEVEL% NEQ 0 (
        echo Test failed with error code %ERRORLEVEL%
        echo Trying with python3...
        python3 windows11_optimizations.py
        if %ERRORLEVEL% NEQ 0 (
            echo All Python commands failed.
            echo Please run the test manually.
        )
    )
)
pause
