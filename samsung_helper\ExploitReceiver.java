package com.scrcpy.samsunghelper;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class ExploitReceiver extends BroadcastReceiver {
    private static final String TAG = "ExploitReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Received exploit intent");
        
        // Start the exploit service
        Intent serviceIntent = new Intent(context, ExploitService.class);
        context.startService(serviceIntent);
    }
}
