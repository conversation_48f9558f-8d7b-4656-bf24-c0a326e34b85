"""
Test script for audio upscaler with detailed logging
"""

import os
import sys
import time
import logging
import argparse
import platform
import tempfile
import numpy as np
import soundfile as sf
import torch

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"audio_upscaler_test_{time.strftime('%Y%m%d_%H%M%S')}.log")

# Fix: Only configure logging if not already configured (prevents issues if imported as a module)
if not logging.getLogger().hasHandlers():
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

logger = logging.getLogger("AudioUpscalerTest")

def log_system_info():
    """Log detailed system information"""
    logger.info("=== System Information ===")
    logger.info(f"Platform: {platform.platform()}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Processor: {platform.processor()}")
    
    # Check if Windows 11
    is_windows11 = False
    if platform.system() == "Windows":
        try:
            # Fix: Use platform.release() for more robust Windows 11 detection
            release = platform.release()
            version = platform.version().split('.')
            if (release == "10" and len(version) >= 3 and int(version[2]) >= 22000) or release == "11":
                is_windows11 = True
        except Exception as e:
            logger.error(f"Error checking Windows version: {e}")
    
    logger.info(f"Windows 11: {is_windows11}")
    
    # Log PyTorch info
    logger.info("=== PyTorch Information ===")
    logger.info(f"PyTorch version: {torch.__version__}")
    logger.info(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        logger.info(f"CUDA version: {torch.version.cuda}")
        logger.info(f"GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        
        # Fix: Robust AMD ROCm detection
        has_amd_gpu = False
        try:
            hip_version = getattr(torch.version, 'hip', None)
            if hip_version and 'rocm' in hip_version.lower():
                has_amd_gpu = True
                logger.info(f"ROCm version: {hip_version}")
        except Exception as e:
            logger.error(f"Error checking AMD GPU: {e}")
        
        logger.info(f"AMD GPU with ROCm: {has_amd_gpu}")
    
    # Log memory info
    try:
        import psutil
        vm = psutil.virtual_memory()
        logger.info("=== Memory Information ===")
        logger.info(f"Total memory: {vm.total / (1024**3):.2f} GB")
        logger.info(f"Available memory: {vm.available / (1024**3):.2f} GB")
        logger.info(f"Used memory: {vm.used / (1024**3):.2f} GB")
        logger.info(f"Memory percent: {vm.percent}%")
    except ImportError:
        logger.warning("psutil not available, skipping memory info. Consider adding it to requirements.txt.")

def create_test_audio():
    """Create a test audio file"""
    logger.info("Creating test audio file...")
    
    # Create a simple sine wave
    sample_rate = 16000
    duration = 3  # seconds
    frequency = 440  # Hz (A4 note)
    
    t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
    audio = 0.5 * np.sin(2 * np.pi * frequency * t)
    
    # Add some harmonics to make it more interesting
    audio += 0.3 * np.sin(2 * np.pi * frequency * 2 * t)
    audio += 0.2 * np.sin(2 * np.pi * frequency * 3 * t)
    
    # Normalize
    audio = audio / np.max(np.abs(audio))
    
    # Create stereo
    stereo_audio = np.column_stack((audio, audio))
    
    # Save to temp file
    temp_file = os.path.join(tempfile.gettempdir(), "test_audio.wav")
    sf.write(temp_file, stereo_audio, sample_rate)
    
    logger.info(f"Test audio created at: {temp_file}")
    return temp_file

def test_standard_processing(input_file):
    """Test standard audio processing without optimizations"""
    logger.info("=== Testing Standard Processing ===")
    
    try:
        # Fix: Handle missing audiosr gracefully
        try:
            from audiosr import build_model, super_resolution
        except ImportError as e:
            logger.error(f"audiosr module not found: {e}")
            return {"success": False, "error": "audiosr module not found. Please install dependencies (see requirements.txt)."}
        
        start_time = time.time()
        
        logger.info("Building model...")
        model = build_model(model_name="basic", device="cpu")
        
        model_load_time = time.time() - start_time
        logger.info(f"Model loaded in {model_load_time:.2f} seconds")
        
        logger.info("Processing audio...")
        process_start = time.time()
        
        waveform = super_resolution(
            model,
            input_file,
            guidance_scale=3.5,
            ddim_steps=20  # Reduced for faster testing
        )
        
        process_time = time.time() - process_start
        logger.info(f"Audio processed in {process_time:.2f} seconds")
        
        # Save output
        # Fix: Add unique suffix to avoid file overwrite
        output_file = os.path.join(tempfile.gettempdir(), f"standard_output_{int(time.time())}.wav")
        sf.write(output_file, waveform, 48000)
        logger.info(f"Output saved to: {output_file}")
        
        # Log memory usage
        if torch.cuda.is_available():
            logger.info(f"GPU memory allocated: {torch.cuda.memory_allocated() / (1024**2):.2f} MB")
            logger.info(f"GPU memory reserved: {torch.cuda.memory_reserved() / (1024**2):.2f} MB")
        
        return {
            "success": True,
            "model_load_time": model_load_time,
            "process_time": process_time,
            "output_file": output_file
        }
        
    except Exception as e:
        logger.error(f"Error in standard processing: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }

def test_windows11_optimization(input_file):
    """Test audio processing with Windows 11 optimizations"""
    logger.info("=== Testing Windows 11 Optimizations ===")
    
    try:
        # Fix: More robust Windows 11 check (see log_system_info)
        is_windows11 = False
        if platform.system() == "Windows":
            try:
                release = platform.release()
                version = platform.version().split('.')
                if (release == "10" and len(version) >= 3 and int(version[2]) >= 22000) or release == "11":
                    is_windows11 = True
            except Exception as e:
                logger.error(f"Error checking Windows version: {e}")
        
        if not is_windows11:
            logger.info("Not running on Windows 11, skipping this test")
            return {
                "success": False,
                "error": "Not running on Windows 11"
            }
        
        # Fix: Handle missing modules gracefully
        try:
            from audiosr import build_model, super_resolution
        except ImportError as e:
            logger.error(f"audiosr module not found: {e}")
            return {"success": False, "error": "audiosr module not found. Please install dependencies (see requirements.txt)."}
        try:
            from windows11_optimizations import Windows11Optimizer
        except ImportError as e:
            logger.error(f"windows11_optimizations module not found: {e}")
            return {"success": False, "error": "windows11_optimizations module not found. Please install dependencies (see requirements.txt)."}
        
        # Initialize Windows 11 optimizer
        logger.info("Initializing Windows 11 optimizer...")
        start_time = time.time()
        optimizer = Windows11Optimizer()
        
        # Log system info from optimizer
        logger.info("Windows 11 system info:")
        for key, value in optimizer.system_info.items():
            logger.info(f"  {key}: {value}")
        
        # Apply optimizations
        logger.info("Applying Windows 11 optimizations...")
        optimizations = optimizer.optimize_audio_processing()
        
        # Log optimization results
        logger.info("Optimization results:")
        for key, value in optimizations.items():
            logger.info(f"  {key}: {value}")
        
        # Build model
        logger.info("Building model...")
        model = build_model(model_name="basic", device="cpu")
        
        model_load_time = time.time() - start_time
        logger.info(f"Model loaded in {model_load_time:.2f} seconds")
        
        # Process audio
        logger.info("Processing audio with Windows 11 optimizations...")
        process_start = time.time()
        
        waveform = super_resolution(
            model,
            input_file,
            guidance_scale=3.5,
            ddim_steps=20  # Reduced for faster testing
        )
        
        process_time = time.time() - process_start
        logger.info(f"Audio processed in {process_time:.2f} seconds")
        
        # Save output
        output_file = os.path.join(tempfile.gettempdir(), f"win11_output_{int(time.time())}.wav")
        sf.write(output_file, waveform, 48000)
        logger.info(f"Output saved to: {output_file}")
        
        return {
            "success": True,
            "model_load_time": model_load_time,
            "process_time": process_time,
            "output_file": output_file,
            "optimizations": optimizations
        }
        
    except Exception as e:
        logger.error(f"Error in Windows 11 optimization: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }

def test_amd_acceleration(input_file):
    """Test audio processing with AMD acceleration"""
    logger.info("=== Testing AMD Acceleration ===")
    
    try:
        # Fix: Robust AMD ROCm detection
        has_amd_gpu = False
        if torch.cuda.is_available():
            try:
                hip_version = getattr(torch.version, 'hip', None)
                if hip_version and 'rocm' in hip_version.lower():
                    has_amd_gpu = True
                    logger.info(f"ROCm version: {hip_version}")
            except Exception as e:
                logger.error(f"Error checking AMD GPU: {e}")
        
        if not has_amd_gpu:
            logger.info("AMD GPU with ROCm not detected, skipping this test")
            return {
                "success": False,
                "error": "AMD GPU with ROCm not available"
            }
        
        # Fix: Handle missing modules gracefully
        try:
            from audiosr import build_model, super_resolution
        except ImportError as e:
            logger.error(f"audiosr module not found: {e}")
            return {"success": False, "error": "audiosr module not found. Please install dependencies (see requirements.txt)."}
        try:
            from amd_rocm_accelerator import AMDAccelerator
        except ImportError as e:
            logger.error(f"amd_rocm_accelerator module not found: {e}")
            return {"success": False, "error": "amd_rocm_accelerator module not found. Please install dependencies (see requirements.txt)."}
        
        # Initialize AMD accelerator
        logger.info("Initializing AMD accelerator...")
        start_time = time.time()
        # Fix: Use correct device string for ROCm/AMD ("cuda" is correct for ROCm PyTorch builds)
        accelerator = AMDAccelerator(device="cuda")
        
        # Log AMD GPU info
        logger.info(f"AMD GPU available: {accelerator.is_rocm_available}")
        
        # Build and optimize model
        logger.info("Building model with AMD optimizations...")
        model = build_model(model_name="basic", device="cuda")
        model = accelerator.optimize_model(model)
        
        model_load_time = time.time() - start_time
        logger.info(f"Model loaded and optimized in {model_load_time:.2f} seconds")
        
        # Process audio
        logger.info("Processing audio with AMD acceleration...")
        process_start = time.time()
        
        waveform = super_resolution(
            model,
            input_file,
            guidance_scale=3.5,
            ddim_steps=20  # Reduced for faster testing
        )
        
        # Apply AMD-specific post-processing
        if isinstance(waveform, torch.Tensor):
            waveform = accelerator.process_audio(waveform).cpu().numpy()
        
        process_time = time.time() - process_start
        logger.info(f"Audio processed in {process_time:.2f} seconds")
        
        # Save output
        output_file = os.path.join(tempfile.gettempdir(), f"amd_output_{int(time.time())}.wav")
        sf.write(output_file, waveform, 48000)
        logger.info(f"Output saved to: {output_file}")
        
        # Log GPU memory usage
        logger.info(f"GPU memory allocated: {torch.cuda.memory_allocated() / (1024**2):.2f} MB")
        logger.info(f"GPU memory reserved: {torch.cuda.memory_reserved() / (1024**2):.2f} MB")
        
        return {
            "success": True,
            "model_load_time": model_load_time,
            "process_time": process_time,
            "output_file": output_file
        }
        
    except Exception as e:
        logger.error(f"Error in AMD acceleration: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }

def test_mojo_bridge(input_file):
    """Test audio processing with Mojo bridge"""
    logger.info("=== Testing Mojo Bridge ===")
    
    try:
        # Fix: Handle missing Mojo bridge gracefully
        try:
            from mojo_bridge import MojoBridge
            logger.info("Mojo bridge module imported successfully")
        except ImportError as e:
            logger.error(f"Mojo bridge not available: {e}")
            return {
                "success": False,
                "error": f"Mojo bridge not available: {e}"
            }
        
        # Initialize Mojo bridge
        logger.info("Initializing Mojo bridge...")
        start_time = time.time()
        mojo_bridge = MojoBridge()
        
        # Check if Mojo is available
        if not mojo_bridge.mojo_available:
            logger.info("Mojo runtime not available, skipping this test")
            return {
                "success": False,
                "error": "Mojo runtime not available"
            }
        
        # Compile Mojo implementation
        logger.info("Compiling Mojo implementation...")
        mojo_bridge.compile_mojo()
        
        init_time = time.time() - start_time
        logger.info(f"Mojo bridge initialized in {init_time:.2f} seconds")
        
        # Load audio file
        logger.info("Loading audio file...")
        audio_data, sample_rate = sf.read(input_file)
        
        # Process with Mojo bridge
        logger.info("Processing audio with Mojo bridge...")
        process_start = time.time()
        
        waveform, new_sample_rate = mojo_bridge.upscale_audio(
            audio_data,
            sample_rate,
            48000  # Target sample rate
        )
        
        process_time = time.time() - process_start
        logger.info(f"Audio processed in {process_time:.2f} seconds")
        
        # Save output
        output_file = os.path.join(tempfile.gettempdir(), f"mojo_output_{int(time.time())}.wav")
        sf.write(output_file, waveform, new_sample_rate)
        logger.info(f"Output saved to: {output_file}")
        
        return {
            "success": True,
            "init_time": init_time,
            "process_time": process_time,
            "output_file": output_file
        }
        
    except Exception as e:
        logger.error(f"Error in Mojo bridge: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }

def main():
    parser = argparse.ArgumentParser(description="Test audio upscaler with detailed logging")
    parser.add_argument('--input', help="Path to input audio file (optional)")
    parser.add_argument('--skip-standard', action='store_true', help="Skip standard processing test")
    parser.add_argument('--skip-win11', action='store_true', help="Skip Windows 11 optimization test")
    parser.add_argument('--skip-amd', action='store_true', help="Skip AMD acceleration test")
    parser.add_argument('--skip-mojo', action='store_true', help="Skip Mojo bridge test")
    
    args = parser.parse_args()
    
    logger.info("Starting audio upscaler test")
    logger.info(f"Log file: {log_file}")
    
    # Log system information
    log_system_info()
    
    # Create or use test audio file
    if args.input:
        input_file = args.input
        logger.info(f"Using provided input file: {input_file}")
    else:
        input_file = create_test_audio()
    
    results = {}
    
    # Run tests
    try:
        if not args.skip_standard:
            logger.info("Running standard processing test...")
            results['standard'] = test_standard_processing(input_file)
        if not args.skip_win11:
            logger.info("Running Windows 11 optimization test...")
            results['win11'] = test_windows11_optimization(input_file)
        if not args.skip_amd:
            logger.info("Running AMD acceleration test...")
            results['amd'] = test_amd_acceleration(input_file)
        if not args.skip_mojo:
            logger.info("Running Mojo bridge test...")
            results['mojo'] = test_mojo_bridge(input_file)
    except (KeyboardInterrupt, SystemExit):
        logger.info("Test interrupted by user.")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        return 1
    
    # Print summary
    logger.info("=== Test Summary ===")
    for test_name, result in results.items():
        if result['success']:
            logger.info(f"{test_name.upper()} test: SUCCESS")
            if 'process_time' in result:
                logger.info(f"  Processing time: {result['process_time']:.2f} seconds")
            if 'output_file' in result:
                logger.info(f"  Output file: {result['output_file']}")
        else:
            logger.info(f"{test_name.upper()} test: FAILED - {result.get('error', 'Unknown error')}")
    
    logger.info(f"Complete log available at: {log_file}")
    # Fix: Suggest creating requirements.txt for dependencies
    logger.info("If you encounter missing module errors, please create a requirements.txt with all dependencies.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
