const axios = require('axios');
const config = require('../config');

class LightningBitNet {
  constructor(apiKey = config.lightning.apiKey) {
    this.apiKey = apiKey;
    this.endpoint = config.lightning.bitnetModelEndpoint;
    this.headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Generate a solution using the BitNet 1.58 model
   * @param {string} problemText - The coding problem text
   * @returns {Promise<Object>} - Generated solution
   */
  async generateSolution(problemText) {
    try {
      const prompt = `
Analyze this coding problem and provide a solution:

${problemText}

Format your response as follows:
1. Problem Analysis
2. Approach
3. Code Solution
4. Time Complexity
5. Space Complexity
`;

      const response = await axios.post(
        this.endpoint,
        {
          prompt: prompt,
          max_tokens: 1024,
          temperature: 0.3,
          top_p: 0.9
        },
        { headers: this.headers }
      );

      if (response.data && response.data.generated_text) {
        // Parse the response into structured format
        return this.parseResponse(response.data.generated_text);
      } else {
        throw new Error('Invalid response format from Lightning AI BitNet API');
      }
    } catch (error) {
      console.error('Lightning BitNet API Error:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  }

  /**
   * Parse the generated text into structured solution format
   * @param {string} text - Generated text from the model
   * @returns {Object} - Structured solution object
   */
  parseResponse(text) {
    // Initialize solution object
    const solution = {
      analysis: '',
      approach: '',
      code: '',
      time_complexity: '',
      space_complexity: ''
    };

    // Extract sections using regex patterns
    const analysisMatch = text.match(/Problem Analysis:?([\s\S]*?)(?=Approach:|$)/i);
    if (analysisMatch && analysisMatch[1]) {
      solution.analysis = analysisMatch[1].trim();
    }

    const approachMatch = text.match(/Approach:?([\s\S]*?)(?=Code Solution:|$)/i);
    if (approachMatch && approachMatch[1]) {
      solution.approach = approachMatch[1].trim();
    }

    const codeMatch = text.match(/Code Solution:?([\s\S]*?)(?=Time Complexity:|$)/i);
    if (codeMatch && codeMatch[1]) {
      solution.code = codeMatch[1].trim();
      
      // Ensure code is wrapped in code blocks if not already
      if (!solution.code.startsWith('```')) {
        const language = this.detectLanguage(solution.code);
        solution.code = '```' + language + '\n' + solution.code + '\n```';
      }
    }

    const timeMatch = text.match(/Time Complexity:?([\s\S]*?)(?=Space Complexity:|$)/i);
    if (timeMatch && timeMatch[1]) {
      solution.time_complexity = timeMatch[1].trim();
    }

    const spaceMatch = text.match(/Space Complexity:?([\s\S]*?)(?=$)/i);
    if (spaceMatch && spaceMatch[1]) {
      solution.space_complexity = spaceMatch[1].trim();
    }

    return solution;
  }

  /**
   * Detect programming language from code snippet
   * @param {string} code - Code snippet
   * @returns {string} - Detected language
   */
  detectLanguage(code) {
    // Simple language detection based on keywords and syntax
    if (code.includes('def ') || code.includes('import ') || 'print(' in code) {
      return 'python';
    } else if (code.includes('function ') || code.includes('const ') || code.includes('let ') || code.includes('var ')) {
      return 'javascript';
    } else if (code.includes('public class ') || code.includes('private ') || code.includes('System.out.println')) {
      return 'java';
    } else if (code.includes('#include') || code.includes('int main')) {
      return 'cpp';
    } else {
      return '';
    }
  }
}

module.exports = LightningBitNet;
