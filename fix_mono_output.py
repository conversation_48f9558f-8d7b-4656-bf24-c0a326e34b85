#!/usr/bin/env python3
"""
Fix the mono output issue in AudioSR.
This script modifies the inference.py file to preserve stereo output.
"""

import os
import sys
from pathlib import Path

def fix_mono_output():
    """Fix the mono output issue in the AudioSR code."""
    print("Fixing mono output issue...")
    
    # Path to the inference.py file
    inference_path = Path("audiosr/inference.py")
    
    if not inference_path.exists():
        print(f"Error: {inference_path} not found. Make sure you're in the repository root.")
        return False
    
    # Read the file
    with open(inference_path, 'r') as f:
        content = f.read()
    
    # Check if the file already has the fix
    if "# Preserve stereo if input is stereo" in content:
        print("Mono output fix already applied.")
        return True
    
    # Find the line where the output is returned and modify it to preserve stereo
    if "return waveform[0]" in content:
        modified_content = content.replace(
            "return waveform[0]",
            "# Preserve stereo if input is stereo\n    if is_stereo and len(waveform) > 1:\n        return np.stack([waveform[0], waveform[1]], axis=1)\n    return waveform[0]"
        )
        
        # Write the modified content back
        with open(inference_path, 'w') as f:
            f.write(modified_content)
        
        print("Mono output issue fixed.")
        return True
    else:
        print("Could not find the line to modify. Manual fix required.")
        return False

if __name__ == "__main__":
    if fix_mono_output():
        print("Fix applied successfully.")
        sys.exit(0)
    else:
        print("Failed to apply fix.")
        sys.exit(1)
