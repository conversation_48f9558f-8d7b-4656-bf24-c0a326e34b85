@echo off
echo Audio Upscaling System - Tests
echo ============================
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python 3.6 or higher.
    pause
    exit /b 1
)

REM Check if required modules are installed
python -c "import torch, numpy, soundfile" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing required modules...
    python -m pip install torch numpy soundfile scipy
)

echo Running tests...
echo.

REM Run tests
python test_audio_upscaling.py

echo.
echo Tests completed.
pause
