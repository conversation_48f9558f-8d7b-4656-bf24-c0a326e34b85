"""
Check Python environment
"""

import os
import sys
import platform

def main():
    print("Python Environment Information")
    print("-----------------------------")
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.platform()}")
    print(f"System: {platform.system()}")
    print(f"Python executable: {sys.executable}")
    print(f"Current working directory: {os.getcwd()}")
    
    print("\nPython Path:")
    for path in sys.path:
        print(f"  {path}")
    
    print("\nEnvironment Variables:")
    for key, value in os.environ.items():
        if "PATH" in key or "PYTHON" in key:
            print(f"  {key}: {value}")
    
    print("\nInstalled Packages:")
    try:
        import pkg_resources
        for package in pkg_resources.working_set:
            print(f"  {package.project_name} {package.version}")
    except ImportError:
        print("  pkg_resources not available")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
