# PowerShell script to play RAVE.flac

Write-Host "Playing RAVE.flac"

# Path to the RAVE.flac file
$downloadsDir = [Environment]::GetFolderPath("UserProfile") + "\Downloads"

# Try to find the RAVE.flac file
$raveFile = $null
Get-ChildItem $downloadsDir | ForEach-Object {
    if ($_.Name -like "*RAVE*.flac") {
        $raveFile = $_.FullName
    }
}

if ($null -eq $raveFile) {
    Write-Host "Could not find RAVE.flac in Downloads folder."
    exit 1
}

Write-Host "Found RAVE.flac: $raveFile"

# Play the file using the default system player
Write-Host "Playing: $raveFile"
Invoke-Item $raveFile

Write-Host "Audio playback started. Use the system player controls to control playback."

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
