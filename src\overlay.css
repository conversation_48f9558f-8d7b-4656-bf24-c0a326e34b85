* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: transparent;
  color: #f5f5f5;
  overflow: hidden;
}

.overlay-container {
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.solution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: rgba(20, 20, 20, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.drag-handle {
  width: 100px;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin: 0 auto;
  cursor: move;
}

.controls {
  display: flex;
  gap: 5px;
}

.controls button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.controls button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.solution-content {
  flex: 1;
  overflow: auto;
  padding: 15px;
}

.tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
}

.tab-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
}

.tab-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.tab-btn.active {
  background-color: rgba(52, 152, 219, 0.3);
  color: #fff;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #3498db;
}

h4 {
  font-size: 16px;
  margin-bottom: 8px;
  color: #2ecc71;
}

.content-area {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  line-height: 1.5;
}

.code-area {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  font-family: 'Consolas', 'Monaco', monospace;
  white-space: pre-wrap;
  overflow-x: auto;
}

.complexity-item {
  margin-bottom: 15px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
