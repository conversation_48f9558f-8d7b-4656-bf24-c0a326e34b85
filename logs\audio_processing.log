2025-04-19 21:56:27,703 - INFO - System Information:
2025-04-19 21:56:27,703 - INFO -   platform: Windows-11-10.0.22631-SP0
2025-04-19 21:56:27,704 - INFO -   processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-04-19 21:56:27,704 - INFO -   python_version: 3.13.3
2025-04-19 21:56:27,704 - INFO -   cpu_count: 4
2025-04-19 21:56:27,705 - INFO -   logical_cpu_count: 8
2025-04-19 21:56:27,705 - INFO -   memory_total: 7.326290130615234
2025-04-19 21:56:27,705 - INFO -   memory_available: 1.2025375366210938
2025-04-19 21:56:27,909 - INFO - Output will be saved to: C:\Users\<USER>\Downloads\upscaled\RAVE_upscaled.wav
2025-04-19 21:56:28,017 - INFO - Initial CPU usage: 7.1%
2025-04-19 21:56:28,017 - INFO - Initial memory usage: 83.2%
2025-04-19 21:56:28,018 - INFO - Loading audio file...
2025-04-19 21:56:28,294 - INFO - Loaded audio: (7463713, 2), 44100Hz
2025-04-19 21:56:28,294 - INFO - Audio duration: 169.25 seconds
2025-04-19 21:56:28,294 - INFO - Audio channels: 2
2025-04-19 21:56:28,295 - INFO - Audio sample rate: 44100Hz
2025-04-19 21:56:28,295 - INFO - Processing audio...
2025-04-19 21:57:08,628 - INFO - Processing completed in 40.16 seconds
2025-04-19 21:57:08,642 - INFO - CPU usage during processing: 10.0% -> 79.6%
2025-04-19 21:57:08,643 - INFO - Memory usage during processing: 84.8% -> 61.4%
2025-04-19 21:57:08,643 - INFO - Saving output to: C:\Users\<USER>\Downloads\upscaled\RAVE_upscaled.wav
2025-04-19 21:57:08,663 - ERROR - Error processing audio: Error opening 'C:\\Users\\<USER>\\Downloads\\upscaled\\RAVE_upscaled.wav': System error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\win fix\process_rave.py", line 161, in main
    sf.write(output_file, processed_audio, sample_rate * upscale_factor)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\soundfile.py", line 363, in write
    with SoundFile(file, 'w', samplerate, channels,
         ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   subtype, endian, format, closefd,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   compression_level, bitrate_mode) as f:
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\Downloads\\upscaled\\RAVE_upscaled.wav': System error.
2025-04-19 21:57:36,777 - INFO - System Information:
2025-04-19 21:57:36,778 - INFO -   platform: Windows-11-10.0.22631-SP0
2025-04-19 21:57:36,779 - INFO -   processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-04-19 21:57:36,779 - INFO -   python_version: 3.13.3
2025-04-19 21:57:36,779 - INFO -   cpu_count: 4
2025-04-19 21:57:36,779 - INFO -   logical_cpu_count: 8
2025-04-19 21:57:36,780 - INFO -   memory_total: 7.326290130615234
2025-04-19 21:57:36,780 - INFO -   memory_available: 1.5025863647460938
2025-04-19 21:57:36,793 - INFO - Output will be saved to: C:\Users\<USER>\Documents\augment-projects\win fix\output\RAVE_upscaled.wav
2025-04-19 21:57:36,905 - INFO - Initial CPU usage: 6.1%
2025-04-19 21:57:36,905 - INFO - Initial memory usage: 79.6%
2025-04-19 21:57:36,906 - INFO - Loading audio file...
2025-04-19 21:57:37,130 - INFO - Loaded audio: (7463713, 2), 44100Hz
2025-04-19 21:57:37,130 - INFO - Audio duration: 169.25 seconds
2025-04-19 21:57:37,130 - INFO - Audio channels: 2
2025-04-19 21:57:37,131 - INFO - Audio sample rate: 44100Hz
2025-04-19 21:57:37,131 - INFO - Processing audio...
2025-04-19 21:58:30,076 - INFO - Processing completed in 52.79 seconds
2025-04-19 21:58:30,086 - INFO - CPU usage during processing: 58.8% -> 10.7%
2025-04-19 21:58:30,087 - INFO - Memory usage during processing: 81.1% -> 63.1%
2025-04-19 21:58:30,087 - INFO - Saving output to: C:\Users\<USER>\Documents\augment-projects\win fix\output\RAVE_upscaled.wav
2025-04-19 21:58:30,493 - INFO - File saved in 0.41 seconds
2025-04-19 21:58:30,496 - INFO - Input file size: 16.28 MB
2025-04-19 21:58:30,496 - INFO - Output file size: 56.94 MB
2025-04-19 21:58:30,497 - INFO - Processing complete!
2025-04-19 21:58:30,497 - INFO - Original sample rate: 44100Hz
2025-04-19 21:58:30,498 - INFO - Upscaled sample rate: 88200Hz
2025-04-19 21:58:30,498 - INFO - Upscale factor: 2x
2025-04-19 21:58:30,498 - INFO - Playing processed file...
2025-04-19 21:58:31,022 - INFO - File opened in default media player
2025-04-19 21:58:31,023 - INFO - Log file created at: C:\Users\<USER>\Documents\augment-projects\win fix\logs\audio_processing.log
2025-04-19 22:01:03,822 - INFO - System Information:
2025-04-19 22:01:03,823 - INFO -   platform: Windows-11-10.0.22631-SP0
2025-04-19 22:01:03,823 - INFO -   processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-04-19 22:01:03,823 - INFO -   python_version: 3.13.3
2025-04-19 22:01:03,823 - INFO -   cpu_count: 4
2025-04-19 22:01:03,824 - INFO -   logical_cpu_count: 8
2025-04-19 22:01:03,824 - INFO -   memory_total: 7.326290130615234
2025-04-19 22:01:03,824 - INFO -   memory_available: 1.1796493530273438
2025-04-19 22:01:03,839 - INFO - Output will be saved to: C:\Users\<USER>\Documents\augment-projects\win fix\output\RAVE_upscaled.wav
2025-04-19 22:01:03,945 - INFO - Initial CPU usage: 4.1%
2025-04-19 22:01:03,946 - INFO - Initial memory usage: 83.9%
2025-04-19 22:01:03,947 - INFO - Loading audio file...
2025-04-19 22:01:04,298 - INFO - Loaded audio: (7463713, 2), 44100Hz
2025-04-19 22:01:04,298 - INFO - Audio duration: 169.25 seconds
2025-04-19 22:01:04,299 - INFO - Audio channels: 2
2025-04-19 22:01:04,299 - INFO - Audio sample rate: 44100Hz
2025-04-19 22:01:04,300 - INFO - Processing audio...
2025-04-19 22:01:04,408 - INFO - Processing parameters:
2025-04-19 22:01:04,408 - INFO -   Upscale factor: 2x
2025-04-19 22:01:04,408 - INFO -   Filter size: 64
2025-04-19 22:01:04,409 - INFO -   High frequency boost: 5.0%
2025-04-19 22:01:04,409 - INFO -   Low frequency boost: 3.0%
2025-04-19 22:01:40,503 - INFO - Applying limiter (peak value: 1.051)
2025-04-19 22:01:40,832 - INFO - New peak value: 0.950
2025-04-19 22:01:40,965 - INFO - Processing completed in 36.53 seconds
2025-04-19 22:01:40,966 - INFO - CPU usage during processing: 0.0% -> 12.2%
2025-04-19 22:01:40,967 - INFO - Memory usage during processing: 85.4% -> 57.4%
2025-04-19 22:01:40,968 - INFO - Saving output to: C:\Users\<USER>\Documents\augment-projects\win fix\output\RAVE_upscaled.wav
2025-04-19 22:01:40,994 - ERROR - Error processing audio: Error opening 'C:\\Users\\<USER>\\Documents\\augment-projects\\win fix\\output\\RAVE_upscaled.wav': System error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\win fix\process_rave.py", line 228, in main
    sf.write(output_file, processed_audio, sample_rate * upscale_factor)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\soundfile.py", line 363, in write
    with SoundFile(file, 'w', samplerate, channels,
         ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   subtype, endian, format, closefd,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   compression_level, bitrate_mode) as f:
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\Documents\\augment-projects\\win fix\\output\\RAVE_upscaled.wav': System error.
2025-04-19 22:02:39,559 - INFO - System Information:
2025-04-19 22:02:39,560 - INFO -   platform: Windows-11-10.0.22631-SP0
2025-04-19 22:02:39,560 - INFO -   processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-04-19 22:02:39,560 - INFO -   python_version: 3.13.3
2025-04-19 22:02:39,561 - INFO -   cpu_count: 4
2025-04-19 22:02:39,561 - INFO -   logical_cpu_count: 8
2025-04-19 22:02:39,562 - INFO -   memory_total: 7.326290130615234
2025-04-19 22:02:39,562 - INFO -   memory_available: 1.5784149169921875
2025-04-19 22:02:39,597 - INFO - Output will be saved to: C:\Users\<USER>\Documents\augment-projects\win fix\output\RAVE_upscaled.wav
2025-04-19 22:02:39,719 - INFO - Initial CPU usage: 86.2%
2025-04-19 22:02:39,720 - INFO - Initial memory usage: 78.4%
2025-04-19 22:02:39,721 - INFO - Loading audio file...
2025-04-19 22:02:40,241 - INFO - Loaded audio: (7463713, 2), 44100Hz
2025-04-19 22:02:40,242 - INFO - Audio duration: 169.25 seconds
2025-04-19 22:02:40,242 - INFO - Audio channels: 2
2025-04-19 22:02:40,242 - INFO - Audio sample rate: 44100Hz
2025-04-19 22:02:40,243 - INFO - Processing audio...
2025-04-19 22:02:40,350 - INFO - Processing parameters:
2025-04-19 22:02:40,350 - INFO -   Upscale factor: 2x
2025-04-19 22:02:40,352 - INFO -   Filter size: 64
2025-04-19 22:02:40,353 - INFO -   High frequency boost: 5.0%
2025-04-19 22:02:40,353 - INFO -   Low frequency boost: 3.0%
2025-04-19 22:03:16,981 - INFO - Applying limiter (peak value: 1.051)
2025-04-19 22:03:17,162 - INFO - New peak value: 0.950
2025-04-19 22:03:17,279 - INFO - Processing completed in 36.92 seconds
2025-04-19 22:03:17,280 - INFO - CPU usage during processing: 8.2% -> 3.9%
2025-04-19 22:03:17,280 - INFO - Memory usage during processing: 79.3% -> 61.7%
2025-04-19 22:03:17,280 - INFO - Creating a version with original sample rate
2025-04-19 22:03:30,738 - INFO - Applying limiter to natural version (peak value: 1.003)
2025-04-19 22:03:30,825 - INFO - New peak value for natural version: 0.950
2025-04-19 22:03:30,826 - INFO - Saving natural version to: C:\Users\<USER>\Documents\augment-projects\win fix\output\natural.wav
2025-04-19 22:03:31,198 - INFO - Natural version saved in 0.37 seconds (sample rate: 44100Hz)
2025-04-19 22:03:31,198 - INFO - Saving upscaled version to: C:\Users\<USER>\Documents\augment-projects\win fix\output\upscaled.wav
2025-04-19 22:03:31,877 - INFO - Upscaled version saved in 0.68 seconds (sample rate: 88200Hz)
2025-04-19 22:03:31,879 - INFO - Input file size: 16.28 MB
2025-04-19 22:03:31,880 - INFO - Natural version file size: 28.47 MB
2025-04-19 22:03:31,880 - INFO - Upscaled version file size: 56.94 MB
2025-04-19 22:03:31,881 - INFO - Processing complete!
2025-04-19 22:03:31,881 - INFO - Original sample rate: 44100Hz
2025-04-19 22:03:31,881 - INFO - Upscaled sample rate: 88200Hz
2025-04-19 22:03:31,881 - INFO - Upscale factor: 2x
2025-04-19 22:03:31,882 - INFO - Playing natural version file...
2025-04-19 22:03:32,417 - INFO - Natural version file opened in default media player
2025-04-19 22:03:32,417 - INFO - 
Two versions have been created:
2025-04-19 22:03:32,418 - INFO - 1. Natural version (44.1kHz): C:\Users\<USER>\Documents\augment-projects\win fix\output\natural.wav
2025-04-19 22:03:32,419 - INFO - 2. High-resolution version (88.2kHz): C:\Users\<USER>\Documents\augment-projects\win fix\output\upscaled.wav
2025-04-19 22:03:32,420 - INFO - 
The natural version is playing now. To play the high-resolution version, open:
2025-04-19 22:03:32,421 - INFO - C:\Users\<USER>\Documents\augment-projects\win fix\output\upscaled.wav
2025-04-19 22:03:32,421 - INFO - Log file created at: C:\Users\<USER>\Documents\augment-projects\win fix\logs\audio_processing.log
