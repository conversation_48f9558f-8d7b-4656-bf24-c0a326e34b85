// Content script for NeuralCodeAssist extension

// Create overlay container
function createOverlay() {
  // Check if overlay already exists
  if (document.getElementById('neural-code-assist-overlay')) {
    return;
  }
  
  // Create overlay container
  const overlay = document.createElement('div');
  overlay.id = 'neural-code-assist-overlay';
  overlay.className = 'neural-code-assist-hidden';
  
  // Set sci-fi themed styles
  overlay.innerHTML = `
    <style>
      #neural-code-assist-overlay {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 80vh;
        background-color: rgba(10, 14, 25, 0.85);
        border: 1px solid rgba(64, 220, 255, 0.7);
        border-radius: 8px;
        box-shadow: 0 0 15px rgba(64, 220, 255, 0.5), 0 0 30px rgba(64, 220, 255, 0.3);
        color: #e0f7ff;
        font-family: 'Rajdhani', 'Orbitron', sans-serif;
        z-index: 9999999;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        backdrop-filter: blur(5px);
      }
      
      #neural-code-assist-overlay.neural-code-assist-hidden {
        opacity: 0;
        pointer-events: none;
      }
      
      .neural-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: linear-gradient(90deg, rgba(10, 14, 25, 0.9) 0%, rgba(32, 58, 96, 0.9) 100%);
        border-bottom: 1px solid rgba(64, 220, 255, 0.5);
      }
      
      .neural-title {
        font-size: 16px;
        font-weight: bold;
        color: #40dcff;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      
      .neural-controls {
        display: flex;
        gap: 8px;
      }
      
      .neural-btn {
        background: none;
        border: 1px solid rgba(64, 220, 255, 0.5);
        color: #40dcff;
        border-radius: 4px;
        padding: 3px 8px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .neural-btn:hover {
        background-color: rgba(64, 220, 255, 0.2);
      }
      
      .neural-tabs {
        display: flex;
        background-color: rgba(10, 14, 25, 0.7);
        border-bottom: 1px solid rgba(64, 220, 255, 0.3);
      }
      
      .neural-tab {
        padding: 8px 15px;
        cursor: pointer;
        color: rgba(224, 247, 255, 0.7);
        font-size: 14px;
        transition: all 0.2s ease;
      }
      
      .neural-tab:hover {
        background-color: rgba(64, 220, 255, 0.1);
      }
      
      .neural-tab.active {
        color: #40dcff;
        border-bottom: 2px solid #40dcff;
        background-color: rgba(64, 220, 255, 0.1);
      }
      
      .neural-content {
        padding: 15px;
        max-height: 60vh;
        overflow-y: auto;
      }
      
      .neural-pane {
        display: none;
      }
      
      .neural-pane.active {
        display: block;
      }
      
      .neural-code {
        font-family: 'JetBrains Mono', 'Consolas', monospace;
        background-color: rgba(10, 14, 25, 0.7);
        padding: 10px;
        border-radius: 4px;
        border-left: 3px solid #40dcff;
        white-space: pre-wrap;
        overflow-x: auto;
        font-size: 13px;
        line-height: 1.5;
      }
      
      .neural-status {
        display: flex;
        justify-content: space-between;
        padding: 8px 15px;
        font-size: 12px;
        background-color: rgba(10, 14, 25, 0.7);
        border-top: 1px solid rgba(64, 220, 255, 0.3);
      }
      
      .neural-model {
        color: #40dcff;
      }
      
      .neural-quick-solution {
        color: #ffcc00;
      }
      
      .neural-final-solution {
        color: #00ff9d;
      }
      
      /* Syntax highlighting */
      .neural-keyword { color: #ff79c6; }
      .neural-string { color: #f1fa8c; }
      .neural-comment { color: #6272a4; }
      .neural-function { color: #50fa7b; }
      .neural-number { color: #bd93f9; }
      .neural-operator { color: #ff79c6; }
      
      /* Scrollbar styling */
      .neural-content::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      .neural-content::-webkit-scrollbar-track {
        background: rgba(10, 14, 25, 0.5);
      }
      
      .neural-content::-webkit-scrollbar-thumb {
        background: rgba(64, 220, 255, 0.5);
        border-radius: 3px;
      }
      
      .neural-content::-webkit-scrollbar-thumb:hover {
        background: rgba(64, 220, 255, 0.7);
      }
      
      /* Sci-fi decorations */
      .neural-decoration {
        position: absolute;
        width: 30px;
        height: 2px;
        background-color: rgba(64, 220, 255, 0.7);
      }
      
      .neural-decoration-1 {
        top: 15px;
        left: -15px;
        transform: rotate(45deg);
      }
      
      .neural-decoration-2 {
        top: 15px;
        right: -15px;
        transform: rotate(-45deg);
      }
      
      @keyframes pulse {
        0% { opacity: 0.7; }
        50% { opacity: 1; }
        100% { opacity: 0.7; }
      }
      
      .neural-pulse {
        animation: pulse 2s infinite;
      }
    </style>
    
    <div class="neural-decoration neural-decoration-1"></div>
    <div class="neural-decoration neural-decoration-2"></div>
    
    <div class="neural-header">
      <div class="neural-title">Neural<span style="color: #ff79c6;">Code</span>Assist</div>
      <div class="neural-controls">
        <button class="neural-btn" id="neural-copy-btn">Copy</button>
        <button class="neural-btn" id="neural-hide-btn">Hide</button>
      </div>
    </div>
    
    <div class="neural-tabs">
      <div class="neural-tab active" data-tab="analysis">Analysis</div>
      <div class="neural-tab" data-tab="approach">Approach</div>
      <div class="neural-tab" data-tab="solution">Solution</div>
      <div class="neural-tab" data-tab="complexity">Complexity</div>
    </div>
    
    <div class="neural-content">
      <div class="neural-pane active" id="neural-analysis">
        <div id="neural-analysis-content">Waiting for problem analysis...</div>
      </div>
      
      <div class="neural-pane" id="neural-approach">
        <div id="neural-approach-content">Waiting for solution approach...</div>
      </div>
      
      <div class="neural-pane" id="neural-solution">
        <pre class="neural-code" id="neural-solution-content">// Waiting for solution code...</pre>
      </div>
      
      <div class="neural-pane" id="neural-complexity">
        <h4 style="color: #40dcff; margin-bottom: 10px;">Time Complexity</h4>
        <div id="neural-time-complexity">Analyzing...</div>
        
        <h4 style="color: #40dcff; margin: 15px 0 10px;">Space Complexity</h4>
        <div id="neural-space-complexity">Analyzing...</div>
      </div>
    </div>
    
    <div class="neural-status">
      <div id="neural-solution-status" class="neural-quick-solution">Initializing...</div>
      <div id="neural-model-info" class="neural-model">CodeGen</div>
    </div>
  `;
  
  document.body.appendChild(overlay);
  
  // Add event listeners
  document.getElementById('neural-hide-btn').addEventListener('click', () => {
    toggleOverlay();
  });
  
  document.getElementById('neural-copy-btn').addEventListener('click', () => {
    const solutionCode = document.getElementById('neural-solution-content').textContent;
    navigator.clipboard.writeText(solutionCode).then(() => {
      const copyBtn = document.getElementById('neural-copy-btn');
      copyBtn.textContent = 'Copied!';
      setTimeout(() => {
        copyBtn.textContent = 'Copy';
      }, 2000);
    });
  });
  
  // Tab switching
  document.querySelectorAll('.neural-tab').forEach(tab => {
    tab.addEventListener('click', () => {
      // Remove active class from all tabs and panes
      document.querySelectorAll('.neural-tab').forEach(t => t.classList.remove('active'));
      document.querySelectorAll('.neural-pane').forEach(p => p.classList.remove('active'));
      
      // Add active class to clicked tab and corresponding pane
      tab.classList.add('active');
      const tabName = tab.getAttribute('data-tab');
      document.getElementById(`neural-${tabName}`).classList.add('active');
    });
  });
  
  // Add keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Ctrl+B or Command+B to toggle overlay
    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
      e.preventDefault();
      toggleOverlay();
    }
    
    // Ctrl+H or Command+H to capture problem
    if ((e.ctrlKey || e.metaKey) && e.key === 'h') {
      e.preventDefault();
      captureProblem();
    }
    
    // Ctrl+Arrow keys to move overlay
    if ((e.ctrlKey || e.metaKey) && ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
      e.preventDefault();
      moveOverlay(e.key.replace('Arrow', '').toLowerCase());
    }
  });
}

// Toggle overlay visibility
function toggleOverlay() {
  const overlay = document.getElementById('neural-code-assist-overlay');
  if (overlay) {
    overlay.classList.toggle('neural-code-assist-hidden');
    
    // Send message to background script
    chrome.runtime.sendMessage({
      type: 'toggle-overlay'
    });
  }
}

// Move overlay
function moveOverlay(direction) {
  chrome.runtime.sendMessage({
    type: 'move-overlay',
    direction: direction
  });
}

// Capture problem
function captureProblem() {
  // Try to find problem statement based on the current website
  let problemText = '';
  let problemElement = null;
  
  // Different selectors for different coding platforms
  if (window.location.hostname.includes('leetcode.com')) {
    problemElement = document.querySelector('.question-content');
  } else if (window.location.hostname.includes('hackerrank.com')) {
    problemElement = document.querySelector('.challenge-body-html');
  } else if (window.location.hostname.includes('codechef.com')) {
    problemElement = document.querySelector('.problem-statement');
  } else if (window.location.hostname.includes('codeforces.com')) {
    problemElement = document.querySelector('.problem-statement');
  } else if (window.location.hostname.includes('codesignal.com')) {
    problemElement = document.querySelector('.markdown');
  } else if (window.location.hostname.includes('topcoder.com')) {
    problemElement = document.querySelector('.problem-statement');
  }
  
  if (problemElement) {
    problemText = problemElement.textContent.trim();
    
    // Send problem text to background script
    chrome.runtime.sendMessage({
      type: 'capture-problem-element',
      text: problemText
    });
    
    // Update status
    const statusElement = document.getElementById('neural-solution-status');
    if (statusElement) {
      statusElement.textContent = 'Analyzing problem...';
      statusElement.className = 'neural-quick-solution neural-pulse';
    }
  } else {
    // If problem element not found, use screenshot method
    chrome.runtime.sendMessage({
      type: 'capture-problem'
    });
  }
}

// Format code with syntax highlighting
function formatCode(code) {
  if (!code) return '';
  
  // Remove code block markers
  code = code.replace(/```\w*\n/g, '').replace(/```$/g, '');
  
  // Highlight keywords
  const keywords = ['function', 'return', 'if', 'else', 'for', 'while', 'class', 'const', 'let', 'var', 'import', 'export', 'from', 'try', 'catch', 'switch', 'case', 'break', 'default', 'continue', 'new', 'this', 'super', 'extends', 'def', 'lambda', 'in', 'not', 'and', 'or', 'True', 'False', 'None'];
  
  keywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'g');
    code = code.replace(regex, `<span class="neural-keyword">${keyword}</span>`);
  });
  
  // Highlight strings
  code = code.replace(/(["'])(.*?)\1/g, '<span class="neural-string">$1$2$1</span>');
  
  // Highlight comments
  code = code.replace(/\/\/(.*?)$/gm, '<span class="neural-comment">//$1</span>');
  code = code.replace(/\/\*(.*?)\*\//gs, '<span class="neural-comment">/*$1*/</span>');
  code = code.replace(/#(.*?)$/gm, '<span class="neural-comment">#$1</span>');
  
  // Highlight function names
  code = code.replace(/function\s+(\w+)/g, 'function <span class="neural-function">$1</span>');
  code = code.replace(/def\s+(\w+)/g, 'def <span class="neural-function">$1</span>');
  
  // Highlight numbers
  code = code.replace(/\b(\d+)\b/g, '<span class="neural-number">$1</span>');
  
  return code;
}

// Format text with markdown-like syntax
function formatText(text) {
  if (!text) return '';
  
  // Replace line breaks with <br>
  text = text.replace(/\n/g, '<br>');
  
  // Bold text between ** **
  text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // Italic text between * *
  text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // Code snippets between ` `
  text = text.replace(/`(.*?)`/g, '<code>$1</code>');
  
  return text;
}

// Update solution content
function updateSolution(solution, type) {
  const analysisElement = document.getElementById('neural-analysis-content');
  const approachElement = document.getElementById('neural-approach-content');
  const solutionElement = document.getElementById('neural-solution-content');
  const timeElement = document.getElementById('neural-time-complexity');
  const spaceElement = document.getElementById('neural-space-complexity');
  const statusElement = document.getElementById('neural-solution-status');
  const modelElement = document.getElementById('neural-model-info');
  
  if (analysisElement && solution.analysis) {
    analysisElement.innerHTML = formatText(solution.analysis);
  }
  
  if (approachElement && solution.approach) {
    approachElement.innerHTML = formatText(solution.approach);
  }
  
  if (solutionElement && solution.code) {
    solutionElement.innerHTML = formatCode(solution.code);
  }
  
  if (timeElement && solution.time_complexity) {
    timeElement.innerHTML = formatText(solution.time_complexity);
  }
  
  if (spaceElement && solution.space_complexity) {
    spaceElement.innerHTML = formatText(solution.space_complexity);
  }
  
  if (statusElement) {
    if (type === 'quick-solution') {
      statusElement.textContent = 'Quick Solution (CodeGen)';
      statusElement.className = 'neural-quick-solution';
    } else {
      statusElement.textContent = 'Final Solution';
      statusElement.className = 'neural-final-solution';
    }
  }
  
  if (modelElement && solution.model) {
    modelElement.textContent = solution.model;
  }
}

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'solution' || message.type === 'quick-solution') {
    // Create overlay if it doesn't exist
    if (!document.getElementById('neural-code-assist-overlay')) {
      createOverlay();
    }
    
    // Update solution content
    updateSolution(message.solution, message.type);
    
    // Show overlay if hidden
    const overlay = document.getElementById('neural-code-assist-overlay');
    if (overlay.classList.contains('neural-code-assist-hidden')) {
      overlay.classList.remove('neural-code-assist-hidden');
    }
    
    sendResponse({ status: 'updated' });
  }
  
  return true; // Keep the message channel open for async responses
});

// Initialize overlay when content script loads
createOverlay();

// Add sci-fi themed font
const fontLink = document.createElement('link');
fontLink.rel = 'stylesheet';
fontLink.href = 'https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;600&family=JetBrains+Mono&family=Orbitron:wght@500&display=swap';
document.head.appendChild(fontLink);
