package com.scrcpy.samsunghelper;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

public class MainActivity extends Activity {
    private TextView statusText;
    private Button startButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        statusText = findViewById(R.id.status_text);
        startButton = findViewById(R.id.start_button);

        startButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                statusText.setText("Preparing exploit...");
                startExploit();
            }
        });
    }

    private void startExploit() {
        // Disable battery optimization for Samsung TTS
        try {
            Intent intent = new Intent();
            intent.setAction("android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS");
            intent.setData(android.net.Uri.parse("package:com.samsung.SMT"));
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Start the exploit service
        Intent serviceIntent = new Intent(this, ExploitService.class);
        startService(serviceIntent);
        
        statusText.setText("Exploit service started. Please wait...");
    }
}
