"""
Simple script to check if a file exists and print information about it
"""

import os
import sys

def main():
    # File to check
    file_path = r"C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac"
    
    print(f"Checking file: {file_path}")
    
    if os.path.exists(file_path):
        print(f"File exists: {file_path}")
        print(f"File size: {os.path.getsize(file_path) / (1024 * 1024):.2f} MB")
        
        # Try to get more information if soundfile is available
        try:
            import soundfile as sf
            info = sf.info(file_path)
            print(f"Sample rate: {info.samplerate} Hz")
            print(f"Channels: {info.channels}")
            print(f"Duration: {info.duration:.2f} seconds")
            print(f"Format: {info.format}")
        except ImportError:
            print("soundfile module not available")
        except Exception as e:
            print(f"Error getting audio info: {e}")
    else:
        print(f"File does not exist: {file_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
