import sys
import json
import argparse
from llama_cpp import <PERSON><PERSON><PERSON>

def main():
    parser = argparse.ArgumentParser(description='Run inference with local LLM')
    parser.add_argument('--model', type=str, required=True, help='Path to model file')
    parser.add_argument('--prompt', type=str, required=True, help='Prompt for generation')
    parser.add_argument('--max_tokens', type=int, default=512, help='Maximum tokens to generate')
    parser.add_argument('--temperature', type=float, default=0.7, help='Temperature for sampling')
    parser.add_argument('--context_size', type=int, default=2048, help='Context window size')
    
    args = parser.parse_args()
    
    try:
        # Load the model
        llm = Llama(
            model_path=args.model,
            n_ctx=args.context_size,
            n_threads=4,  # Adjust based on your CPU
            n_gpu_layers=0  # Set to higher value if using GPU
        )
        
        # Generate response
        output = llm(
            args.prompt,
            max_tokens=args.max_tokens,
            temperature=args.temperature,
            stop=["</s>", "User:", "System:"],  # Common stop tokens
            echo=False
        )
        
        # Extract and format the response
        if 'choices' in output and len(output['choices']) > 0:
            response = output['choices'][0]['text'].strip()
        else:
            response = output.get('generation', '').strip()
        
        # Parse the response into structured format
        result = parse_response(response)
        
        # Output as JSON
        print(json.dumps(result))
        
    except Exception as e:
        error_result = {
            "error": str(e),
            "analysis": "Error generating solution with local model",
            "approach": "Please try again or use online models",
            "code": "# Error occurred during local inference",
            "time_complexity": "N/A",
            "space_complexity": "N/A"
        }
        print(json.dumps(error_result))
        sys.exit(1)

def parse_response(text):
    # Initialize solution object
    solution = {
        "analysis": "",
        "approach": "",
        "code": "",
        "time_complexity": "",
        "space_complexity": ""
    }
    
    # Extract sections using simple text parsing
    sections = text.split("\n\n")
    
    for i, section in enumerate(sections):
        if i == 0 or "analysis" in section.lower() or "problem" in section.lower():
            solution["analysis"] += section.strip() + "\n"
        elif "approach" in section.lower() or "solution" in section.lower():
            solution["approach"] += section.strip() + "\n"
        elif "code" in section.lower() or "```" in section:
            solution["code"] += section.strip() + "\n"
        elif "time" in section.lower() and "complex" in section.lower():
            solution["time_complexity"] += section.strip() + "\n"
        elif "space" in section.lower() and "complex" in section.lower():
            solution["space_complexity"] += section.strip() + "\n"
    
    # Clean up and ensure code is properly formatted
    if "```" not in solution["code"] and solution["code"].strip():
        solution["code"] = "```python\n" + solution["code"] + "\n```"
    
    return solution

if __name__ == "__main__":
    main()
