"""
Dynamic Range Enhancer for Audio Processing
Focuses specifically on enhancing dynamic range while preserving audio quality
"""

import os
import time
import logging
import numpy as np
import soundfile as sf
from scipy import signal
from typing import Dict, Any, Optional, List, Union, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DynamicRangeEnhancer:
    """
    Enhances dynamic range in audio using advanced DSP techniques
    Based on research from:
    - 'Digital Dynamic Range Compressor Design' (<PERSON><PERSON><PERSON><PERSON> et al., 2012)
    - 'Adaptive Digital Audio Effects' (<PERSON><PERSON><PERSON><PERSON> et al., 2006)
    - 'Transparent Dynamic Range Compression' (Gorlow & Reiss, 2013)
    """
    def __init__(self, 
                 quality_level: int = 2,
                 preserve_transients: bool = True,
                 multiband: bool = True,
                 chunk_size: int = 8192,
                 overlap: int = 1024):
        """
        Initialize the dynamic range enhancer
        
        Args:
            quality_level: Quality level (1=Low, 2=Medium, 3=High)
            preserve_transients: Whether to preserve transients
            multiband: Whether to use multiband processing
            chunk_size: Size of audio chunks for processing
            overlap: Overlap between chunks
        """
        self.quality_level = quality_level
        self.preserve_transients = preserve_transients
        self.multiband = multiband
        self.chunk_size = chunk_size
        self.overlap = overlap
        
        # Initialize parameters based on quality level
        self._init_parameters()
        
        logger.info(f"Dynamic Range Enhancer initialized")
        logger.info(f"  Quality level: {quality_level}")
        logger.info(f"  Preserve transients: {preserve_transients}")
        logger.info(f"  Multiband processing: {multiband}")
    
    def _init_parameters(self):
        """Initialize parameters based on quality level"""
        # Common parameters
        self.attack_time = 0.001  # 1ms attack time
        self.release_time = 0.1  # 100ms release time
        
        # Expansion parameters
        self.expansion_threshold = 0.2  # Threshold for expansion
        self.expansion_ratio = 0.8  # 0.8:1 expansion ratio (lower = more expansion)
        
        # Transient parameters
        self.transient_threshold = 0.08  # Threshold for transient detection
        self.transient_boost = 0.08  # 8% boost for transients
        
        # Multiband parameters
        self.num_bands = 4  # Number of frequency bands
        self.band_crossovers = [100, 1000, 8000]  # Crossover frequencies in Hz
        
        # Quality-specific parameters
        if self.quality_level == 1:  # Low
            self.filter_size = 32
            self.iterations = 1
        elif self.quality_level == 2:  # Medium
            self.filter_size = 64
            self.iterations = 2
        else:  # High
            self.filter_size = 128
            self.iterations = 3
    
    def process_file(self, 
                    input_file: str, 
                    output_file: Optional[str] = None,
                    sample_rate: Optional[int] = None) -> str:
        """
        Process an audio file to enhance dynamic range
        
        Args:
            input_file: Path to input audio file
            output_file: Path to output audio file (optional)
            sample_rate: Target sample rate (optional, will use original if not specified)
            
        Returns:
            Path to the processed audio file
        """
        # Check if input file exists
        if not os.path.isfile(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")
        
        # Generate output filename if not provided
        if output_file is None:
            input_dir = os.path.dirname(input_file)
            input_basename = os.path.basename(input_file)
            input_name, ext = os.path.splitext(input_basename)
            output_dir = os.path.join(input_dir, "dynamic_enhanced")
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{input_name}_enhanced.wav")
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Load audio file
        logger.info(f"Loading audio file: {input_file}")
        audio_data, orig_sample_rate = sf.read(input_file)
        
        # Resample if needed
        if sample_rate is not None and sample_rate != orig_sample_rate:
            logger.info(f"Resampling from {orig_sample_rate}Hz to {sample_rate}Hz")
            if audio_data.ndim == 1:
                # Mono
                audio_data = signal.resample_poly(audio_data, sample_rate, orig_sample_rate)
            else:
                # Multi-channel
                resampled_data = np.zeros((int(len(audio_data) * sample_rate / orig_sample_rate), audio_data.shape[1]))
                for i in range(audio_data.shape[1]):
                    resampled_data[:, i] = signal.resample_poly(audio_data[:, i], sample_rate, orig_sample_rate)
                audio_data = resampled_data
            
            # Update sample rate
            orig_sample_rate = sample_rate
        
        # Log audio properties
        duration = len(audio_data) / orig_sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]
        logger.info(f"Audio duration: {duration:.2f} seconds")
        logger.info(f"Audio channels: {channels}")
        logger.info(f"Audio sample rate: {orig_sample_rate}Hz")
        
        # Analyze original dynamics
        orig_dynamics = self.analyze_dynamics(audio_data)
        logger.info("Original dynamics:")
        logger.info(f"  Dynamic range: {orig_dynamics['dynamic_range']:.2f} dB")
        logger.info(f"  Crest factor: {orig_dynamics['crest_factor']:.2f} dB")
        logger.info(f"  RMS level: {orig_dynamics['rms_level']:.2f} dB")
        
        # Process audio
        logger.info("Processing audio to enhance dynamic range...")
        start_time = time.time()
        
        processed_audio = self.process_audio(audio_data, orig_sample_rate)
        
        process_time = time.time() - start_time
        logger.info(f"Processing completed in {process_time:.2f} seconds")
        
        # Analyze processed dynamics
        proc_dynamics = self.analyze_dynamics(processed_audio)
        logger.info("Processed dynamics:")
        logger.info(f"  Dynamic range: {proc_dynamics['dynamic_range']:.2f} dB")
        logger.info(f"  Crest factor: {proc_dynamics['crest_factor']:.2f} dB")
        logger.info(f"  RMS level: {proc_dynamics['rms_level']:.2f} dB")
        logger.info(f"  Dynamic range improvement: {proc_dynamics['dynamic_range'] - orig_dynamics['dynamic_range']:.2f} dB")
        
        # Save output
        logger.info(f"Saving output to: {output_file}")
        sf.write(output_file, processed_audio, orig_sample_rate)
        
        return output_file
    
    def process_audio(self, 
                     audio_data: np.ndarray, 
                     sample_rate: int) -> np.ndarray:
        """
        Process audio data to enhance dynamic range
        
        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio data
            
        Returns:
            Processed audio data
        """
        # Handle mono vs stereo
        if audio_data.ndim == 1:
            # Mono
            return self._process_mono(audio_data, sample_rate)
        else:
            # Multi-channel
            processed_audio = np.zeros_like(audio_data)
            for i in range(audio_data.shape[1]):
                processed_audio[:, i] = self._process_mono(audio_data[:, i], sample_rate)
            return processed_audio
    
    def _process_mono(self, 
                     audio_data: np.ndarray, 
                     sample_rate: int) -> np.ndarray:
        """Process mono audio data"""
        # Process in chunks if the audio is long
        if len(audio_data) > self.chunk_size:
            return self._process_in_chunks(audio_data, sample_rate)
        
        # Apply multiband processing if enabled
        if self.multiband:
            return self._apply_multiband_processing(audio_data, sample_rate)
        else:
            # Apply fullband processing
            return self._apply_dynamic_expansion(audio_data, sample_rate)
    
    def _process_in_chunks(self, 
                          audio_data: np.ndarray, 
                          sample_rate: int) -> np.ndarray:
        """Process audio in chunks"""
        # Calculate number of chunks
        num_chunks = (len(audio_data) - self.overlap) // (self.chunk_size - self.overlap) + 1
        
        # Initialize output array
        processed_audio = np.zeros_like(audio_data)
        
        logger.info(f"Processing audio in {num_chunks} chunks")
        
        # Process each chunk
        for i in range(num_chunks):
            # Calculate chunk start and end
            start = i * (self.chunk_size - self.overlap)
            end = min(start + self.chunk_size, len(audio_data))
            
            # Extract chunk
            chunk = audio_data[start:end]
            
            # Process chunk
            if self.multiband:
                processed_chunk = self._apply_multiband_processing(chunk, sample_rate)
            else:
                processed_chunk = self._apply_dynamic_expansion(chunk, sample_rate)
            
            # Apply fade in/out for overlap regions
            if i > 0:  # Not the first chunk
                # Create fade-in window for overlap region
                fade_in = np.linspace(0, 1, self.overlap)
                
                # Apply fade-in to current chunk
                processed_chunk[:self.overlap] *= fade_in
                
                # Apply fade-out to previous chunk in overlap region
                fade_out = np.linspace(1, 0, self.overlap)
                processed_audio[start:start+self.overlap] *= fade_out
            
            # Add chunk to output
            chunk_end = min(end, len(audio_data))
            processed_audio[start:chunk_end] += processed_chunk[:chunk_end-start]
        
        return processed_audio
    
    def _apply_multiband_processing(self, 
                                  audio_data: np.ndarray, 
                                  sample_rate: int) -> np.ndarray:
        """Apply multiband dynamic range enhancement"""
        # Calculate band filters
        nyquist = sample_rate / 2
        bands = []
        
        # Extract each band
        for band in range(self.num_bands):
            if band == 0:
                # Lowpass for first band
                bands.append(self._apply_lowpass(audio_data, self.band_crossovers[0] / nyquist, self.filter_size))
            elif band == self.num_bands - 1:
                # Highpass for last band
                bands.append(self._apply_highpass(audio_data, self.band_crossovers[-1] / nyquist, self.filter_size))
            else:
                # Bandpass for middle bands
                bands.append(self._apply_bandpass(
                    audio_data, 
                    self.band_crossovers[band-1] / nyquist, 
                    self.band_crossovers[band] / nyquist, 
                    self.filter_size
                ))
        
        # Process each band
        processed_bands = []
        for i, band_data in enumerate(bands):
            # Adjust expansion parameters based on band
            if i == 0:  # Low band
                threshold = self.expansion_threshold * 1.2  # Higher threshold for low frequencies
                ratio = self.expansion_ratio * 1.1  # Less expansion for low frequencies
            elif i == self.num_bands - 1:  # High band
                threshold = self.expansion_threshold * 0.8  # Lower threshold for high frequencies
                ratio = self.expansion_ratio * 0.9  # More expansion for high frequencies
            else:  # Mid bands
                threshold = self.expansion_threshold
                ratio = self.expansion_ratio
            
            # Apply dynamic expansion to this band
            processed_band = self._apply_dynamic_expansion(
                band_data, 
                sample_rate,
                threshold=threshold,
                ratio=ratio
            )
            
            processed_bands.append(processed_band)
        
        # Sum all bands
        return np.sum(processed_bands, axis=0)
    
    def _apply_dynamic_expansion(self, 
                               audio_data: np.ndarray, 
                               sample_rate: int,
                               threshold: Optional[float] = None,
                               ratio: Optional[float] = None) -> np.ndarray:
        """Apply dynamic range expansion"""
        # Use default parameters if not specified
        if threshold is None:
            threshold = self.expansion_threshold
        if ratio is None:
            ratio = self.expansion_ratio
        
        # Calculate attack and release times in samples
        attack_samples = int(self.attack_time * sample_rate)
        release_samples = int(self.release_time * sample_rate)
        
        # Create a copy of the input
        processed_audio = audio_data.copy()
        
        # Compute envelope
        envelope = np.zeros_like(audio_data)
        
        # Simple envelope follower
        envelope[0] = abs(audio_data[0])
        attack_coef = np.exp(-1.0 / attack_samples)
        release_coef = np.exp(-1.0 / release_samples)
        
        for i in range(1, len(audio_data)):
            env = abs(audio_data[i])
            if env > envelope[i-1]:
                envelope[i] = attack_coef * envelope[i-1] + (1 - attack_coef) * env
            else:
                envelope[i] = release_coef * envelope[i-1] + (1 - release_coef) * env
        
        # Detect transients if enabled
        if self.preserve_transients:
            # Compute derivative to detect transients
            derivative = np.diff(audio_data, prepend=audio_data[0])
            transient_mask = np.abs(derivative) > self.transient_threshold * np.max(np.abs(derivative))
        
        # Apply dynamic range expansion
        threshold_amp = threshold * np.max(envelope)
        
        # Compute gain for expansion
        gain = np.ones_like(audio_data)
        mask = envelope < threshold_amp
        if np.any(mask):
            # Calculate gain for expansion (values below threshold get boosted)
            # The lower the ratio, the more expansion
            gain[mask] = (envelope[mask] / threshold_amp) ** (ratio - 1.0)
            
            # Apply gain to audio
            processed_audio[mask] *= gain[mask]
        
        # Enhance transients if enabled
        if self.preserve_transients:
            # Apply transient boost
            transient_boost = 1.0 + self.transient_boost
            processed_audio[transient_mask] *= transient_boost
        
        # Apply iterations for better results
        for _ in range(1, self.iterations):
            # Re-compute envelope
            envelope = np.zeros_like(processed_audio)
            envelope[0] = abs(processed_audio[0])
            
            for i in range(1, len(processed_audio)):
                env = abs(processed_audio[i])
                if env > envelope[i-1]:
                    envelope[i] = attack_coef * envelope[i-1] + (1 - attack_coef) * env
                else:
                    envelope[i] = release_coef * envelope[i-1] + (1 - release_coef) * env
            
            # Apply additional expansion
            mask = envelope < threshold_amp
            if np.any(mask):
                # Use a gentler ratio for subsequent iterations
                iter_ratio = ratio * 0.5 + 0.5  # Closer to 1.0
                gain[mask] = (envelope[mask] / threshold_amp) ** (iter_ratio - 1.0)
                processed_audio[mask] *= gain[mask]
        
        # Apply limiter to prevent clipping
        max_val = np.max(np.abs(processed_audio))
        if max_val > 0.95:
            scale_factor = 0.95 / max_val
            processed_audio *= scale_factor
        
        return processed_audio
    
    def _apply_lowpass(self, 
                      audio_data: np.ndarray, 
                      cutoff: float, 
                      filter_size: int) -> np.ndarray:
        """Apply lowpass filter"""
        # Design FIR lowpass filter
        b = signal.firwin(filter_size, cutoff, window='hamming')
        
        # Apply filter
        return signal.lfilter(b, 1.0, audio_data)
    
    def _apply_highpass(self, 
                       audio_data: np.ndarray, 
                       cutoff: float, 
                       filter_size: int) -> np.ndarray:
        """Apply highpass filter"""
        # Design FIR highpass filter (spectral inversion of lowpass)
        b = signal.firwin(filter_size, cutoff, window='hamming')
        b = -b
        b[filter_size//2] = b[filter_size//2] + 1
        
        # Apply filter
        return signal.lfilter(b, 1.0, audio_data)
    
    def _apply_bandpass(self, 
                       audio_data: np.ndarray, 
                       low_cutoff: float, 
                       high_cutoff: float, 
                       filter_size: int) -> np.ndarray:
        """Apply bandpass filter"""
        # Design FIR bandpass filter
        b = signal.firwin(filter_size, [low_cutoff, high_cutoff], window='hamming', pass_zero=False)
        
        # Apply filter
        return signal.lfilter(b, 1.0, audio_data)
    
    def analyze_dynamics(self, audio_data: np.ndarray) -> Dict[str, float]:
        """Analyze dynamic range characteristics of audio"""
        # Handle multi-channel audio
        if audio_data.ndim > 1:
            # Analyze first channel
            return self.analyze_dynamics(audio_data[:, 0])
        
        # Calculate peak level
        peak_level = np.max(np.abs(audio_data))
        peak_db = 20 * np.log10(peak_level + 1e-10)
        
        # Calculate RMS level
        rms_level = np.sqrt(np.mean(audio_data ** 2))
        rms_db = 20 * np.log10(rms_level + 1e-10)
        
        # Calculate dynamic range (difference between peak and RMS)
        dynamic_range = peak_db - rms_db
        
        # Calculate crest factor (peak / RMS)
        crest_factor = peak_level / (rms_level + 1e-10)
        crest_factor_db = 20 * np.log10(crest_factor)
        
        # Calculate percentiles for loudness distribution
        sorted_abs = np.sort(np.abs(audio_data))
        percentile_10 = sorted_abs[int(len(sorted_abs) * 0.1)]
        percentile_90 = sorted_abs[int(len(sorted_abs) * 0.9)]
        
        # Calculate loudness range (difference between 10th and 90th percentiles)
        loudness_range = 20 * np.log10((percentile_90 + 1e-10) / (percentile_10 + 1e-10))
        
        return {
            'peak_level': peak_db,
            'rms_level': rms_db,
            'dynamic_range': dynamic_range,
            'crest_factor': crest_factor_db,
            'loudness_range': loudness_range
        }

# Test function
def test_dynamic_range_enhancer():
    """Test the DynamicRangeEnhancer with a simple sine wave"""
    # Create a simple test signal
    sample_rate = 44100
    duration = 2  # seconds
    t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
    
    # Create a test signal with multiple frequencies and varying amplitude
    signal = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz
    
    # Add amplitude modulation to reduce dynamic range
    am = 0.5 + 0.3 * np.sin(2 * np.pi * 0.5 * t)  # 0.5 Hz modulation
    signal = signal * am
    
    # Add some noise
    np.random.seed(42)  # For reproducibility
    signal += 0.05 * np.random.randn(len(t))
    
    # Create enhancer
    enhancer = DynamicRangeEnhancer(quality_level=2, preserve_transients=True, multiband=True)
    
    # Process signal
    processed_signal = enhancer.process_audio(signal, sample_rate)
    
    # Analyze dynamics
    orig_dynamics = enhancer.analyze_dynamics(signal)
    proc_dynamics = enhancer.analyze_dynamics(processed_signal)
    
    # Print results
    print("Original dynamics:")
    print(f"  Dynamic range: {orig_dynamics['dynamic_range']:.2f} dB")
    print(f"  Crest factor: {orig_dynamics['crest_factor']:.2f} dB")
    print(f"  RMS level: {orig_dynamics['rms_level']:.2f} dB")
    
    print("\nProcessed dynamics:")
    print(f"  Dynamic range: {proc_dynamics['dynamic_range']:.2f} dB")
    print(f"  Crest factor: {proc_dynamics['crest_factor']:.2f} dB")
    print(f"  RMS level: {proc_dynamics['rms_level']:.2f} dB")
    
    print(f"\nDynamic range improvement: {proc_dynamics['dynamic_range'] - orig_dynamics['dynamic_range']:.2f} dB")

if __name__ == "__main__":
    test_dynamic_range_enhancer()
