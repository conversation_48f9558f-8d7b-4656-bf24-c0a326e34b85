import tkinter as tk
from tkinter import messagebox, font
import re
import math
import random
import time
from calculator_improved import Calculator, CalculatorError

class SciFiCalculatorGUI:
    # Constants
    MEMORY_ERROR_TITLE = "Memory Error"

    def __init__(self, root):
        self.root = root
        self.root.title("Quantum Nexus Calculator")
        self.root.geometry("500x700")
        self.root.resizable(False, False)
        self.root.configure(bg="#0D1117")

        # Set theme colors
        self.colors = {
            "bg_dark": "#0D1117",
            "bg_medium": "#161B22",
            "accent_blue": "#58A6FF",
            "accent_cyan": "#39D353",
            "accent_purple": "#8957E5",
            "text_primary": "#E6EDF3",
            "text_secondary": "#8B949E",
            "button_hover": "#30363D",
            "display_bg": "#0D1117",
            "memory_bg": "#161B22",
            "error_color": "#F85149"
        }

        # Load custom fonts
        self.load_fonts()

        # Initialize calculator
        self.calculator = Calculator()

        # Create display variables
        self.display_var = tk.StringVar()
        self.history_var = tk.StringVar()
        self.memory_var = tk.StringVar()
        self.memory_value = None

        # Animation variables
        self.particles = []
        self.last_key_time = 0

        # Create UI components
        self.create_ui()

        # Start animations
        self.animate_background()

    def load_fonts(self):
        # Try to load sci-fi fonts if available, otherwise use system fonts
        try:
            self.display_font = font.Font(family="Orbitron", size=24, weight="bold")
            self.button_font = font.Font(family="Orbitron", size=14)
            self.history_font = font.Font(family="Orbitron", size=10)
        except (tk.TclError, RuntimeError):
            self.display_font = font.Font(family="Arial", size=24, weight="bold")
            self.button_font = font.Font(family="Arial", size=14)
            self.history_font = font.Font(family="Arial", size=10)

    def create_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, bg=self.colors["bg_dark"])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title with sci-fi design
        title_frame = tk.Frame(main_frame, bg=self.colors["bg_dark"])
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = tk.Label(
            title_frame,
            text="QUANTUM NEXUS",
            font=font.Font(family=self.display_font.cget("family"), size=18, weight="bold"),
            fg=self.colors["accent_cyan"],
            bg=self.colors["bg_dark"]
        )
        title_label.pack(side=tk.LEFT, padx=5)

        version_label = tk.Label(
            title_frame,
            text="v2.0",
            font=self.history_font,
            fg=self.colors["text_secondary"],
            bg=self.colors["bg_dark"]
        )
        version_label.pack(side=tk.RIGHT, padx=5)

        # Canvas for particle effects
        self.canvas = tk.Canvas(
            main_frame,
            bg=self.colors["bg_dark"],
            highlightthickness=0,
            height=50
        )
        self.canvas.pack(fill=tk.X, pady=(0, 10))

        # History display
        history_frame = tk.Frame(main_frame, bg=self.colors["bg_medium"], bd=1, relief=tk.RAISED)
        history_frame.pack(fill=tk.X, pady=(0, 10))

        history_label = tk.Label(
            history_frame,
            textvariable=self.history_var,
            font=self.history_font,
            fg=self.colors["text_secondary"],
            bg=self.colors["bg_medium"],
            anchor="e",
            padx=10,
            pady=5
        )
        history_label.pack(fill=tk.X)

        # Main display with futuristic border
        display_frame = tk.Frame(main_frame, bg=self.colors["accent_blue"], bd=2, relief=tk.RAISED)
        display_frame.pack(fill=tk.X, pady=(0, 20))

        self.display_entry = tk.Entry(
            display_frame,
            textvariable=self.display_var,
            font=self.display_font,
            fg=self.colors["accent_cyan"],
            bg=self.colors["display_bg"],
            insertbackground=self.colors["accent_cyan"],  # Cursor color
            justify="right",
            bd=10,
            relief=tk.FLAT
        )
        self.display_entry.pack(fill=tk.X, ipady=15)

        # Memory display
        memory_frame = tk.Frame(main_frame, bg=self.colors["memory_bg"])
        memory_frame.pack(fill=tk.X, pady=(0, 10))

        memory_label = tk.Label(
            memory_frame,
            text="MEMORY:",
            font=self.history_font,
            fg=self.colors["text_secondary"],
            bg=self.colors["memory_bg"],
            padx=5,
            pady=2
        )
        memory_label.pack(side=tk.LEFT)

        memory_value = tk.Label(
            memory_frame,
            textvariable=self.memory_var,
            font=self.history_font,
            fg=self.colors["accent_purple"],
            bg=self.colors["memory_bg"],
            padx=5,
            pady=2
        )
        memory_value.pack(side=tk.LEFT)

        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg=self.colors["bg_dark"])
        buttons_frame.pack(fill=tk.BOTH, expand=True)

        # Configure grid
        for i in range(7):
            buttons_frame.rowconfigure(i, weight=1)
        for i in range(5):
            buttons_frame.columnconfigure(i, weight=1)

        # Memory buttons
        memory_buttons = [
            ('MC', 0, 0, self.memory_clear, self.colors["accent_purple"]),
            ('MR', 0, 1, self.memory_recall, self.colors["accent_purple"]),
            ('M+', 0, 2, self.memory_add, self.colors["accent_purple"]),
            ('M-', 0, 3, self.memory_subtract, self.colors["accent_purple"]),
            ('MS', 0, 4, self.memory_store, self.colors["accent_purple"]),
        ]

        # Function buttons
        function_buttons = [
            ('sin', 1, 0, lambda: self.add_function('sin'), self.colors["accent_blue"]),
            ('cos', 1, 1, lambda: self.add_function('cos'), self.colors["accent_blue"]),
            ('tan', 1, 2, lambda: self.add_function('tan'), self.colors["accent_blue"]),
            ('log', 2, 0, lambda: self.add_function('log'), self.colors["accent_blue"]),
            ('ln', 2, 1, lambda: self.add_function('ln'), self.colors["accent_blue"]),
            ('√', 2, 2, lambda: self.add_function('sqrt'), self.colors["accent_blue"]),
            ('|x|', 3, 0, lambda: self.add_function('abs'), self.colors["accent_blue"]),
            ('π', 3, 1, lambda: self.add_text(str(math.pi)), self.colors["accent_blue"]),
            ('e', 3, 2, lambda: self.add_text(str(math.e)), self.colors["accent_blue"]),
        ]

        # Clear and backspace buttons
        special_buttons = [
            ('C', 1, 3, self.clear, self.colors["error_color"]),
            ('⌫', 1, 4, self.backspace, self.colors["error_color"]),
            ('(', 2, 3, lambda: self.add_text('('), self.colors["accent_blue"]),
            (')', 2, 4, lambda: self.add_text(')'), self.colors["accent_blue"]),
            ('^', 3, 3, lambda: self.add_text('^'), self.colors["accent_blue"]),
            ('±', 3, 4, self.toggle_sign, self.colors["accent_blue"]),
        ]

        # Number buttons
        number_buttons = [
            ('7', 4, 0, lambda: self.add_text('7'), self.colors["text_primary"]),
            ('8', 4, 1, lambda: self.add_text('8'), self.colors["text_primary"]),
            ('9', 4, 2, lambda: self.add_text('9'), self.colors["text_primary"]),
            ('4', 5, 0, lambda: self.add_text('4'), self.colors["text_primary"]),
            ('5', 5, 1, lambda: self.add_text('5'), self.colors["text_primary"]),
            ('6', 5, 2, lambda: self.add_text('6'), self.colors["text_primary"]),
            ('1', 6, 0, lambda: self.add_text('1'), self.colors["text_primary"]),
            ('2', 6, 1, lambda: self.add_text('2'), self.colors["text_primary"]),
            ('3', 6, 2, lambda: self.add_text('3'), self.colors["text_primary"]),
            ('0', 7, 1, lambda: self.add_text('0'), self.colors["text_primary"]),
        ]

        # Operator buttons
        operator_buttons = [
            ('÷', 4, 3, lambda: self.add_text('/'), self.colors["accent_cyan"]),
            ('×', 4, 4, lambda: self.add_text('*'), self.colors["accent_cyan"]),
            ('−', 5, 3, lambda: self.add_text('-'), self.colors["accent_cyan"]),
            ('+', 5, 4, lambda: self.add_text('+'), self.colors["accent_cyan"]),
            ('.', 7, 0, lambda: self.add_text('.'), self.colors["text_primary"]),
            ('=', 7, 2, self.calculate, self.colors["accent_cyan"]),
        ]

        # Create all buttons
        all_buttons = memory_buttons + function_buttons + special_buttons + number_buttons + operator_buttons

        for text, row, col, command, color in all_buttons:
            # Special case for equals button
            if text == '=':
                btn = self.create_button(buttons_frame, text, command, color)
                btn.grid(row=row, column=col, columnspan=3, sticky='nsew', padx=3, pady=3)
            else:
                btn = self.create_button(buttons_frame, text, command, color)
                btn.grid(row=row, column=col, sticky='nsew', padx=3, pady=3)

        # Bind keyboard events
        self.root.bind('<Return>', lambda _: self.calculate())
        self.root.bind('<Key>', self.key_pressed)

        # Set focus to the display
        self.display_entry.focus_set()

    def create_button(self, parent, text, command, fg_color):
        """Create a sci-fi styled button"""
        btn = tk.Button(
            parent,
            text=text,
            font=self.button_font,
            fg=fg_color,
            bg=self.colors["bg_medium"],
            activeforeground=fg_color,
            activebackground=self.colors["button_hover"],
            relief=tk.RAISED,
            bd=1,
            padx=5,
            pady=5,
            command=command
        )

        # Add hover effect
        btn.bind("<Enter>", lambda _event, b=btn: self.on_button_hover(b, True))
        btn.bind("<Leave>", lambda _event, b=btn: self.on_button_hover(b, False))

        return btn

    def on_button_hover(self, button, is_hover):
        """Handle button hover effect"""
        if is_hover:
            button.config(bg=self.colors["button_hover"])
            # Create particles on hover
            self.create_particles(5)
        else:
            button.config(bg=self.colors["bg_medium"])

    def add_text(self, text):
        """Add text to the display"""
        current = self.display_var.get()
        cursor_pos = self.display_entry.index(tk.INSERT)

        # Insert at cursor position
        new_text = current[:cursor_pos] + text + current[cursor_pos:]
        self.display_var.set(new_text)

        # Move cursor after inserted text
        self.display_entry.icursor(cursor_pos + len(text))

        # Create particles for visual feedback
        self.create_particles(3)
        self.last_key_time = time.time()

    def add_function(self, func):
        """Add a function to the display"""
        self.add_text(f"{func}(")

    def clear(self):
        """Clear the display"""
        self.display_var.set("")
        self.create_particles(10)  # More particles for clear

    def backspace(self):
        """Remove the last character"""
        current = self.display_var.get()
        cursor_pos = self.display_entry.index(tk.INSERT)

        if cursor_pos > 0:
            new_text = current[:cursor_pos-1] + current[cursor_pos:]
            self.display_var.set(new_text)
            self.display_entry.icursor(cursor_pos - 1)
            self.create_particles(2)

    def toggle_sign(self):
        """Toggle the sign of the current number"""
        current = self.display_var.get()
        cursor_pos = self.display_entry.index(tk.INSERT)

        # Find the number at or before the cursor position
        pattern = r'[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?'
        matches = list(re.finditer(pattern, current))

        for match in reversed(matches):
            start, end = match.span()
            if start <= cursor_pos <= end:
                number = match.group()
                if number.startswith('-'):
                    new_number = number[1:]
                else:
                    new_number = '-' + number

                new_text = current[:start] + new_number + current[end:]
                self.display_var.set(new_text)

                # Adjust cursor position
                new_cursor_pos = cursor_pos
                if number.startswith('-'):
                    new_cursor_pos -= 1
                else:
                    new_cursor_pos += 1

                self.display_entry.icursor(min(new_cursor_pos, len(new_text)))
                self.create_particles(5)
                break

    def calculate(self):
        """Calculate the result of the expression"""
        expression = self.display_var.get()

        if not expression:
            return

        # Update history
        self.history_var.set(expression)

        try:
            # Calculate result
            result = self.calculator.evaluate(expression)

            # Format result
            if result == int(result):
                formatted_result = str(int(result))
            else:
                formatted_result = str(result)

            # Update display
            self.display_var.set(formatted_result)

            # Create success particles
            self.create_particles(15, color=self.colors["accent_cyan"])

        except CalculatorError as e:
            messagebox.showerror("Error", e.message)
            # Create error particles
            self.create_particles(10, color=self.colors["error_color"])
        except Exception as e:
            messagebox.showerror("Error", str(e))
            # Create error particles
            self.create_particles(10, color=self.colors["error_color"])

    def memory_store(self):
        """Store the current value in memory"""
        try:
            value = self.display_var.get()
            if value:
                self.memory_value = self.calculator.evaluate(value)
                self.memory_var.set(str(self.memory_value))
                self.create_particles(5, color=self.colors["accent_purple"])
        except Exception as e:
            messagebox.showerror(self.MEMORY_ERROR_TITLE, str(e))

    def memory_recall(self):
        """Recall the value from memory"""
        if self.memory_value is not None:
            self.display_var.set(str(self.memory_value))
            self.create_particles(5, color=self.colors["accent_purple"])

    def memory_add(self):
        """Add the current value to memory"""
        try:
            value = self.display_var.get()
            if value and self.memory_value is not None:
                result = self.memory_value + self.calculator.evaluate(value)
                self.memory_value = result
                self.memory_var.set(str(result))
                self.create_particles(5, color=self.colors["accent_purple"])
        except Exception as e:
            messagebox.showerror(self.MEMORY_ERROR_TITLE, str(e))

    def memory_subtract(self):
        """Subtract the current value from memory"""
        try:
            value = self.display_var.get()
            if value and self.memory_value is not None:
                result = self.memory_value - self.calculator.evaluate(value)
                self.memory_value = result
                self.memory_var.set(str(result))
                self.create_particles(5, color=self.colors["accent_purple"])
        except Exception as e:
            messagebox.showerror(self.MEMORY_ERROR_TITLE, str(e))

    def memory_clear(self):
        """Clear the memory"""
        self.memory_value = None
        self.memory_var.set("")
        self.create_particles(5, color=self.colors["accent_purple"])

    def key_pressed(self, event):
        """Handle keyboard input"""
        key = event.char

        # Ignore special keys
        if not key:
            return

        # Map keyboard operators to calculator operators
        if key in '0123456789.()+-*/^':
            self.add_text(key)
        elif key == '\r':  # Enter key
            self.calculate()
        elif key == '\x08':  # Backspace
            self.backspace()

        # Create particles for visual feedback
        self.create_particles(2)
        self.last_key_time = time.time()

    def create_particles(self, count, color=None):
        """Create particles for visual effects"""
        for _ in range(count):
            x = random.randint(0, self.canvas.winfo_width())
            y = random.randint(0, self.canvas.winfo_height())
            size = random.randint(2, 5)
            speed = random.uniform(0.5, 2.0)
            angle = random.uniform(0, 2 * math.pi)

            if color is None:
                colors = [self.colors["accent_blue"], self.colors["accent_cyan"], self.colors["accent_purple"]]
                particle_color = random.choice(colors)
            else:
                particle_color = color

            particle = {
                'id': self.canvas.create_oval(x, y, x+size, y+size, fill=particle_color, outline=""),
                'x': x,
                'y': y,
                'size': size,
                'speed': speed,
                'angle': angle,
                'color': particle_color,
                'alpha': 1.0,
                'fade_speed': random.uniform(0.02, 0.05)
            }

            self.particles.append(particle)

    def animate_background(self):
        """Animate the background particles"""
        # Update existing particles
        particles_to_remove = []

        for particle in self.particles:
            # Move particle
            dx = math.cos(particle['angle']) * particle['speed']
            dy = math.sin(particle['angle']) * particle['speed']

            particle['x'] += dx
            particle['y'] += dy

            # Fade out
            particle['alpha'] -= particle['fade_speed']

            if particle['alpha'] <= 0:
                particles_to_remove.append(particle)
                self.canvas.delete(particle['id'])
            else:
                # Update position
                self.canvas.coords(
                    particle['id'],
                    particle['x'],
                    particle['y'],
                    particle['x'] + particle['size'],
                    particle['y'] + particle['size']
                )

                # Update color with alpha
                r, g, b = self.hex_to_rgb(particle['color'])
                # Alpha is not used in the color string for Tkinter
                new_color = f'#{r:02x}{g:02x}{b:02x}'

                self.canvas.itemconfig(particle['id'], fill=new_color)

        # Remove faded particles
        for particle in particles_to_remove:
            self.particles.remove(particle)

        # Add ambient particles occasionally
        if random.random() < 0.1:
            self.create_particles(1)

        # Add typing effect particles
        if time.time() - self.last_key_time < 0.5 and random.random() < 0.3:
            self.create_particles(1)

        # Schedule next animation frame
        self.root.after(30, self.animate_background)

    def hex_to_rgb(self, hex_color):
        """Convert hex color to RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def main():
    root = tk.Tk()
    SciFiCalculatorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
