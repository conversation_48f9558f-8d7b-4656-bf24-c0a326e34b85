"""
<PERSON>ript to play the first audio file from a directory
"""

import os
import sys
import platform
import subprocess

def list_audio_files(directory):
    """List all audio files in the directory"""
    audio_extensions = ['.mp3', '.wav', '.flac', '.ogg', '.m4a', '.aac']
    audio_files = []
    
    for file in os.listdir(directory):
        if any(file.lower().endswith(ext) for ext in audio_extensions):
            audio_files.append(os.path.join(directory, file))
    
    return audio_files

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def main():
    # Check command line arguments
    if len(sys.argv) < 2:
        print("Usage: python play_first_audio.py <directory>")
        return 1
    
    directory = sys.argv[1]
    
    # Check if directory exists
    if not os.path.isdir(directory):
        print(f"Error: Directory not found: {directory}")
        return 1
    
    # List audio files
    audio_files = list_audio_files(directory)
    
    if not audio_files:
        print(f"No audio files found in: {directory}")
        return 1
    
    # Print available audio files
    print("Available audio files:")
    for i, file in enumerate(audio_files):
        print(f"{i+1}. {os.path.basename(file)}")
    
    # Play the first file
    print(f"\nPlaying the first audio file: {os.path.basename(audio_files[0])}")
    play_audio_file(audio_files[0])
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
