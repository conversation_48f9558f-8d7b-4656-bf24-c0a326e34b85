{"name": "interview-assistant", "version": "1.0.0", "description": "Desktop application for technical interview assistance", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder"}, "author": "", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-screenshot-app": "^4.0.3", "node-tesseract-ocr": "^2.2.1", "python-shell": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "build": {"appId": "com.interview.assistant", "productName": "Interview Assistant", "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}}}