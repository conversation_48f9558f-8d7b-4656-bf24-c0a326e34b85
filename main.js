const { app, BrowserWindow, globalShortcut, ipcMain, screen } = require('electron');
const path = require('path');
const { PythonShell } = require('python-shell');
const fs = require('fs');
const screenshot = require('electron-screenshot-app');

// Keep references to prevent garbage collection
let mainWindow;
let overlayWindow;
let isOverlayVisible = false;

// Create main application window
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    },
    title: 'Interview Assistant',
    icon: path.join(__dirname, 'assets/icon.png')
  });

  mainWindow.loadFile('src/index.html');
  
  // Open DevTools in development
  // mainWindow.webContents.openDevTools();

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Create transparent overlay window
function createOverlayWindow() {
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;
  
  overlayWindow = new BrowserWindow({
    width: 400,
    height: 300,
    x: width - 450,
    y: height - 350,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  overlayWindow.loadFile('src/overlay.html');
  overlayWindow.setIgnoreMouseEvents(true);
  overlayWindow.setOpacity(0); // Initially hidden
  
  overlayWindow.on('closed', () => {
    overlayWindow = null;
  });
}

// Initialize app
app.whenReady().then(() => {
  createMainWindow();
  createOverlayWindow();
  
  // Register global shortcuts
  registerShortcuts();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
      createOverlayWindow();
    }
  });
});

// Register global keyboard shortcuts
function registerShortcuts() {
  // ⌘+H / Ctrl+H: Capture screenshot and generate solution
  globalShortcut.register('CommandOrControl+H', captureAndProcess);
  
  // ⌘+B / Ctrl+B: Toggle solution overlay visibility
  globalShortcut.register('CommandOrControl+B', toggleOverlay);
  
  // Arrow keys for positioning overlay (when visible)
  globalShortcut.register('CommandOrControl+Up', () => moveOverlay('up'));
  globalShortcut.register('CommandOrControl+Down', () => moveOverlay('down'));
  globalShortcut.register('CommandOrControl+Left', () => moveOverlay('left'));
  globalShortcut.register('CommandOrControl+Right', () => moveOverlay('right'));
}

// Capture screenshot and process it
async function captureAndProcess() {
  try {
    // Capture screenshot
    const screenshotPath = path.join(app.getPath('temp'), 'interview-screenshot.png');
    
    screenshot.saveScreenshot('fullscreen', screenshotPath, (error, complete) => {
      if (error) {
        console.error('Screenshot error:', error);
        return;
      }
      
      if (complete) {
        // Process the screenshot with OCR
        processScreenshot(screenshotPath);
      }
    });
  } catch (error) {
    console.error('Error capturing screenshot:', error);
  }
}

// Process screenshot with OCR and AI
function processScreenshot(imagePath) {
  // Create python directory if it doesn't exist
  const pythonDir = path.join(__dirname, 'python');
  if (!fs.existsSync(pythonDir)) {
    fs.mkdirSync(pythonDir);
  }
  
  // Run Python OCR script
  const options = {
    mode: 'text',
    pythonPath: 'python', // Adjust based on your Python installation
    pythonOptions: ['-u'], // unbuffered output
    scriptPath: pythonDir,
    args: [imagePath]
  };
  
  PythonShell.run('ocr_engine.py', options, (err, results) => {
    if (err) {
      console.error('OCR processing error:', err);
      return;
    }
    
    if (results && results.length > 0) {
      const extractedText = results[0];
      
      // Generate solution with AI
      generateSolution(extractedText);
    }
  });
}

// Generate solution using AI
function generateSolution(problemText) {
  const pythonDir = path.join(__dirname, 'python');
  
  const options = {
    mode: 'json',
    pythonPath: 'python',
    pythonOptions: ['-u'],
    scriptPath: pythonDir,
    args: [problemText]
  };
  
  PythonShell.run('solution_generator.py', options, (err, results) => {
    if (err) {
      console.error('Solution generation error:', err);
      return;
    }
    
    if (results && results.length > 0) {
      const solution = results[0];
      
      // Send solution to overlay window
      overlayWindow.webContents.send('solution-ready', solution);
      
      // Make overlay visible
      if (!isOverlayVisible) {
        toggleOverlay();
      }
    }
  });
}

// Toggle overlay visibility
function toggleOverlay() {
  isOverlayVisible = !isOverlayVisible;
  
  if (isOverlayVisible) {
    overlayWindow.setOpacity(0.9);
    overlayWindow.setIgnoreMouseEvents(false, { forward: true });
  } else {
    overlayWindow.setOpacity(0);
    overlayWindow.setIgnoreMouseEvents(true);
  }
}

// Move overlay window
function moveOverlay(direction) {
  if (!isOverlayVisible) return;
  
  const [x, y] = overlayWindow.getPosition();
  const step = 10;
  
  switch (direction) {
    case 'up':
      overlayWindow.setPosition(x, y - step);
      break;
    case 'down':
      overlayWindow.setPosition(x, y + step);
      break;
    case 'left':
      overlayWindow.setPosition(x - step, y);
      break;
    case 'right':
      overlayWindow.setPosition(x + step, y);
      break;
  }
}

// IPC communication handlers
ipcMain.on('toggle-overlay', toggleOverlay);

// Quit app when all windows are closed (except on macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Unregister shortcuts when app is about to quit
app.on('will-quit', () => {
  globalShortcut.unregisterAll();
});
