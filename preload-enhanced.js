const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld(
  'api', {
    // Send messages to main process
    send: (channel, data) => {
      // Whitelist channels
      const validChannels = [
        'toggle-overlay', 
        'move-overlay', 
        'set-overlay-opacity',
        'switch-groq-model'
      ];
      if (validChannels.includes(channel)) {
        ipcRenderer.send(channel, data);
      }
    },
    
    // Receive messages from main process
    receive: (channel, func) => {
      const validChannels = [
        'solution-ready', 
        'quick-solution-ready', 
        'status-update',
        'overlay-visibility-changed'
      ];
      if (validChannels.includes(channel)) {
        // Deliberately strip event as it includes `sender` 
        ipcRenderer.on(channel, (event, ...args) => func(...args));
      }
    }
  }
);
