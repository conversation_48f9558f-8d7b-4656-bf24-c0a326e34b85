#ifndef SC_OPTIONS_H
#define SC_OPTIONS_H

#include "common.h"

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#include "util/tick.h"

enum sc_record_format {
    SC_RECORD_FORMAT_AUTO,
    SC_RECORD_FORMAT_MP4,
    SC_RECORD_FORMAT_MKV,
};

enum sc_log_level {
    SC_LOG_LEVEL_VERBOSE,
    SC_LOG_LEVEL_DEBUG,
    SC_LOG_LEVEL_INFO,
    SC_LOG_LEVEL_WARN,
    SC_LOG_LEVEL_ERROR,
};

enum sc_codec {
    SC_CODEC_H264,
    SC_CODEC_H265,
    SC_CODEC_AV1,
};

enum sc_audio_codec {
    SC_AUDIO_CODEC_RAW,
    SC_AUDIO_CODEC_OPUS,
    SC_AUDIO_CODEC_AAC,
};

enum sc_video_source {
    SC_VIDEO_SOURCE_DISPLAY,
    SC_VIDEO_SOURCE_CAMERA,
};

enum sc_audio_source {
    SC_AUDIO_SOURCE_OUTPUT,
    SC_AUDIO_SOURCE_MIC,
};

enum sc_camera_facing {
    SC_CAMERA_FACING_FRONT,
    SC_CAMERA_FACING_BACK,
    SC_CAMERA_FACING_EXTERNAL,
};

enum sc_keyboard_input_mode {
    SC_KEYBOARD_INPUT_MODE_DISABLED,
    SC_KEYBOARD_INPUT_MODE_SDK,
    SC_KEYBOARD_INPUT_MODE_UHID,
    SC_KEYBOARD_INPUT_MODE_AOA,
};

enum sc_mouse_input_mode {
    SC_MOUSE_INPUT_MODE_DISABLED,
    SC_MOUSE_INPUT_MODE_SDK,
    SC_MOUSE_INPUT_MODE_UHID,
    SC_MOUSE_INPUT_MODE_AOA,
};

enum sc_gamepad_input_mode {
    SC_GAMEPAD_INPUT_MODE_DISABLED,
    SC_GAMEPAD_INPUT_MODE_SDK,
    SC_GAMEPAD_INPUT_MODE_UHID,
    SC_GAMEPAD_INPUT_MODE_AOA,
};

enum sc_connection_mode {
    SC_CONNECTION_MODE_ADB,
    SC_CONNECTION_MODE_USB_DISPLAY,  // New connection mode for Samsung devices
};

struct sc_port_range {
    uint16_t first;
    uint16_t last;
};

struct sc_options {
    const char *serial;
    enum sc_log_level log_level;
    enum sc_codec codec;
    enum sc_audio_codec audio_codec;
    enum sc_video_source video_source;
    enum sc_audio_source audio_source;
    enum sc_camera_facing camera_facing;
    enum sc_connection_mode connection_mode;  // Added connection mode
    const char *camera_id;
    const char *camera_size;
    const char *crop;
    const char *record_filename;
    enum sc_record_format record_format;
    const char *window_title;
    const char *push_target;
    const char *render_driver;
    const char *video_codec_options;
    const char *audio_codec_options;
    const char *video_encoder;
    const char *audio_encoder;
    const char *camera_high_speed;
    const char *v4l2_device;
    const char *v4l2_buffer;
    const char *start_app;
    const char *new_display;
    const char *display_id;
    const char *tcpip;
    const char *display_ime_policy;
    uint16_t port;
    uint16_t tunnel_port;
    uint16_t max_size;
    uint32_t bit_rate;
    uint32_t audio_bit_rate;
    uint32_t max_fps;
    uint32_t camera_fps;
    uint32_t camera_ar_fps;
    uint32_t audio_buffer_ms;
    uint32_t lock_video_orientation;
    uint32_t display_buffer;
    uint8_t display_id_int;
    int8_t rotation;
    int8_t window_x;
    int8_t window_y;
    uint16_t window_width;
    uint16_t window_height;
    uint16_t window_borderless;
    uint16_t v4l2_buffer_count;
    enum sc_keyboard_input_mode keyboard_input_mode;
    enum sc_mouse_input_mode mouse_input_mode;
    enum sc_gamepad_input_mode gamepad_input_mode;
    struct sc_port_range port_range;
    sc_tick display_buffer_expiration;
    bool show_touches;
    bool fullscreen;
    bool always_on_top;
    bool control;
    bool video;
    bool audio;
    bool display;
    bool turn_screen_off;
    bool prefer_text;
    bool window_frame;
    bool audio_playback;
    bool force_adb_forward;
    bool disable_screensaver;
    bool forward_key_repeat;
    bool forward_all_clicks;
    bool legacy_paste;
    bool power_off_on_close;
    bool clipboard_autosync;
    bool downsize_on_error;
    bool tcpip_discovery;
    bool select_usb;
    bool select_tcpip;
    bool cleanup;
    bool power_on;
    bool list_encoders;
    bool list_displays;
    bool list_cameras;
    bool list_camera_sizes;
    bool list_apps;
    bool help;
    bool version;
    bool no_mipmaps;
    bool no_key_repeat;
    bool no_clipboard_autosync;
    bool otg;
    bool stay_awake;
    bool force_uhid_keyboard;
    bool v4l2_sink;
    bool send_device_meta;
    bool send_frame_meta;
    bool send_codec_meta;
    bool send_dummy_byte;
    bool raw_video_stream;
    bool audio_mirroring;
    bool no_audio_buffering;
    bool no_playback;
    bool kill_adb_on_close;
    bool camera_ar;
    bool camera_ar_auto;
    bool camera_ar_error_message;
    bool camera_high_speed_error_message;
    bool audio_dup;
};

#endif
