"""
Play FLAC files in Downloads folder
"""

import os
import sys
import platform
import subprocess

def list_flac_files(directory):
    """List all FLAC files in the directory"""
    flac_files = []
    
    for file in os.listdir(directory):
        if file.lower().endswith('.flac'):
            flac_files.append(os.path.join(directory, file))
    
    return flac_files

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def main():
    # Get Downloads folder
    downloads_dir = os.path.expanduser("~/Downloads")
    print(f"Searching for FLAC files in: {downloads_dir}")
    
    # List FLAC files
    flac_files = list_flac_files(downloads_dir)
    
    if not flac_files:
        print("No FLAC files found in Downloads folder.")
        return 1
    
    # Print available FLAC files
    print("Available FLAC files:")
    for i, file in enumerate(flac_files):
        print(f"{i+1}. {os.path.basename(file)}")
    
    # Ask user which file to play
    try:
        choice = int(input("\nEnter the number of the file to play (or 0 to exit): "))
        if choice == 0:
            return 0
        if choice < 1 or choice > len(flac_files):
            print("Invalid choice")
            return 1
        
        # Play the selected file
        play_audio_file(flac_files[choice-1])
        
    except ValueError:
        print("Invalid input")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
