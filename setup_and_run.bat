@echo off
echo AudioSR GUI Setup and Run
echo ========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.9-3.11 from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%V in ('python --version 2^>^&1') do set PYVER=%%V
echo Detected Python version: %PYVER%

REM Run setup script
echo.
echo Running setup script...
python setup_audiosr.py
if %errorlevel% neq 0 (
    echo Setup failed. Please check the error messages above.
    pause
    exit /b 1
)

REM Ask if user wants to run the GUI now
echo.
set /p RUN_NOW="Do you want to run the GUI now? (y/n): "
if /i "%RUN_NOW%"=="y" (
    echo.
    echo Starting AudioSR GUI...
    python audiosr_gui.py
)

echo.
echo Setup complete!
echo You can run the GUI anytime by running "python audiosr_gui.py"
echo Or use the executable in the dist/AudioSR directory.
echo.
pause
