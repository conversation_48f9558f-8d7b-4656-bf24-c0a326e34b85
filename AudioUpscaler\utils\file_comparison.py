"""
File Comparison Utility
Compares original and upscaled audio files
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, Tuple, Optional, List

def compare_audio_files(original_file: str, upscaled_file: str) -> Dict[str, Any]:
    """
    Compare original and upscaled audio files
    
    Args:
        original_file: Path to original audio file
        upscaled_file: Path to upscaled audio file
        
    Returns:
        Dictionary with comparison results
    """
    if not os.path.isfile(original_file):
        return {'error': f"Original file not found: {original_file}"}
    
    if not os.path.isfile(upscaled_file):
        return {'error': f"Upscaled file not found: {upscaled_file}"}
    
    try:
        import soundfile as sf
        
        # Load audio files
        original_data, original_rate = sf.read(original_file)
        upscaled_data, upscaled_rate = sf.read(upscaled_file)
        
        # Get basic info
        original_info = sf.info(original_file)
        upscaled_info = sf.info(upscaled_file)
        
        # Calculate statistics
        results = {
            'original': {
                'path': original_file,
                'name': os.path.basename(original_file),
                'sample_rate': original_rate,
                'channels': original_info.channels,
                'duration': original_info.duration,
                'size': os.path.getsize(original_file)
            },
            'upscaled': {
                'path': upscaled_file,
                'name': os.path.basename(upscaled_file),
                'sample_rate': upscaled_rate,
                'channels': upscaled_info.channels,
                'duration': upscaled_info.duration,
                'size': os.path.getsize(upscaled_file)
            },
            'comparison': {
                'sample_rate_ratio': upscaled_rate / original_rate,
                'duration_ratio': upscaled_info.duration / original_info.duration,
                'size_ratio': os.path.getsize(upscaled_file) / os.path.getsize(original_file)
            }
        }
        
        # Calculate spectral differences
        try:
            from scipy import signal
            
            # Convert to mono if needed for comparison
            if original_data.ndim > 1:
                original_mono = np.mean(original_data, axis=1)
            else:
                original_mono = original_data
                
            if upscaled_data.ndim > 1:
                upscaled_mono = np.mean(upscaled_data, axis=1)
            else:
                upscaled_mono = upscaled_data
            
            # Resample to match lengths if needed
            if len(original_mono) != len(upscaled_mono):
                # Resample the shorter one to match the longer one
                if len(original_mono) < len(upscaled_mono):
                    original_mono = signal.resample(original_mono, len(upscaled_mono))
                else:
                    upscaled_mono = signal.resample(upscaled_mono, len(original_mono))
            
            # Calculate frequency spectra
            original_spectrum = np.abs(np.fft.rfft(original_mono))
            upscaled_spectrum = np.abs(np.fft.rfft(upscaled_mono))
            
            # Normalize spectra
            original_spectrum = original_spectrum / np.max(original_spectrum)
            upscaled_spectrum = upscaled_spectrum / np.max(upscaled_spectrum)
            
            # Calculate spectral difference
            if len(original_spectrum) != len(upscaled_spectrum):
                # Resample to match
                if len(original_spectrum) < len(upscaled_spectrum):
                    original_spectrum = np.interp(
                        np.linspace(0, 1, len(upscaled_spectrum)),
                        np.linspace(0, 1, len(original_spectrum)),
                        original_spectrum
                    )
                else:
                    upscaled_spectrum = np.interp(
                        np.linspace(0, 1, len(original_spectrum)),
                        np.linspace(0, 1, len(upscaled_spectrum)),
                        upscaled_spectrum
                    )
            
            # Calculate spectral differences
            spectral_diff = np.mean(np.abs(upscaled_spectrum - original_spectrum))
            high_freq_ratio = np.mean(upscaled_spectrum[len(upscaled_spectrum)//2:]) / np.mean(original_spectrum[len(original_spectrum)//2:])
            
            results['comparison']['spectral_difference'] = float(spectral_diff)
            results['comparison']['high_frequency_ratio'] = float(high_freq_ratio)
            
            # Store spectra for plotting
            results['spectra'] = {
                'original': original_spectrum.tolist(),
                'upscaled': upscaled_spectrum.tolist(),
                'frequencies': np.fft.rfftfreq(len(original_mono), 1/original_rate).tolist()
            }
            
        except ImportError:
            results['comparison']['spectral_analysis'] = 'scipy not available'
        except Exception as e:
            results['comparison']['spectral_analysis_error'] = str(e)
        
        return results
        
    except ImportError:
        return {'error': "soundfile not available"}
    except Exception as e:
        return {'error': f"Error comparing files: {str(e)}"}

def generate_comparison_report(comparison_results: Dict[str, Any], output_dir: Optional[str] = None) -> Optional[str]:
    """
    Generate a comparison report with visualizations
    
    Args:
        comparison_results: Results from compare_audio_files
        output_dir: Directory to save the report (optional)
        
    Returns:
        Path to the report file or None if generation failed
    """
    if 'error' in comparison_results:
        print(f"Error: {comparison_results['error']}")
        return None
    
    try:
        # Create output directory if needed
        if output_dir is None:
            output_dir = os.path.dirname(comparison_results['upscaled']['path'])
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate report filename
        original_name = os.path.splitext(comparison_results['original']['name'])[0]
        report_path = os.path.join(output_dir, f"{original_name}_comparison_report.html")
        
        # Create HTML report
        with open(report_path, 'w') as f:
            f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>Audio Upscaling Comparison Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2 {{ color: #333; }}
        .container {{ max-width: 1000px; margin: 0 auto; }}
        .comparison-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .comparison-table th, .comparison-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .comparison-table th {{ background-color: #f2f2f2; }}
        .file-info {{ margin-bottom: 20px; }}
        .chart-container {{ width: 100%; height: 400px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Audio Upscaling Comparison Report</h1>
        
        <div class="file-info">
            <h2>Original File</h2>
            <p>Name: {comparison_results['original']['name']}</p>
            <p>Sample Rate: {comparison_results['original']['sample_rate']} Hz</p>
            <p>Channels: {comparison_results['original']['channels']}</p>
            <p>Duration: {comparison_results['original']['duration']:.2f} seconds</p>
            <p>File Size: {comparison_results['original']['size'] / 1024:.2f} KB</p>
        </div>
        
        <div class="file-info">
            <h2>Upscaled File</h2>
            <p>Name: {comparison_results['upscaled']['name']}</p>
            <p>Sample Rate: {comparison_results['upscaled']['sample_rate']} Hz</p>
            <p>Channels: {comparison_results['upscaled']['channels']}</p>
            <p>Duration: {comparison_results['upscaled']['duration']:.2f} seconds</p>
            <p>File Size: {comparison_results['upscaled']['size'] / 1024:.2f} KB</p>
        </div>
        
        <h2>Comparison Results</h2>
        <table class="comparison-table">
            <tr>
                <th>Metric</th>
                <th>Value</th>
                <th>Interpretation</th>
            </tr>
            <tr>
                <td>Sample Rate Ratio</td>
                <td>{comparison_results['comparison']['sample_rate_ratio']:.2f}</td>
                <td>{"Higher is better (indicates upsampling)" if comparison_results['comparison']['sample_rate_ratio'] > 1 else "No sample rate increase"}</td>
            </tr>
            <tr>
                <td>Duration Ratio</td>
                <td>{comparison_results['comparison']['duration_ratio']:.2f}</td>
                <td>{"Should be close to 1.0 (same duration)" if 0.95 <= comparison_results['comparison']['duration_ratio'] <= 1.05 else "Duration changed significantly"}</td>
            </tr>
            <tr>
                <td>File Size Ratio</td>
                <td>{comparison_results['comparison']['size_ratio']:.2f}</td>
                <td>{"Higher quality usually means larger file" if comparison_results['comparison']['size_ratio'] > 1 else "File size decreased"}</td>
            </tr>
""")
            
            # Add spectral analysis if available
            if 'spectral_difference' in comparison_results['comparison']:
                f.write(f"""
            <tr>
                <td>Spectral Difference</td>
                <td>{comparison_results['comparison']['spectral_difference']:.4f}</td>
                <td>{"Lower values indicate more similar frequency content" if comparison_results['comparison']['spectral_difference'] < 0.2 else "Significant frequency content changes"}</td>
            </tr>
            <tr>
                <td>High Frequency Ratio</td>
                <td>{comparison_results['comparison']['high_frequency_ratio']:.2f}</td>
                <td>{"Values > 1 indicate enhanced high frequencies" if comparison_results['comparison']['high_frequency_ratio'] > 1 else "High frequencies not enhanced"}</td>
            </tr>
""")
            
            f.write("""
        </table>
""")
            
            # Add spectral plot if available
            if 'spectra' in comparison_results:
                # Generate spectral plot
                try:
                    plt.figure(figsize=(10, 6))
                    plt.plot(comparison_results['spectra']['frequencies'], comparison_results['spectra']['original'], label='Original', alpha=0.7)
                    plt.plot(comparison_results['spectra']['frequencies'], comparison_results['spectra']['upscaled'], label='Upscaled', alpha=0.7)
                    plt.xlabel('Frequency (Hz)')
                    plt.ylabel('Magnitude')
                    plt.title('Frequency Spectrum Comparison')
                    plt.legend()
                    plt.grid(True, alpha=0.3)
                    
                    # Save plot
                    plot_path = os.path.join(output_dir, f"{original_name}_spectrum_comparison.png")
                    plt.savefig(plot_path)
                    plt.close()
                    
                    # Add to report
                    f.write(f"""
        <h2>Spectral Analysis</h2>
        <p>This chart shows the frequency spectrum of both files. Higher values in the upscaled file at higher frequencies indicate better detail.</p>
        <div class="chart-container">
            <img src="{os.path.basename(plot_path)}" alt="Spectrum Comparison" style="width: 100%;">
        </div>
""")
                except Exception as e:
                    f.write(f"""
        <h2>Spectral Analysis</h2>
        <p>Error generating spectral plot: {str(e)}</p>
""")
            
            # Finish HTML
            f.write("""
        <h2>Conclusion</h2>
""")
            
            # Generate conclusion based on metrics
            if (comparison_results['comparison']['sample_rate_ratio'] > 1.5 and 
                comparison_results['comparison'].get('high_frequency_ratio', 0) > 1.2):
                f.write("""
        <p>The upscaling process has <strong>significantly improved</strong> the audio quality by increasing the sample rate and enhancing high-frequency content. This should result in better clarity and detail.</p>
""")
            elif (comparison_results['comparison']['sample_rate_ratio'] > 1.0 and 
                  comparison_results['comparison'].get('high_frequency_ratio', 0) > 1.0):
                f.write("""
        <p>The upscaling process has <strong>moderately improved</strong> the audio quality. There is some enhancement in high-frequency content which may improve clarity.</p>
""")
            else:
                f.write("""
        <p>The upscaling process has made <strong>minimal changes</strong> to the audio quality. The differences between the original and upscaled files are subtle.</p>
""")
            
            f.write("""
    </div>
</body>
</html>
""")
        
        print(f"Comparison report generated: {report_path}")
        return report_path
        
    except Exception as e:
        print(f"Error generating comparison report: {e}")
        return None
