"""
Direct installation of AudioSR using pip
"""

import subprocess
import sys
import os

def main():
    print("Installing AudioSR directly...")
    
    # Install dependencies first
    dependencies = [
        "torch",
        "torchaudio",
        "torchvision",
        "numpy",
        "scipy",
        "librosa",
        "soundfile",
        "diffusers",
        "transformers",
        "accelerate"
    ]
    
    for dep in dependencies:
        print(f"Installing {dep}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        except subprocess.CalledProcessError as e:
            print(f"Error installing {dep}: {e}")
    
    # Install AudioSR from GitHub
    print("Installing AudioSR from GitHub...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "git+https://github.com/haoheliu/versatile_audio_super_resolution.git"
        ])
        print("AudioSR installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Error installing AudioSR: {e}")
        print("Please install it manually:")
        print("pip install git+https://github.com/haoheliu/versatile_audio_super_resolution.git")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
