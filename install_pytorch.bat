@echo off
echo Installing PyTorch and dependencies

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Install PyTorch
echo Installing PyTorch...
"%PYTHON_PATH%\python.exe" -m pip install torch
"%PYTHON_PATH%\python.exe" -m pip install torchvision
"%PYTHON_PATH%\python.exe" -m pip install torchaudio

REM Install other dependencies
echo Installing other dependencies...
"%PYTHON_PATH%\python.exe" -m pip install numpy
"%PYTHON_PATH%\python.exe" -m pip install scipy
"%PYTHON_PATH%\python.exe" -m pip install librosa
"%PYTHON_PATH%\python.exe" -m pip install soundfile
"%PYTHON_PATH%\python.exe" -m pip install diffusers
"%PYTHON_PATH%\python.exe" -m pip install transformers
"%PYTHON_PATH%\python.exe" -m pip install accelerate

echo.
echo Installation complete!
pause
