"""
Process and play audio file with audio upscaler
"""

import os
import sys
import time
import tempfile
import platform
import subprocess
import soundfile as sf
import numpy as np

# Try to import audio playback libraries
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("pygame not available, will use system default player")

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    if PYGAME_AVAILABLE:
        # Use pygame for playback
        pygame.mixer.init()
        pygame.mixer.music.load(file_path)
        pygame.mixer.music.play()
        
        # Wait for playback to finish
        while pygame.mixer.music.get_busy():
            pygame.time.Clock().tick(10)
    else:
        # Use system default player
        if platform.system() == "Windows":
            os.startfile(file_path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", file_path])
        else:  # Linux
            subprocess.run(["xdg-open", file_path])
        
        # Wait a bit to allow the player to start
        time.sleep(2)

def process_audio(input_file, use_windows11_optimizations=True, use_amd_acceleration=False, use_mojo=False):
    """Process audio file with audio upscaler"""
    print(f"Processing: {input_file}")
    
    # Create output directory
    output_dir = os.path.join(os.path.dirname(input_file), "audiosr_output")
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate output filename
    input_basename = os.path.basename(input_file)
    input_name, ext = os.path.splitext(input_basename)
    output_file = os.path.join(output_dir, f"{input_name}_SR.wav")
    
    # Load audio file
    print("Loading audio file...")
    audio_data, sample_rate = sf.read(input_file)
    
    # Apply Windows 11 optimizations if enabled
    if use_windows11_optimizations:
        try:
            from windows11_optimizations import Windows11Optimizer
            print("Applying Windows 11 optimizations...")
            optimizer = Windows11Optimizer()
            optimizations = optimizer.optimize_audio_processing()
            print("Optimization results:")
            for key, value in optimizations.items():
                print(f"  {key}: {value}")
        except ImportError:
            print("Windows11Optimizer not available, skipping optimizations")
    
    # Process with Mojo bridge if enabled
    if use_mojo:
        try:
            from mojo_bridge import MojoBridge
            print("Processing with Mojo acceleration...")
            mojo_bridge = MojoBridge()
            if mojo_bridge.mojo_available:
                # Process with Mojo bridge
                waveform, new_sample_rate = mojo_bridge.upscale_audio(
                    audio_data,
                    sample_rate,
                    48000  # Target sample rate
                )
                print(f"Audio processed with Mojo. New sample rate: {new_sample_rate}")
            else:
                print("Mojo not available, falling back to standard processing")
                # Simple upsampling as fallback
                waveform = audio_data
                new_sample_rate = sample_rate
        except ImportError:
            print("MojoBridge not available, falling back to standard processing")
            # Simple upsampling as fallback
            waveform = audio_data
            new_sample_rate = sample_rate
    
    # Process with AMD acceleration if enabled
    elif use_amd_acceleration:
        try:
            import torch
            from amd_rocm_accelerator import AMDAccelerator
            print("Processing with AMD acceleration...")
            accelerator = AMDAccelerator(device="cuda" if torch.cuda.is_available() else "cpu")
            if accelerator.is_rocm_available:
                # Convert to tensor for AMD processing
                audio_tensor = torch.tensor(audio_data)
                # Process with AMD accelerator
                processed_tensor = accelerator.process_audio(audio_tensor)
                waveform = processed_tensor.cpu().numpy()
                new_sample_rate = sample_rate
                print("Audio processed with AMD acceleration")
            else:
                print("AMD acceleration not available, falling back to standard processing")
                # Simple upsampling as fallback
                waveform = audio_data
                new_sample_rate = sample_rate
        except ImportError:
            print("AMDAccelerator not available, falling back to standard processing")
            # Simple upsampling as fallback
            waveform = audio_data
            new_sample_rate = sample_rate
    
    # Standard processing (simple upsampling for demo)
    else:
        print("Using standard processing...")
        try:
            from scipy import signal
            # Simple resampling to 48kHz
            if audio_data.ndim == 1:
                # Mono
                waveform = signal.resample_poly(audio_data, 48000, sample_rate)
            else:
                # Stereo or multi-channel
                waveform = np.zeros((int(audio_data.shape[0] * 48000 / sample_rate), audio_data.shape[1]))
                for i in range(audio_data.shape[1]):
                    waveform[:, i] = signal.resample_poly(audio_data[:, i], 48000, sample_rate)
            new_sample_rate = 48000
            print(f"Audio resampled from {sample_rate}Hz to {new_sample_rate}Hz")
        except ImportError:
            print("scipy not available, using original audio")
            waveform = audio_data
            new_sample_rate = sample_rate
    
    # Save output
    print(f"Saving output to: {output_file}")
    sf.write(output_file, waveform, new_sample_rate)
    
    return output_file

def main():
    # Check command line arguments
    if len(sys.argv) < 2:
        print("Usage: python process_and_play.py <input_file> [--play-original] [--play-processed] [--use-amd] [--use-mojo]")
        return 1
    
    input_file = sys.argv[1]
    play_original = "--play-original" in sys.argv
    play_processed = "--play-processed" in sys.argv
    use_amd = "--use-amd" in sys.argv
    use_mojo = "--use-mojo" in sys.argv
    
    # Default to playing processed if neither specified
    if not play_original and not play_processed:
        play_processed = True
    
    # Check if file exists
    if not os.path.isfile(input_file):
        print(f"Error: File not found: {input_file}")
        return 1
    
    # Play original if requested
    if play_original:
        print("\n=== Playing Original Audio ===")
        play_audio_file(input_file)
    
    # Process audio
    print("\n=== Processing Audio ===")
    output_file = process_audio(input_file, use_amd_acceleration=use_amd, use_mojo=use_mojo)
    
    # Play processed if requested
    if play_processed:
        print("\n=== Playing Processed Audio ===")
        play_audio_file(output_file)
    
    print("\nDone!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
