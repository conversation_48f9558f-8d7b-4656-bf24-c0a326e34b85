"""
Test script for Windows 11 optimizations
"""

import sys
import platform

print(f"Python version: {sys.version}")
print(f"Platform: {platform.platform()}")
print()

try:
    print("Importing Windows11Optimizer...")
    from windows11_optimizations import Windows11Optimizer
    
    print("Creating optimizer instance...")
    optimizer = Windows11Optimizer()
    
    print(f"Running on Windows 11: {optimizer.is_windows11}")
    print(f"OS: {optimizer.system_info['os']}")
    print(f"Version: {optimizer.system_info['version']}")
    
    print("\nApplying optimizations...")
    optimizations = optimizer.optimize_audio_processing()
    
    print("\nOptimization Results:")
    for key, value in optimizations.items():
        print(f"  {key}: {value}")
    
    print("\nTest completed successfully!")
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
