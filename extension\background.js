// Background script for NeuralCodeAssist extension

// Native messaging connection to local service
let nativePort = null;

// Connect to native messaging host
function connectToNativeHost() {
  try {
    nativePort = chrome.runtime.connectNative('com.neuralcodeassist.service');
    
    nativePort.onMessage.addListener((message) => {
      console.log('Received message from native host:', message);
      
      // Forward messages to content script
      if (message.type === 'solution' || message.type === 'quick-solution') {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, message);
          }
        });
      }
    });
    
    nativePort.onDisconnect.addListener(() => {
      console.log('Native port disconnected');
      nativePort = null;
      
      // Try to reconnect after a delay
      setTimeout(connectToNativeHost, 5000);
    });
    
    console.log('Connected to native host');
  } catch (error) {
    console.error('Failed to connect to native host:', error);
    
    // Try to reconnect after a delay
    setTimeout(connectToNativeHost, 5000);
  }
}

// Initialize connection
connectToNativeHost();

// Listen for commands from keyboard shortcuts
chrome.commands.onCommand.addListener((command) => {
  if (command === 'toggle-overlay') {
    sendToNativeHost({ type: 'toggle-overlay' });
  } else if (command === 'capture-problem') {
    chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
      sendToNativeHost({ 
        type: 'capture-problem',
        screenshot: dataUrl
      });
    });
  }
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'capture-problem-element') {
    sendToNativeHost({
      type: 'capture-problem-text',
      text: message.text,
      url: sender.tab.url
    });
    sendResponse({ status: 'processing' });
  } else if (message.type === 'toggle-overlay') {
    sendToNativeHost({ type: 'toggle-overlay' });
    sendResponse({ status: 'toggling' });
  } else if (message.type === 'move-overlay') {
    sendToNativeHost({ 
      type: 'move-overlay',
      direction: message.direction
    });
    sendResponse({ status: 'moving' });
  } else if (message.type === 'switch-model') {
    sendToNativeHost({ 
      type: 'switch-model',
      model: message.model
    });
    sendResponse({ status: 'switching' });
  }
  
  return true; // Keep the message channel open for async responses
});

// Send message to native host
function sendToNativeHost(message) {
  if (nativePort) {
    try {
      nativePort.postMessage(message);
    } catch (error) {
      console.error('Error sending message to native host:', error);
      
      // Try to reconnect
      connectToNativeHost();
    }
  } else {
    console.warn('Native port not connected, trying to reconnect...');
    connectToNativeHost();
  }
}
