# Windows 11 Optimization Projects

This repository contains four main projects focused on Windows 11 optimizations and utilities.

## Project Structure

The repository is organized into the following main directories:

### 1. Audio Processing Suite (`audio_processing/`)

A comprehensive audio processing suite with Windows 11 optimizations for reduced resource usage and latency. This project integrates both audio upscaling and audio processing components into a unified system.

- Real-time audio upscaling with reduced latency
- Windows 11-specific optimizations for better performance
- Direct interaction with audio drivers
- Hardware acceleration for AMD/NVIDIA GPUs
- Batch processing capabilities
- Audio Super Resolution (AudioSR) integration
- Mojo language integration for high-performance processing

#### High-Quality Audio Upscaling System

The Audio Processing Suite includes a high-quality audio upscaling system that prioritizes quality over processing time. This system enhances audio quality through a three-stage process: preprocessing, upscaling, and postprocessing.

**Features:**

- **High-Quality Audio Upscaling**: Increase sample rate and bit depth while preserving and enhancing audio quality
- **Dynamic Range Enhancement**: Improve dynamic range through multiband processing
- **Harmonic Enhancement**: Enhance harmonics for richer sound
- **Transient Preservation**: Detect and preserve transients for better clarity
- **Stereo Enhancement**: Improve stereo image for more immersive sound
- **Noise Reduction**: Reduce noise while preserving audio quality
- **Loudness Normalization**: Normalize loudness to target LUFS level
- **Batch Processing**: Process multiple files at once
- **GPU Acceleration**: Utilize GPU for faster processing
- **Windows 11 Optimizations**: Optimize performance on Windows 11

**Components:**

1. **Preprocessor**: Enhances audio quality before upscaling (DC offset correction, noise reduction, phase correction, transient detection)
2. **Upscaler**: Increases sample rate and bit depth while preserving and enhancing audio quality
3. **Postprocessor**: Enhances audio quality after upscaling (harmonic enhancement, stereo enhancement, limiting, loudness normalization, dithering)

### 2. Interview Assistant Application (`interview_assistant/`)

An Electron-based desktop application for assisting during technical coding interviews.

- Screenshot-to-solution generation using OCR
- Multiple AI models (local, Groq, BitNet)
- Invisible overlay hidden from screen sharing
- Keyboard shortcuts for control
- Real-time debugging assistance

### 3. Calculator Application (`calculator/`)

A sci-fi themed calculator with advanced mathematical functions and visual effects.

- Sci-fi themed interface with particle animations
- Advanced mathematical functions
- Memory functions
- Comprehensive error handling
- Visual feedback with animations

### 4. Samsung Screen Mirroring (`samsung_mirroring/`)

A comprehensive tool that enables screen mirroring for Samsung devices (particularly the A22) by integrating with scrcpy and providing multiple connection methods.

- Mimics HDMI-to-OTG connections for Samsung devices
- Integrates with scrcpy for high-quality screen mirroring
- GUI for interacting with the Samsung device
- Exploits Samsung TTS vulnerability to enable USB debugging
- Works without requiring user interaction on the device
- Multiple connection methods (USB, wireless, exploit)

### Common Utilities (`common/`)

Shared utilities and resources used across multiple projects.

## Getting Started

Each project has its own README file with detailed instructions on setup and usage. See the respective project directories for more information.

## Requirements

- Windows 11
- Python 3.12
- Node.js (for Interview Assistant)
- ADB (for Samsung Screen Mirroring)
- Additional project-specific requirements are listed in each project's README

## Contributing

Please read the development guidelines in `common/docs/` before contributing to this project.
