# Audio Upscaler with Windows 11 Optimizations

A comprehensive audio upscaling system with Windows 11 optimizations for enhanced audio quality.

## Features

- **Real-time Audio Upscaling**: Process system audio on-the-fly
- **Batch Processing**: Upscale multiple audio files in a queue
- **Windows 11 Optimizations**: Enhanced performance on Windows 11
- **Hardware Acceleration**: Utilize GPU for faster processing
- **File Comparison**: Verify upscaling quality with detailed analysis
- **Multiple Quality Levels**: Choose between performance and quality

## Requirements

- Windows 11 (for optimizations, works on other Windows versions with reduced features)
- Python 3.8 or higher
- Required Python packages (automatically installed):
  - numpy
  - scipy
  - soundfile
  - matplotlib (for comparison tool)

## Quick Start

1. Run `run_upscaler.bat` to start the main application
2. Choose between real-time upscaling or batch processing
3. Adjust settings as needed
4. Process your audio files

## Usage

### Real-time Audio Upscaling

1. In the main application, go to the "Real-time Upscaling" tab
2. Select your desired upscale factor and quality level
3. Click "Start Real-time Upscaling"
4. All system audio will be processed in real-time
5. Click "Stop Real-time Upscaling" when done

### Batch Processing

1. In the main application, go to the "Batch Processing" tab
2. Add files using "Add Files" or "Add Folder"
3. Select your desired upscale factor and quality level
4. Choose an output directory (optional)
5. Click "Process Files"
6. Monitor progress and wait for completion

### File Comparison

1. Run `compare_files.bat` to start the comparison tool
2. Select an original audio file
3. Select an upscaled audio file
4. Click "Compare Files"
5. View the detailed analysis and report

### Cleanup

If you encounter any issues or need to stop all background processes:

1. Run `cleanup.bat` to terminate all Audio Upscaler processes
2. Confirm the cleanup was successful

## Settings

### Upscale Factor

- **2x**: Double the sample rate (good balance of quality and performance)
- **4x**: Quadruple the sample rate (better quality, more resource-intensive)
- **8x**: 8x the sample rate (highest quality, very resource-intensive)

### Quality Level

- **Low**: Faster processing, lower quality
- **Medium**: Balanced processing and quality
- **High**: Best quality, slower processing

### Windows 11 Optimizations

- Process priority optimization
- Thread priority optimization
- Power mode optimization
- Exclusive audio mode
- Hardware acceleration

## Troubleshooting

- If real-time processing causes audio glitches, try a lower quality setting
- If batch processing is slow, reduce the quality level or disable some optimizations
- If the application crashes, run `cleanup.bat` to ensure all processes are terminated
- For comparison issues, ensure both files are valid audio files

## Technical Details

The Audio Upscaler uses advanced signal processing techniques to enhance audio quality:

1. **Resampling**: Increases the sample rate for better frequency response
2. **Spectral Enhancement**: Boosts specific frequency ranges for improved clarity
3. **Windows 11 Optimizations**: Utilizes Windows 11-specific features for better performance
4. **Hardware Acceleration**: Leverages GPU capabilities when available

## License

This software is provided as-is with no warranty. Use at your own risk.
