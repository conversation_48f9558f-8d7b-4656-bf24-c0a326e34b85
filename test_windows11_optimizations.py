"""
Test script for Windows 11 optimizations
"""

from windows11_optimizations import Windows11Optimizer

def test_windows11_optimizer():
    """Test the Windows11Optimizer class"""
    print("Testing Windows11Optimizer...")
    
    # Create optimizer instance
    optimizer = Windows11Optimizer()
    
    # Print system information
    print("\nSystem Information:")
    for key, value in optimizer.system_info.items():
        print(f"  {key}: {value}")
    
    # Test optimizations
    print("\nApplying optimizations...")
    optimizations = optimizer.optimize_audio_processing()
    
    print("\nOptimization Results:")
    for key, value in optimizations.items():
        print(f"  {key}: {value}")
    
    # Test exclusive audio
    print("\nSetting up exclusive audio...")
    exclusive_audio = optimizer.setup_exclusive_audio()
    print(f"  Exclusive audio setup: {'Success' if exclusive_audio else 'Failed'}")
    
    # Test hardware acceleration
    print("\nEnabling hardware acceleration...")
    hw_accel = optimizer.enable_hardware_acceleration()
    print(f"  Hardware acceleration: {'Enabled' if hw_accel else 'Disabled'}")
    
    print("\nTest completed.")

if __name__ == "__main__":
    test_windows11_optimizer()
