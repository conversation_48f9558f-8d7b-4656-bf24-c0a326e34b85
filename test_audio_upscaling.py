"""
Test script for the audio upscaling system
"""

import os
import time
import logging
import numpy as np
import soundfile as sf
import argparse
from typing import Dict, Any, Optional, List, Union, Tuple

# Import our modules
from audio_preprocessor import AudioPreprocessor
from audio_upscaler import AudioUpscaler
from audio_postprocessor import AudioPostprocessor
from audio_upscaling_system import AudioUpscalingSystem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("test_audio_upscaling.log")
    ]
)
logger = logging.getLogger(__name__)

def generate_test_signal(duration: float = 2.0, sample_rate: int = 44100) -> Tuple[np.ndarray, int]:
    """
    Generate a test signal with multiple frequencies
    
    Args:
        duration: Duration in seconds
        sample_rate: Sample rate in Hz
        
    Returns:
        Tuple of (audio_data, sample_rate)
    """
    # Create time vector
    t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
    
    # Create a test signal with multiple frequencies
    signal = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz (A4)
    signal += 0.3 * np.sin(2 * np.pi * 880 * t)  # 880 Hz (A5)
    signal += 0.2 * np.sin(2 * np.pi * 1760 * t)  # 1760 Hz (A6)
    
    # Add some noise
    np.random.seed(42)  # For reproducibility
    signal += 0.05 * np.random.randn(len(t))
    
    # Add DC offset
    signal += 0.1
    
    # Create stereo signal
    stereo_signal = np.column_stack((signal, signal * 0.8))
    
    return stereo_signal, sample_rate

def test_preprocessor():
    """Test the audio preprocessor"""
    logger.info("Testing audio preprocessor...")
    
    # Generate test signal
    audio_data, sample_rate = generate_test_signal()
    
    # Save test signal
    test_file = "test_signal.wav"
    sf.write(test_file, audio_data, sample_rate)
    
    # Create preprocessor
    preprocessor = AudioPreprocessor(device="auto", quality_level=3)
    
    # Process test signal
    start_time = time.time()
    output_file = preprocessor.preprocess_file(test_file)
    process_time = time.time() - start_time
    
    logger.info(f"Preprocessed file saved to: {output_file}")
    logger.info(f"Processing time: {process_time:.2f} seconds")
    
    # Load processed file
    processed_signal, _ = sf.read(output_file)
    
    # Analyze original and processed signals
    logger.info("Original signal:")
    logger.info(f"  Mean (DC offset): {np.mean(audio_data):.6f}")
    logger.info(f"  Standard deviation: {np.std(audio_data):.6f}")
    logger.info(f"  Peak amplitude: {np.max(np.abs(audio_data)):.6f}")
    
    logger.info("Processed signal:")
    logger.info(f"  Mean (DC offset): {np.mean(processed_signal):.6f}")
    logger.info(f"  Standard deviation: {np.std(processed_signal):.6f}")
    logger.info(f"  Peak amplitude: {np.max(np.abs(processed_signal)):.6f}")
    
    # Check if DC offset was corrected
    assert abs(np.mean(processed_signal)) < 0.01, "DC offset was not corrected"
    
    logger.info("Audio preprocessor test passed")
    
    return output_file

def test_upscaler():
    """Test the audio upscaler"""
    logger.info("Testing audio upscaler...")
    
    # Generate test signal
    audio_data, sample_rate = generate_test_signal()
    
    # Save test signal
    test_file = "test_signal.wav"
    sf.write(test_file, audio_data, sample_rate)
    
    # Create upscaler
    upscaler = AudioUpscaler(quality_level=3, use_gpu=True)
    
    # Process test signal
    start_time = time.time()
    output_file = upscaler.upscale_file(test_file, target_sample_rate=96000, target_bit_depth=24)
    process_time = time.time() - start_time
    
    logger.info(f"Upscaled file saved to: {output_file}")
    logger.info(f"Processing time: {process_time:.2f} seconds")
    
    # Load processed file
    processed_signal, processed_sample_rate = sf.read(output_file)
    
    # Check if sample rate was increased
    assert processed_sample_rate > sample_rate, "Sample rate was not increased"
    
    logger.info("Audio upscaler test passed")
    
    return output_file

def test_postprocessor():
    """Test the audio postprocessor"""
    logger.info("Testing audio postprocessor...")
    
    # Generate test signal
    audio_data, sample_rate = generate_test_signal()
    
    # Save test signal
    test_file = "test_signal.wav"
    sf.write(test_file, audio_data, sample_rate)
    
    # Create postprocessor
    postprocessor = AudioPostprocessor(device="auto", quality_level=3)
    
    # Process test signal
    start_time = time.time()
    output_file = postprocessor.postprocess_file(test_file, target_lufs=-16)
    process_time = time.time() - start_time
    
    logger.info(f"Postprocessed file saved to: {output_file}")
    logger.info(f"Processing time: {process_time:.2f} seconds")
    
    # Load processed file
    processed_signal, _ = sf.read(output_file)
    
    # Check if limiting was applied
    assert np.max(np.abs(processed_signal)) <= 1.0, "Limiting was not applied"
    
    logger.info("Audio postprocessor test passed")
    
    return output_file

def test_complete_system():
    """Test the complete audio upscaling system"""
    logger.info("Testing complete audio upscaling system...")
    
    # Generate test signal
    audio_data, sample_rate = generate_test_signal()
    
    # Save test signal
    test_file = "test_signal.wav"
    sf.write(test_file, audio_data, sample_rate)
    
    # Create upscaling system
    system = AudioUpscalingSystem(quality_level=3, use_gpu=True)
    
    # Process test signal
    start_time = time.time()
    output_file = system.process_file(
        test_file,
        target_sample_rate=96000,
        target_bit_depth=24,
        target_lufs=-16
    )
    process_time = time.time() - start_time
    
    logger.info(f"Processed file saved to: {output_file}")
    logger.info(f"Processing time: {process_time:.2f} seconds")
    
    # Load processed file
    processed_signal, processed_sample_rate = sf.read(output_file)
    
    # Check if sample rate was increased
    assert processed_sample_rate > sample_rate, "Sample rate was not increased"
    
    # Check if limiting was applied
    assert np.max(np.abs(processed_signal)) <= 1.0, "Limiting was not applied"
    
    # Check if DC offset was corrected
    assert abs(np.mean(processed_signal)) < 0.01, "DC offset was not corrected"
    
    logger.info("Complete audio upscaling system test passed")
    
    return output_file

def test_batch_processing():
    """Test batch processing"""
    logger.info("Testing batch processing...")
    
    # Create test directory
    test_dir = "test_batch"
    os.makedirs(test_dir, exist_ok=True)
    
    # Generate multiple test signals
    for i in range(3):
        audio_data, sample_rate = generate_test_signal(duration=1.0 + i * 0.5)
        test_file = os.path.join(test_dir, f"test_signal_{i}.wav")
        sf.write(test_file, audio_data, sample_rate)
    
    # Create upscaling system
    system = AudioUpscalingSystem(quality_level=2, use_gpu=True)
    
    # Process test directory
    start_time = time.time()
    output_files = system.batch_process(
        test_dir,
        target_sample_rate=48000,
        target_bit_depth=24,
        target_lufs=-16
    )
    process_time = time.time() - start_time
    
    logger.info(f"Processed {len(output_files)} files")
    logger.info(f"Processing time: {process_time:.2f} seconds")
    
    # Check if all files were processed
    assert len(output_files) == 3, "Not all files were processed"
    
    logger.info("Batch processing test passed")
    
    return output_files

def test_analysis():
    """Test audio analysis"""
    logger.info("Testing audio analysis...")
    
    # Generate test signal
    audio_data, sample_rate = generate_test_signal()
    
    # Save test signal
    test_file = "test_signal.wav"
    sf.write(test_file, audio_data, sample_rate)
    
    # Create upscaling system
    system = AudioUpscalingSystem(quality_level=3, use_gpu=True)
    
    # Analyze test signal
    analysis = system.analyze_file(test_file)
    
    # Check if analysis contains expected keys
    expected_keys = [
        'file_path', 'duration', 'channels', 'sample_rate',
        'peak_level_db', 'rms_level_db', 'dynamic_range_db',
        'crest_factor_db', 'loudness_range_db'
    ]
    
    for key in expected_keys:
        assert key in analysis, f"Analysis missing key: {key}"
    
    logger.info("Audio analysis test passed")
    
    return analysis

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Test audio upscaling system')
    parser.add_argument('--test', choices=['preprocessor', 'upscaler', 'postprocessor', 'system', 'batch', 'analysis', 'all'], default='all', help='Test to run')
    args = parser.parse_args()
    
    try:
        if args.test == 'preprocessor' or args.test == 'all':
            test_preprocessor()
        
        if args.test == 'upscaler' or args.test == 'all':
            test_upscaler()
        
        if args.test == 'postprocessor' or args.test == 'all':
            test_postprocessor()
        
        if args.test == 'system' or args.test == 'all':
            test_complete_system()
        
        if args.test == 'batch' or args.test == 'all':
            test_batch_processing()
        
        if args.test == 'analysis' or args.test == 'all':
            test_analysis()
        
        logger.info("All tests passed!")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    main()
