"""
Very simple test for Windows 11 optimizations
"""

import os
import sys
import platform

def main():
    print("Simple Windows 11 Optimization Test")
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.platform()}")
    
    # Check if Windows 11
    is_windows11 = False
    if platform.system() == "Windows":
        try:
            version = platform.version().split('.')
            if len(version) >= 3 and int(version[2]) >= 22000:
                is_windows11 = True
                print("Windows 11 detected!")
            else:
                print(f"Not Windows 11. Version: {platform.version()}")
        except Exception as e:
            print(f"Error checking Windows version: {e}")
    else:
        print(f"Not Windows. System: {platform.system()}")
    
    # Try to import Windows 11 optimizer
    try:
        print("\nImporting Windows11Optimizer...")
        from windows11_optimizations import Windows11Optimizer
        print("Import successful!")
        
        # Create optimizer instance
        print("\nCreating Windows11Optimizer instance...")
        optimizer = Windows11Optimizer()
        print("Instance created successfully!")
        
        # Print system info
        print("\nSystem Information:")
        for key, value in optimizer.system_info.items():
            print(f"  {key}: {value}")
        
        # Apply optimizations
        print("\nApplying optimizations...")
        optimizations = optimizer.optimize_audio_processing()
        
        print("\nOptimization Results:")
        for key, value in optimizations.items():
            print(f"  {key}: {value}")
        
        print("\nTest completed successfully!")
        return 0
        
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
