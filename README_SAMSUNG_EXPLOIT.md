# Samsung A22 Screen Mirroring Exploit

This project provides a method to enable screen mirroring on Samsung A22 (and other Samsung devices) without requiring USB debugging to be enabled. It uses a vulnerability in Samsung's Text-to-Speech (TTS) engine to gain system-level access and programmatically enable USB debugging.

## How It Works

This exploit uses the following techniques:

1. **Samsung TTS Vulnerability (CVE-2019-16253)**:
   - Exploits a vulnerability in Samsung's Text-to-Speech engine
   - Downgrades the TTS app to a vulnerable version
   - Loads a malicious library that runs with system privileges (UID 1000)

2. **System Shell Access**:
   - The exploit gains system shell access
   - Uses this access to enable USB debugging and ADB over network
   - No user interaction or settings changes required

3. **scrcpy Integration**:
   - Once USB debugging is enabled, connects to the device using scrcpy
   - Provides full screen mirroring and control

## Requirements

- A Samsung A22 or other Samsung device with the latest OS update
- Windows PC or Linux/macOS
- USB cable to connect the device
- ADB (Android Debug Bridge) installed on your computer
- scrcpy installed on your computer

## Usage

### Windows

1. Connect your Samsung device to your computer via USB
2. Make sure USB file transfer is enabled on your device
3. Run the `samsung_exploit.bat` script:
   ```
   samsung_exploit.bat
   ```

### Linux/macOS

1. Connect your Samsung device to your computer via USB
2. Make sure USB file transfer is enabled on your device
3. Make the script executable:
   ```
   chmod +x samsung_exploit.sh
   ```
4. Run the script:
   ```
   ./samsung_exploit.sh
   ```

## Building the Helper App

If you want to build the helper app yourself:

1. Clone this repository
2. Navigate to the `samsung_helper` directory
3. Build the JNI library:
   ```
   cd jni
   ndk-build
   ```
4. Build the APK:
   ```
   ./gradlew assembleDebug
   ```
5. The APK will be in `app/build/outputs/apk/debug/app-debug.apk`

## Security Considerations

This exploit uses a known vulnerability in Samsung's TTS engine. While it's being used here for legitimate screen mirroring purposes, it could potentially be used for malicious purposes. Use this tool responsibly and only on devices you own or have permission to access.

## Limitations

- This exploit may not work on all Samsung devices or OS versions
- Samsung may patch this vulnerability in future updates
- The exploit requires temporary installation of a vulnerable TTS app

## Troubleshooting

1. **Device not detected**
   - Make sure your device is connected via USB
   - Ensure USB file transfer is enabled
   - Try a different USB cable or port

2. **Exploit fails**
   - Some Samsung devices may have different security configurations
   - Try running the script with verbose logging: `./samsung_exploit.sh -v`

3. **scrcpy fails to connect**
   - Make sure your device and computer are on the same network
   - Check if ADB over network is enabled by running `adb devices`

## Credits

- Based on the original scrcpy project by Genymobile: https://github.com/Genymobile/scrcpy
- TTS vulnerability research by k0mraid3: https://github.com/k0mraid3/CVE-2019-16253
- System shell exploit implementation by zt64: https://github.com/zt64/tts-system-shell
