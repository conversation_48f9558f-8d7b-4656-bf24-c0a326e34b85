"""
Complete Audio Upscaling System
Integrates preprocessing, upscaling, and postprocessing
"""

import os
import sys
import time
import argparse
import logging
import numpy as np
import soundfile as sf
from typing import Dict, Any, Optional, List, Union, Tuple
from pathlib import Path

# Import our modules
from audio_preprocessor import AudioPreprocessor
from audio_upscaler import AudioUpscaler
from audio_postprocessor import AudioPostprocessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("audio_upscaling.log")
    ]
)
logger = logging.getLogger(__name__)

class AudioUpscalingSystem:
    """
    Complete audio upscaling system that integrates preprocessing, upscaling, and postprocessing
    """
    def __init__(self, 
                 quality_level: int = 3,
                 use_gpu: bool = True,
                 use_win11_opt: bool = True,
                 reduce_load: bool = True):
        """
        Initialize the audio upscaling system
        
        Args:
            quality_level: Quality level (1=Low, 2=Medium, 3=High)
            use_gpu: Whether to use GPU acceleration
            use_win11_opt: Whether to use Windows 11 optimizations
            reduce_load: Whether to reduce load on GPU/CPU
        """
        self.quality_level = quality_level
        self.use_gpu = use_gpu
        self.use_win11_opt = use_win11_opt
        self.reduce_load = reduce_load
        
        # Initialize components
        device = "auto" if use_gpu else "cpu"
        
        logger.info("Initializing audio upscaling system...")
        
        # Initialize preprocessor
        logger.info("Initializing preprocessor...")
        self.preprocessor = AudioPreprocessor(
            device=device,
            quality_level=quality_level,
            reduce_load=reduce_load
        )
        
        # Initialize upscaler
        logger.info("Initializing upscaler...")
        self.upscaler = AudioUpscaler(
            quality_level=quality_level,
            use_gpu=use_gpu,
            use_win11_opt=use_win11_opt,
            reduce_load=reduce_load
        )
        
        # Initialize postprocessor
        logger.info("Initializing postprocessor...")
        self.postprocessor = AudioPostprocessor(
            device=device,
            quality_level=quality_level,
            reduce_load=reduce_load
        )
        
        logger.info(f"Audio Upscaling System initialized")
        logger.info(f"  Quality level: {quality_level}")
        logger.info(f"  Using GPU: {use_gpu}")
        logger.info(f"  Windows 11 optimizations: {use_win11_opt}")
        logger.info(f"  Reduced load: {reduce_load}")
    
    def process_file(self, 
                    input_file: str, 
                    output_file: Optional[str] = None,
                    target_sample_rate: Optional[int] = None,
                    target_bit_depth: Optional[int] = None,
                    target_lufs: Optional[float] = None,
                    skip_preprocessing: bool = False,
                    skip_postprocessing: bool = False) -> str:
        """
        Process an audio file through the complete upscaling system
        
        Args:
            input_file: Path to input audio file
            output_file: Path to output audio file (optional)
            target_sample_rate: Target sample rate (optional)
            target_bit_depth: Target bit depth (optional)
            target_lufs: Target LUFS level (optional)
            skip_preprocessing: Whether to skip preprocessing
            skip_postprocessing: Whether to skip postprocessing
            
        Returns:
            Path to the processed audio file
        """
        # Check if input file exists
        if not os.path.isfile(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")
        
        # Generate output filename if not provided
        if output_file is None:
            input_dir = os.path.dirname(input_file)
            input_basename = os.path.basename(input_file)
            input_name, input_ext = os.path.splitext(input_basename)
            output_dir = os.path.join(input_dir, "upscaled")
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{input_name}_upscaled.wav")
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Start timing
        start_time = time.time()
        
        # Step 1: Preprocessing
        if not skip_preprocessing:
            logger.info("Step 1: Preprocessing...")
            preprocessed_file = self.preprocessor.preprocess_file(input_file)
            current_file = preprocessed_file
        else:
            logger.info("Skipping preprocessing...")
            current_file = input_file
        
        # Step 2: Upscaling
        logger.info("Step 2: Upscaling...")
        upscaled_file = self.upscaler.upscale_file(
            current_file,
            None,  # Generate temporary output file
            target_sample_rate,
            target_bit_depth
        )
        current_file = upscaled_file
        
        # Step 3: Postprocessing
        if not skip_postprocessing:
            logger.info("Step 3: Postprocessing...")
            postprocessed_file = self.postprocessor.postprocess_file(
                current_file,
                output_file,
                target_lufs
            )
            final_file = postprocessed_file
        else:
            logger.info("Skipping postprocessing...")
            # Copy upscaled file to output file if different
            if current_file != output_file:
                import shutil
                shutil.copy2(current_file, output_file)
            final_file = output_file
        
        # Calculate total processing time
        total_time = time.time() - start_time
        logger.info(f"Total processing time: {total_time:.2f} seconds")
        
        # Clean up temporary files
        if not skip_preprocessing and os.path.exists(preprocessed_file) and preprocessed_file != input_file and preprocessed_file != final_file:
            try:
                os.remove(preprocessed_file)
                logger.info(f"Removed temporary file: {preprocessed_file}")
            except:
                logger.warning(f"Could not remove temporary file: {preprocessed_file}")
        
        if os.path.exists(upscaled_file) and upscaled_file != input_file and upscaled_file != final_file:
            try:
                os.remove(upscaled_file)
                logger.info(f"Removed temporary file: {upscaled_file}")
            except:
                logger.warning(f"Could not remove temporary file: {upscaled_file}")
        
        return final_file
    
    def batch_process(self, 
                     input_dir: str, 
                     output_dir: Optional[str] = None,
                     file_types: List[str] = ['.wav', '.flac', '.mp3'],
                     recursive: bool = False,
                     target_sample_rate: Optional[int] = None,
                     target_bit_depth: Optional[int] = None,
                     target_lufs: Optional[float] = None,
                     skip_preprocessing: bool = False,
                     skip_postprocessing: bool = False) -> List[str]:
        """
        Batch process audio files in a directory
        
        Args:
            input_dir: Input directory
            output_dir: Output directory (optional)
            file_types: List of file extensions to process
            recursive: Whether to process subdirectories
            target_sample_rate: Target sample rate (optional)
            target_bit_depth: Target bit depth (optional)
            target_lufs: Target LUFS level (optional)
            skip_preprocessing: Whether to skip preprocessing
            skip_postprocessing: Whether to skip postprocessing
            
        Returns:
            List of processed file paths
        """
        # Check if input directory exists
        if not os.path.isdir(input_dir):
            raise FileNotFoundError(f"Input directory not found: {input_dir}")
        
        # Generate output directory if not provided
        if output_dir is None:
            output_dir = os.path.join(input_dir, "upscaled")
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Find audio files
        audio_files = []
        if recursive:
            for root, _, files in os.walk(input_dir):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in file_types):
                        audio_files.append(os.path.join(root, file))
        else:
            for file in os.listdir(input_dir):
                if any(file.lower().endswith(ext) for ext in file_types):
                    audio_files.append(os.path.join(input_dir, file))
        
        logger.info(f"Found {len(audio_files)} audio files to process")
        
        # Process each file
        processed_files = []
        for i, file in enumerate(audio_files):
            logger.info(f"Processing file {i+1}/{len(audio_files)}: {file}")
            
            # Generate output path
            rel_path = os.path.relpath(file, input_dir)
            output_path = os.path.join(output_dir, rel_path)
            output_dir_path = os.path.dirname(output_path)
            os.makedirs(output_dir_path, exist_ok=True)
            
            # Change extension to .wav
            output_path = os.path.splitext(output_path)[0] + '.wav'
            
            try:
                # Process file
                processed_file = self.process_file(
                    file,
                    output_path,
                    target_sample_rate,
                    target_bit_depth,
                    target_lufs,
                    skip_preprocessing,
                    skip_postprocessing
                )
                processed_files.append(processed_file)
                logger.info(f"Successfully processed: {file} -> {processed_file}")
            except Exception as e:
                logger.error(f"Error processing {file}: {e}")
        
        return processed_files
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze an audio file
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Dictionary with analysis results
        """
        # Check if file exists
        if not os.path.isfile(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Load audio file
        logger.info(f"Analyzing audio file: {file_path}")
        audio_data, sample_rate = sf.read(file_path)
        
        # Basic properties
        duration = len(audio_data) / sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]
        
        # Convert to mono for analysis if stereo
        if audio_data.ndim > 1:
            analysis_data = np.mean(audio_data, axis=1)
        else:
            analysis_data = audio_data
        
        # Calculate peak level
        peak_level = np.max(np.abs(analysis_data))
        peak_db = 20 * np.log10(peak_level + 1e-10)
        
        # Calculate RMS level
        rms_level = np.sqrt(np.mean(analysis_data ** 2))
        rms_db = 20 * np.log10(rms_level + 1e-10)
        
        # Calculate dynamic range (difference between peak and RMS)
        dynamic_range = peak_db - rms_db
        
        # Calculate crest factor (peak / RMS)
        crest_factor = peak_level / (rms_level + 1e-10)
        crest_factor_db = 20 * np.log10(crest_factor)
        
        # Calculate percentiles for loudness distribution
        sorted_abs = np.sort(np.abs(analysis_data))
        percentile_10 = sorted_abs[int(len(sorted_abs) * 0.1)]
        percentile_90 = sorted_abs[int(len(sorted_abs) * 0.9)]
        
        # Calculate loudness range (difference between 10th and 90th percentiles)
        loudness_range = 20 * np.log10((percentile_90 + 1e-10) / (percentile_10 + 1e-10))
        
        # Analyze noise profile
        noise_profile = self.preprocessor.analyze_noise_profile(analysis_data)
        
        # Spectral analysis
        try:
            from scipy import signal
            
            # Compute spectrogram
            f, t, Sxx = signal.spectrogram(
                analysis_data,
                fs=sample_rate,
                nperseg=2048,
                noverlap=1024,
                scaling='spectrum'
            )
            
            # Convert to dB
            Sxx_db = 10 * np.log10(Sxx + 1e-10)
            
            # Calculate spectral centroid
            spectral_centroid = np.sum(f[:, np.newaxis] * Sxx, axis=0) / np.sum(Sxx, axis=0)
            spectral_centroid_mean = np.mean(spectral_centroid)
            
            # Calculate spectral bandwidth
            spectral_bandwidth = np.sqrt(np.sum(((f[:, np.newaxis] - spectral_centroid) ** 2) * Sxx, axis=0) / np.sum(Sxx, axis=0))
            spectral_bandwidth_mean = np.mean(spectral_bandwidth)
            
            # Calculate spectral rolloff
            cumsum = np.cumsum(Sxx, axis=0)
            rolloff_threshold = 0.85
            spectral_rolloff = np.zeros_like(t)
            for i in range(len(t)):
                rolloff_point = np.where(cumsum[:, i] >= rolloff_threshold * cumsum[-1, i])[0][0]
                spectral_rolloff[i] = f[rolloff_point]
            spectral_rolloff_mean = np.mean(spectral_rolloff)
            
            spectral_analysis = {
                'spectral_centroid': spectral_centroid_mean,
                'spectral_bandwidth': spectral_bandwidth_mean,
                'spectral_rolloff': spectral_rolloff_mean
            }
        except Exception as e:
            logger.warning(f"Could not perform spectral analysis: {e}")
            spectral_analysis = {}
        
        return {
            'file_path': file_path,
            'duration': duration,
            'channels': channels,
            'sample_rate': sample_rate,
            'peak_level_db': peak_db,
            'rms_level_db': rms_db,
            'dynamic_range_db': dynamic_range,
            'crest_factor_db': crest_factor_db,
            'loudness_range_db': loudness_range,
            'percentile_10': 20 * np.log10(percentile_10 + 1e-10),
            'percentile_90': 20 * np.log10(percentile_90 + 1e-10),
            'noise_level_db': noise_profile['noise_level_db'],
            'snr_db': noise_profile['snr_db'],
            **spectral_analysis
        }


def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description='Complete Audio Upscaling System')
    
    # Input/output options
    parser.add_argument('input', help='Input audio file or directory')
    parser.add_argument('-o', '--output', help='Output audio file or directory')
    parser.add_argument('-b', '--batch', action='store_true', help='Batch process directory')
    parser.add_argument('-r', '--recursive', action='store_true', help='Process subdirectories recursively')
    parser.add_argument('-t', '--types', default='.wav,.flac,.mp3', help='File types to process (comma-separated)')
    
    # Quality options
    parser.add_argument('-q', '--quality', type=int, choices=[1, 2, 3], default=3, help='Quality level (1=Low, 2=Medium, 3=High)')
    parser.add_argument('-s', '--sample-rate', type=int, help='Target sample rate')
    parser.add_argument('-d', '--bit-depth', type=int, choices=[16, 24, 32], help='Target bit depth')
    parser.add_argument('-l', '--lufs', type=float, help='Target LUFS level')
    
    # Processing options
    parser.add_argument('--cpu', action='store_true', help='Force CPU processing')
    parser.add_argument('--no-win11-opt', action='store_true', help='Disable Windows 11 optimizations')
    parser.add_argument('--full-load', action='store_true', help='Use full CPU/GPU load')
    parser.add_argument('--skip-preprocessing', action='store_true', help='Skip preprocessing')
    parser.add_argument('--skip-postprocessing', action='store_true', help='Skip postprocessing')
    
    # Analysis options
    parser.add_argument('-a', '--analyze', action='store_true', help='Analyze audio file without processing')
    parser.add_argument('--compare', help='Compare with another file')
    
    args = parser.parse_args()
    
    try:
        # Create upscaling system
        system = AudioUpscalingSystem(
            quality_level=args.quality,
            use_gpu=not args.cpu,
            use_win11_opt=not args.no_win11_opt,
            reduce_load=not args.full_load
        )
        
        # Check if input is a file or directory
        if os.path.isfile(args.input):
            if args.analyze:
                # Analyze file
                analysis = system.analyze_file(args.input)
                
                # Print analysis results
                print("\nAudio Analysis Results:")
                print(f"File: {analysis['file_path']}")
                print(f"Duration: {analysis['duration']:.2f} seconds")
                print(f"Channels: {analysis['channels']}")
                print(f"Sample Rate: {analysis['sample_rate']} Hz")
                print(f"Peak Level: {analysis['peak_level_db']:.2f} dB")
                print(f"RMS Level: {analysis['rms_level_db']:.2f} dB")
                print(f"Dynamic Range: {analysis['dynamic_range_db']:.2f} dB")
                print(f"Crest Factor: {analysis['crest_factor_db']:.2f} dB")
                print(f"Loudness Range: {analysis['loudness_range_db']:.2f} dB")
                print(f"Noise Level: {analysis['noise_level_db']:.2f} dB")
                print(f"Signal-to-Noise Ratio: {analysis['snr_db']:.2f} dB")
                
                if 'spectral_centroid' in analysis:
                    print(f"Spectral Centroid: {analysis['spectral_centroid']:.2f} Hz")
                    print(f"Spectral Bandwidth: {analysis['spectral_bandwidth']:.2f} Hz")
                    print(f"Spectral Rolloff: {analysis['spectral_rolloff']:.2f} Hz")
                
                # Compare with another file if requested
                if args.compare and os.path.isfile(args.compare):
                    compare_analysis = system.analyze_file(args.compare)
                    
                    print("\nComparison with:", args.compare)
                    print(f"Duration: {compare_analysis['duration']:.2f} seconds")
                    print(f"Channels: {compare_analysis['channels']}")
                    print(f"Sample Rate: {compare_analysis['sample_rate']} Hz")
                    print(f"Peak Level: {compare_analysis['peak_level_db']:.2f} dB (Diff: {compare_analysis['peak_level_db'] - analysis['peak_level_db']:.2f} dB)")
                    print(f"RMS Level: {compare_analysis['rms_level_db']:.2f} dB (Diff: {compare_analysis['rms_level_db'] - analysis['rms_level_db']:.2f} dB)")
                    print(f"Dynamic Range: {compare_analysis['dynamic_range_db']:.2f} dB (Diff: {compare_analysis['dynamic_range_db'] - analysis['dynamic_range_db']:.2f} dB)")
                    print(f"Crest Factor: {compare_analysis['crest_factor_db']:.2f} dB (Diff: {compare_analysis['crest_factor_db'] - analysis['crest_factor_db']:.2f} dB)")
                    print(f"Loudness Range: {compare_analysis['loudness_range_db']:.2f} dB (Diff: {compare_analysis['loudness_range_db'] - analysis['loudness_range_db']:.2f} dB)")
                    print(f"Noise Level: {compare_analysis['noise_level_db']:.2f} dB (Diff: {compare_analysis['noise_level_db'] - analysis['noise_level_db']:.2f} dB)")
                    print(f"Signal-to-Noise Ratio: {compare_analysis['snr_db']:.2f} dB (Diff: {compare_analysis['snr_db'] - analysis['snr_db']:.2f} dB)")
                    
                    if 'spectral_centroid' in compare_analysis and 'spectral_centroid' in analysis:
                        print(f"Spectral Centroid: {compare_analysis['spectral_centroid']:.2f} Hz (Diff: {compare_analysis['spectral_centroid'] - analysis['spectral_centroid']:.2f} Hz)")
                        print(f"Spectral Bandwidth: {compare_analysis['spectral_bandwidth']:.2f} Hz (Diff: {compare_analysis['spectral_bandwidth'] - analysis['spectral_bandwidth']:.2f} Hz)")
                        print(f"Spectral Rolloff: {compare_analysis['spectral_rolloff']:.2f} Hz (Diff: {compare_analysis['spectral_rolloff'] - analysis['spectral_rolloff']:.2f} Hz)")
            else:
                # Process file
                output_file = system.process_file(
                    args.input,
                    args.output,
                    args.sample_rate,
                    args.bit_depth,
                    args.lufs,
                    args.skip_preprocessing,
                    args.skip_postprocessing
                )
                print(f"\nProcessed file saved to: {output_file}")
        elif os.path.isdir(args.input) and args.batch:
            # Batch process directory
            file_types = args.types.split(',')
            processed_files = system.batch_process(
                args.input,
                args.output,
                file_types,
                args.recursive,
                args.sample_rate,
                args.bit_depth,
                args.lufs,
                args.skip_preprocessing,
                args.skip_postprocessing
            )
            print(f"\nProcessed {len(processed_files)} files")
        else:
            if not os.path.exists(args.input):
                print(f"Error: Input path does not exist: {args.input}")
            elif os.path.isdir(args.input) and not args.batch:
                print(f"Error: Input is a directory but --batch flag is not set")
            else:
                print(f"Error: Invalid input path: {args.input}")
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
