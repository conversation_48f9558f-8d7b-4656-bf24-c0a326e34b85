@echo off
echo Processing RAVE.flac with Sony-inspired upscaling techniques...
echo.

REM Check if PyTorch is installed
python -c "import torch" 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo PyTorch is not installed. Installing required packages...
    pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install PyTorch. Continuing with CPU processing...
    ) else (
        echo PyTorch installed successfully.
    )
)

REM Check if other required packages are installed
python -c "import numpy, scipy, soundfile" 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    pip install numpy scipy soundfile
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install required packages. Please install them manually.
        pause
        exit /b 1
    )
)

REM Set quality level (3=highest quality, preserves dynamics better)
set QUALITY=3

REM Set upscale factor (2=double sample rate)
set UPSCALE_FACTOR=2

REM Set reduce_load flag (true=better quality but slower processing)
set REDUCE_LOAD=true

REM Set AI model type (dynamic=combined time and frequency domain processing)
set AI_MODEL=dynamic

REM Process with GPU acceleration and Windows 11 optimizations
echo Processing with quality level %QUALITY% and upscale factor %UPSCALE_FACTOR%x...
echo Using enhanced dynamic range processing with AI model (%AI_MODEL%)...
echo This may take some time for higher quality results...
echo.

python process_rave_gpu.py --quality %QUALITY% --upscale-factor %UPSCALE_FACTOR% --ai-model %AI_MODEL%

if %ERRORLEVEL% NEQ 0 (
    echo Processing failed. Trying with CPU only...
    python process_rave_gpu.py --quality %QUALITY% --upscale-factor %UPSCALE_FACTOR% --no-gpu --ai-model %AI_MODEL%

    if %ERRORLEVEL% NEQ 0 (
        echo Processing failed with AI model. Trying without AI...
        python process_rave_gpu.py --quality %QUALITY% --upscale-factor %UPSCALE_FACTOR% --no-gpu --no-ai

        if %ERRORLEVEL% NEQ 0 (
            echo Processing failed.
            pause
            exit /b 1
        )
    )
)

echo.
echo Processing complete!
echo.
echo Two versions have been created:
echo 1. Natural version (44.1kHz) - Better for most listening
echo 2. High-resolution version (88.2kHz or higher) - For audiophile equipment
echo.
echo The natural version is playing now.
echo.
pause
