@echo off
echo Adding Python to PATH...

REM Try common Python installation locations
set PYTHON_PATHS=^
C:\Python311;^
C:\Python310;^
C:\Python39;^
C:\Python38;^
C:\Python37;^
C:\Program Files\Python311;^
C:\Program Files\Python310;^
C:\Program Files\Python39;^
C:\Program Files\Python38;^
C:\Program Files\Python37;^
C:\Program Files (x86)\Python311;^
C:\Program Files (x86)\Python310;^
C:\Program Files (x86)\Python39;^
C:\Program Files (x86)\Python38;^
C:\Program Files (x86)\Python37;^
C:\Users\<USER>\AppData\Local\Programs\Python\Python311;^
C:\Users\<USER>\AppData\Local\Programs\Python\Python310;^
C:\Users\<USER>\AppData\Local\Programs\Python\Python39;^
C:\Users\<USER>\AppData\Local\Programs\Python\Python38;^
C:\Users\<USER>\AppData\Local\Programs\Python\Python37

REM Check each path and add to PATH if it exists
for %%p in (%PYTHON_PATHS%) do (
    if exist "%%p\python.exe" (
        echo Found Python at: %%p
        set "PATH=%%p;%%p\Scripts;%PATH%"
        goto :found
    )
)

echo Python not found in common locations.
echo Please enter the full path to your Python installation directory:
set /p CUSTOM_PATH=

if exist "%CUSTOM_PATH%\python.exe" (
    echo Found Python at: %CUSTOM_PATH%
    set "PATH=%CUSTOM_PATH%;%CUSTOM_PATH%\Scripts;%PATH%"
    goto :found
) else (
    echo Python not found at specified path.
    goto :not_found
)

:found
echo Python has been added to PATH for this session.
echo Testing Python installation...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo Failed to run Python. Please check your installation.
) else (
    echo Python is now available in this terminal session.
    echo You can run the Windows 11 optimizations test with:
    echo python windows11_optimizations.py
)
goto :end

:not_found
echo Python was not found on your system.
echo Please install Python from https://www.python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation.

:end
echo.
echo Press any key to continue...
pause > nul
