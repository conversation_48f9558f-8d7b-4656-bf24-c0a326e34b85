"""
Simple script to process RAVE.flac with AudioSR
"""

import os
import sys
import time
import platform
import subprocess

def play_audio_file(file_path):
    """Play audio file using system default player"""
    print(f"Playing: {file_path}")
    
    # Use system default player
    if platform.system() == "Windows":
        os.startfile(file_path)
    elif platform.system() == "Darwin":  # macOS
        subprocess.run(["open", file_path])
    else:  # Linux
        subprocess.run(["xdg-open", file_path])
    
    print("Audio playback started. Use the system player controls to control playback.")

def main():
    # Path to the RAVE.flac file
    input_file = r"C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac"
    
    # Check if file exists
    if not os.path.isfile(input_file):
        print(f"Error: File not found: {input_file}")
        
        # Try to find the file in the Downloads folder
        downloads_dir = os.path.expanduser("~/Downloads")
        print(f"Searching in Downloads folder: {downloads_dir}")
        
        for file in os.listdir(downloads_dir):
            if "RAVE" in file and file.endswith(".flac"):
                input_file = os.path.join(downloads_dir, file)
                print(f"Found file: {input_file}")
                break
        else:
            print("Could not find RAVE.flac in Downloads folder.")
            return 1
    
    # Try to import AudioSR
    try:
        from audiosr import build_model, super_resolution
        import soundfile as sf
        import numpy as np
        
        # Apply Windows 11 optimizations
        try:
            from windows11_optimizations import Windows11Optimizer
            print("Applying Windows 11 optimizations...")
            optimizer = Windows11Optimizer()
            optimizations = optimizer.optimize_audio_processing()
            print("Optimization results:")
            for key, value in optimizations.items():
                print(f"  {key}: {value}")
        except ImportError:
            print("Windows 11 optimizations not available, continuing without them.")
        
        # Generate output filename
        input_dir = os.path.dirname(input_file)
        input_basename = os.path.basename(input_file)
        input_name, ext = os.path.splitext(input_basename)
        output_dir = os.path.join(input_dir, "audiosr_output")
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, f"{input_name}_SR.wav")
        
        # Build model
        print("Building model 'basic' on cpu...")
        start_time = time.time()
        model = build_model(model_name="basic", device="cpu")
        model_load_time = time.time() - start_time
        print(f"Model loaded in {model_load_time:.2f} seconds")
        
        # Process audio
        print(f"Processing audio: {input_file}")
        print("Parameters: guidance_scale=3.5, ddim_steps=10")
        process_start = time.time()
        
        waveform = super_resolution(
            model,
            input_file,
            guidance_scale=3.5,
            ddim_steps=10  # Fast mode
        )
        
        process_time = time.time() - process_start
        print(f"Audio processed in {process_time:.2f} seconds")
        
        # Save output
        print(f"Saving output to: {output_file}")
        sf.write(output_file, waveform, 48000)  # AudioSR outputs at 48kHz
        
        # Play the processed file
        print("\n=== Playing Processed Audio ===")
        play_audio_file(output_file)
        
    except ImportError as e:
        print(f"Error: Required module not found: {e}")
        print("Please install AudioSR and its dependencies manually.")
        return 1
    except Exception as e:
        print(f"Error processing audio: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
