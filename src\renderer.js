// DOM Elements
const statusText = document.getElementById('status-text');
const overlayOpacity = document.getElementById('overlay-opacity');

// Update status
function updateStatus(message) {
  statusText.textContent = message;
  
  // Reset status after 5 seconds
  setTimeout(() => {
    statusText.textContent = 'Ready';
  }, 5000);
}

// Event listeners
overlayOpacity.addEventListener('change', (e) => {
  const opacity = parseFloat(e.target.value);
  window.api.send('set-overlay-opacity', opacity);
});

// Listen for status updates from main process
window.api.receive('status-update', (message) => {
  updateStatus(message);
});
