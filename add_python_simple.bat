@echo off
echo Adding Python to PATH...

REM Add common Python paths to PATH
set "PATH=C:\Python311;C:\Python311\Scripts;%PATH%"
set "PATH=C:\Python310;C:\Python310\Scripts;%PATH%"
set "PATH=C:\Python39;C:\Python39\Scripts;%PATH%"
set "PATH=C:\Program Files\Python311;C:\Program Files\Python311\Scripts;%PATH%"
set "PATH=C:\Program Files\Python310;C:\Program Files\Python310\Scripts;%PATH%"
set "PATH=C:\Program Files\Python39;C:\Program Files\Python39\Scripts;%PATH%"
set "PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;%PATH%"
set "PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts;%PATH%"
set "PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python39;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts;%PATH%"

echo Python paths have been added to PATH for this session.
echo Testing Python installation...
python --version

if %ERRORLEVEL% NEQ 0 (
    echo Failed to run Python. Please check your installation.
) else (
    echo Python is now available in this terminal session.
    echo You can run the Windows 11 optimizations test with:
    echo python windows11_optimizations.py
)

echo.
echo Press any key to continue...
pause > nul
