const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const config = require('../config');

class MistralOCR {
  constructor(apiKey = config.mistral.apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.mistral.ai/v1/ocr';
    this.headers = {
      'Authorization': `Bearer ${this.apiKey}`
    };
  }

  /**
   * Process an image with Mistral OCR API
   * @param {string} imagePath - Path to the image file
   * @returns {Promise<string>} - Extracted text
   */
  async processImage(imagePath) {
    try {
      // Check if file exists
      if (!fs.existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      // Create form data
      const formData = new FormData();
      formData.append('file', fs.createReadStream(imagePath));
      formData.append('model', 'mistral-ocr-latest');

      // Make API request
      const response = await axios.post(this.baseUrl, formData, {
        headers: {
          ...this.headers,
          ...formData.getHeaders()
        }
      });

      // Process response
      if (response.data?.pages) {
        // Extract text from all pages
        const extractedText = response.data.pages
          .map(page => page.markdown || page.text)
          .join('\n\n');

        return extractedText;
      } else {
        throw new Error('Invalid response format from Mistral OCR API');
      }
    } catch (error) {
      console.error('Mistral OCR Error:', error.message);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    }
  }

  /**
   * Process a base64 image with Mistral OCR API
   * @param {string} base64Image - Base64 encoded image
   * @returns {Promise<string>} - Extracted text
   */
  async processBase64Image(base64Image) {
    try {
      // Create temporary file from base64
      const tempDir = path.join(__dirname, '../temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFilePath = path.join(tempDir, `temp_${Date.now()}.png`);

      // Remove data URL prefix if present
      const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');
      fs.writeFileSync(tempFilePath, Buffer.from(base64Data, 'base64'));

      // Process the temporary file
      const result = await this.processImage(tempFilePath);

      // Clean up temporary file
      fs.unlinkSync(tempFilePath);

      return result;
    } catch (error) {
      console.error('Base64 OCR Error:', error.message);
      throw error;
    }
  }
}

module.exports = MistralOCR;
