* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

header {
  text-align: center;
  margin-bottom: 30px;
}

h1 {
  font-size: 2.5rem;
  color: #2c3e50;
}

h2 {
  font-size: 1.8rem;
  color: #3498db;
  margin-bottom: 15px;
}

.instructions, .status, .settings {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

ul {
  list-style-position: inside;
  margin-left: 10px;
}

li {
  margin-bottom: 10px;
}

.setting-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

label {
  flex: 0 0 150px;
  font-weight: bold;
}

input[type="range"] {
  flex: 1;
}

footer {
  text-align: center;
  margin-top: 30px;
  color: #7f8c8d;
  font-size: 0.9rem;
}
