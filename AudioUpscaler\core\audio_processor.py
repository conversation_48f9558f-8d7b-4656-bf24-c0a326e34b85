"""
Audio Processor Module
Handles audio processing, upscaling, and enhancement
"""

import os
import time
import numpy as np
import soundfile as sf
from typing import Dict, Any, Tuple, Optional, List

class AudioProcessor:
    """
    Handles audio processing, upscaling, and enhancement
    """
    def __init__(self, use_win11_opt=True, use_hardware_accel=True):
        self.use_win11_opt = use_win11_opt
        self.use_hardware_accel = use_hardware_accel
        self.windows_optimizer = None
        
        # Initialize Windows 11 optimizations if enabled
        if self.use_win11_opt:
            self._initialize_win11_optimizations()
        
        # Initialize hardware acceleration if enabled
        if self.use_hardware_accel:
            self._initialize_hardware_acceleration()
    
    def _initialize_win11_optimizations(self):
        """Initialize Windows 11 optimizations"""
        try:
            from .windows11_optimizations import Windows11Optimizer
            self.windows_optimizer = Windows11Optimizer()
            
            if self.windows_optimizer.is_windows11:
                print("Windows 11 detected. Optimizations available.")
                # Apply optimizations
                optimizations = self.windows_optimizer.optimize_audio_processing()
                print("Optimization results:")
                for key, value in optimizations.items():
                    print(f"  {key}: {value}")
                
                # Set up exclusive audio if available
                exclusive_audio = self.windows_optimizer.setup_exclusive_audio()
                print(f"Exclusive audio setup: {'Success' if exclusive_audio else 'Failed'}")
                
                # Enable hardware acceleration if available
                if self.use_hardware_accel:
                    hw_accel = self.windows_optimizer.enable_hardware_acceleration()
                    print(f"Hardware acceleration: {'Enabled' if hw_accel else 'Disabled'}")
            else:
                print("Not running on Windows 11. Some optimizations may not be available.")
        except ImportError:
            print("Windows 11 optimizations not available.")
            self.windows_optimizer = None
    
    def _initialize_hardware_acceleration(self):
        """Initialize hardware acceleration"""
        try:
            import torch
            self.has_torch = True
            self.has_cuda = torch.cuda.is_available()
            
            if self.has_cuda:
                print(f"CUDA available: {torch.cuda.get_device_name(0)}")
                self.device = torch.device("cuda")
            else:
                print("CUDA not available, using CPU")
                self.device = torch.device("cpu")
        except ImportError:
            print("PyTorch not available, using CPU processing")
            self.has_torch = False
            self.has_cuda = False
    
    def process_file(self, input_file: str, output_file: Optional[str] = None, 
                    upscale_factor: int = 2, quality_level: int = 2) -> Optional[str]:
        """
        Process an audio file with upscaling
        
        Args:
            input_file: Path to input audio file
            output_file: Path to output audio file (optional)
            upscale_factor: Factor to upscale the audio (2, 4, or 8)
            quality_level: Quality level (1=Low, 2=Medium, 3=High)
            
        Returns:
            Path to output file if successful, None otherwise
        """
        try:
            # Check if input file exists
            if not os.path.isfile(input_file):
                print(f"Error: Input file not found: {input_file}")
                return None
            
            # Generate output filename if not provided
            if output_file is None:
                input_dir = os.path.dirname(input_file)
                input_basename = os.path.basename(input_file)
                input_name, ext = os.path.splitext(input_basename)
                output_dir = os.path.join(input_dir, "upscaled")
                os.makedirs(output_dir, exist_ok=True)
                output_file = os.path.join(output_dir, f"{input_name}_upscaled{ext}")
            
            # Load input file
            print(f"Loading input file: {input_file}")
            audio_data, sample_rate = sf.read(input_file)
            print(f"Input file loaded: {audio_data.shape}, {sample_rate}Hz")
            
            # Process audio
            print("Processing audio...")
            print(f"Upscale factor: {upscale_factor}x")
            print(f"Quality level: {quality_level}")
            
            # Get quality parameters
            if quality_level == 1:  # Low
                quality_params = {"iterations": 1, "filter_size": 16}
            elif quality_level == 2:  # Medium
                quality_params = {"iterations": 2, "filter_size": 32}
            else:  # High
                quality_params = {"iterations": 3, "filter_size": 64}
            
            # Process with scipy if available
            try:
                from scipy import signal
                
                start_time = time.time()
                
                # Determine target sample rate
                target_sample_rate = sample_rate * upscale_factor
                print(f"Target sample rate: {target_sample_rate}Hz")
                
                # Process audio with resampling
                if audio_data.ndim == 1:
                    # Mono
                    processed_audio = signal.resample_poly(
                        audio_data, 
                        upscale_factor, 
                        1, 
                        window=('kaiser', quality_params["filter_size"])
                    )
                else:
                    # Stereo or multi-channel
                    processed_audio = np.zeros((int(audio_data.shape[0] * upscale_factor), audio_data.shape[1]))
                    for i in range(audio_data.shape[1]):
                        processed_audio[:, i] = signal.resample_poly(
                            audio_data[:, i], 
                            upscale_factor, 
                            1, 
                            window=('kaiser', quality_params["filter_size"])
                        )
                
                # Apply additional processing based on quality level
                for _ in range(quality_params["iterations"]):
                    # Apply some enhancement (simple high-frequency boost for demonstration)
                    if processed_audio.ndim == 1:
                        # Mono
                        spectrum = np.fft.rfft(processed_audio)
                        # Boost high frequencies
                        freq_bins = len(spectrum)
                        boost_start = int(freq_bins * 0.5)  # Boost frequencies above 50%
                        boost_factor = 1.2  # 20% boost
                        spectrum[boost_start:] *= boost_factor
                        processed_audio = np.fft.irfft(spectrum, len(processed_audio))
                    else:
                        # Stereo or multi-channel
                        for i in range(processed_audio.shape[1]):
                            spectrum = np.fft.rfft(processed_audio[:, i])
                            # Boost high frequencies
                            freq_bins = len(spectrum)
                            boost_start = int(freq_bins * 0.5)  # Boost frequencies above 50%
                            boost_factor = 1.2  # 20% boost
                            spectrum[boost_start:] *= boost_factor
                            processed_audio[:, i] = np.fft.irfft(spectrum, len(processed_audio[:, i]))
                
                process_time = time.time() - start_time
                print(f"Processing completed in {process_time:.2f} seconds")
                
            except ImportError:
                print("scipy not available, using simple interpolation")
                
                # Simple interpolation as fallback
                if audio_data.ndim == 1:
                    # Mono
                    processed_audio = np.interp(
                        np.linspace(0, len(audio_data) - 1, len(audio_data) * upscale_factor),
                        np.arange(len(audio_data)),
                        audio_data
                    )
                else:
                    # Stereo or multi-channel
                    processed_audio = np.zeros((int(audio_data.shape[0] * upscale_factor), audio_data.shape[1]))
                    for i in range(audio_data.shape[1]):
                        processed_audio[:, i] = np.interp(
                            np.linspace(0, len(audio_data[:, i]) - 1, len(audio_data[:, i]) * upscale_factor),
                            np.arange(len(audio_data[:, i])),
                            audio_data[:, i]
                        )
                
                target_sample_rate = sample_rate * upscale_factor
            
            # Save output
            print(f"Saving output to: {output_file}")
            sf.write(output_file, processed_audio, target_sample_rate)
            
            print("Processing complete!")
            return output_file
            
        except Exception as e:
            print(f"Error processing audio: {e}")
            return None
    
    def process_realtime(self, input_data: np.ndarray, sample_rate: int, 
                        upscale_factor: int = 2, quality_level: int = 1) -> np.ndarray:
        """
        Process audio data in real-time
        
        Args:
            input_data: Input audio data as numpy array
            sample_rate: Sample rate of input audio
            upscale_factor: Factor to upscale the audio (2, 4, or 8)
            quality_level: Quality level (1=Low, 2=Medium, 3=High)
            
        Returns:
            Processed audio data as numpy array
        """
        try:
            # Get quality parameters (simplified for real-time)
            if quality_level == 1:  # Low
                quality_params = {"filter_size": 8}
            elif quality_level == 2:  # Medium
                quality_params = {"filter_size": 16}
            else:  # High
                quality_params = {"filter_size": 32}
            
            # Process with scipy if available
            try:
                from scipy import signal
                
                # Process audio with resampling
                if input_data.ndim == 1:
                    # Mono
                    processed_audio = signal.resample_poly(
                        input_data, 
                        upscale_factor, 
                        1, 
                        window=('kaiser', quality_params["filter_size"])
                    )
                else:
                    # Stereo or multi-channel
                    processed_audio = np.zeros((int(input_data.shape[0] * upscale_factor), input_data.shape[1]))
                    for i in range(input_data.shape[1]):
                        processed_audio[:, i] = signal.resample_poly(
                            input_data[:, i], 
                            upscale_factor, 
                            1, 
                            window=('kaiser', quality_params["filter_size"])
                        )
                
                # Apply simple enhancement
                if processed_audio.ndim == 1:
                    # Mono
                    spectrum = np.fft.rfft(processed_audio)
                    # Boost high frequencies
                    freq_bins = len(spectrum)
                    boost_start = int(freq_bins * 0.5)  # Boost frequencies above 50%
                    boost_factor = 1.2  # 20% boost
                    spectrum[boost_start:] *= boost_factor
                    processed_audio = np.fft.irfft(spectrum, len(processed_audio))
                else:
                    # Stereo or multi-channel
                    for i in range(processed_audio.shape[1]):
                        spectrum = np.fft.rfft(processed_audio[:, i])
                        # Boost high frequencies
                        freq_bins = len(spectrum)
                        boost_start = int(freq_bins * 0.5)  # Boost frequencies above 50%
                        boost_factor = 1.2  # 20% boost
                        spectrum[boost_start:] *= boost_factor
                        processed_audio[:, i] = np.fft.irfft(spectrum, len(processed_audio[:, i]))
                
            except ImportError:
                print("scipy not available, using simple interpolation")
                
                # Simple interpolation as fallback
                if input_data.ndim == 1:
                    # Mono
                    processed_audio = np.interp(
                        np.linspace(0, len(input_data) - 1, len(input_data) * upscale_factor),
                        np.arange(len(input_data)),
                        input_data
                    )
                else:
                    # Stereo or multi-channel
                    processed_audio = np.zeros((int(input_data.shape[0] * upscale_factor), input_data.shape[1]))
                    for i in range(input_data.shape[1]):
                        processed_audio[:, i] = np.interp(
                            np.linspace(0, len(input_data[:, i]) - 1, len(input_data[:, i]) * upscale_factor),
                            np.arange(len(input_data[:, i])),
                            input_data[:, i]
                        )
            
            return processed_audio
            
        except Exception as e:
            print(f"Error processing audio in real-time: {e}")
            return input_data  # Return original data on error
