@echo off
echo Audio Upscaler Cleanup Utility
echo -----------------------------
echo.
echo This utility will terminate all running Audio Upscaler processes.
echo.

REM Set Python path
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Run the cleanup script
echo Terminating Audio Upscaler processes...
"%PYTHON_PATH%\python.exe" -c "import sys; sys.path.append('.'); from utils.process_manager import kill_all_upscaler_processes; killed = kill_all_upscaler_processes(); print(f'Terminated {killed} processes.')"

if %ERRORLEVEL% NEQ 0 (
    echo Failed to terminate processes.
    echo You may need to end them manually using Task Manager.
) else (
    echo Cleanup complete.
)

echo.
pause
