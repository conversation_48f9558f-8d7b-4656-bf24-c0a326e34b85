@echo off
echo Process any audio file from Downloads folder with Audio Super Resolution

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Check if Audio<PERSON> is installed
echo Checking if AudioSR is installed...
"%PYTHON_PATH%\python.exe" -c "import audiosr" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo AudioSR is not installed. Installing now...
    "%PYTHON_PATH%\python.exe" install_audiosr.py
)

REM Process any audio file
echo.
echo Running script...
"%PYTHON_PATH%\python.exe" process_any_audio_sr.py --model basic --device cpu --guidance-scale 3.5 --ddim-steps 30 --play-processed

echo.
echo Done!
pause
