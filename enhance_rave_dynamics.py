"""
Process the RAVE.flac file with dynamic range enhancement
"""

import os
import sys
import time
import logging
import argparse
from typing import Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("rave_dynamics.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def find_rave_file():
    """Find the RAVE.flac file in the Downloads folder"""
    downloads_dir = os.path.join(os.path.expanduser("~"), "Downloads")
    
    # Look for files containing "RAVE" and ending with ".flac"
    for file in os.listdir(downloads_dir):
        if "RAVE" in file and file.endswith(".flac"):
            return os.path.join(downloads_dir, file)
    
    return None

def enhance_rave_dynamics(input_file: Optional[str] = None,
                         output_dir: Optional[str] = None,
                         quality_level: int = 3,
                         preserve_transients: bool = True,
                         multiband: bool = True,
                         play_result: bool = True):
    """
    Enhance the dynamic range of the RAVE.flac file
    
    Args:
        input_file: Path to input file (if None, will search for RAVE.flac)
        output_dir: Output directory (if None, will use 'dynamic_enhanced' in same dir as input)
        quality_level: Quality level (1=Low, 2=Medium, 3=High)
        preserve_transients: Whether to preserve transients
        multiband: Whether to use multiband processing
        play_result: Whether to play the result after processing
    
    Returns:
        Path to the processed file
    """
    # Find input file if not provided
    if input_file is None:
        input_file = find_rave_file()
        if input_file is None:
            logger.error("RAVE.flac file not found in Downloads folder")
            return None
    
    # Check if input file exists
    if not os.path.isfile(input_file):
        logger.error(f"Input file not found: {input_file}")
        return None
    
    logger.info(f"Processing file: {input_file}")
    
    # Create output directory if not provided
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(input_file), "dynamic_enhanced")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Create output path
    output_file = os.path.join(output_dir, "RAVE_dynamic_enhanced.wav")
    logger.info(f"Output will be saved to: {output_file}")
    
    try:
        # Import the dynamic range enhancer
        from dynamic_range_enhancer import DynamicRangeEnhancer
        
        # Create enhancer
        enhancer = DynamicRangeEnhancer(
            quality_level=quality_level,
            preserve_transients=preserve_transients,
            multiband=multiband
        )
        
        # Process file
        start_time = time.time()
        output_file = enhancer.process_file(input_file, output_file)
        process_time = time.time() - start_time
        
        logger.info(f"Processing completed in {process_time:.2f} seconds")
        logger.info(f"Output saved to: {output_file}")
        
        # Play result if requested
        if play_result:
            logger.info("Playing processed file...")
            if sys.platform == "win32":
                os.startfile(output_file)
                logger.info("File opened in default media player")
            else:
                logger.info("Automatic playback not supported on this platform")
        
        return output_file
    
    except Exception as e:
        logger.error(f"Error processing audio: {e}", exc_info=True)
        return None

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Enhance the dynamic range of the RAVE.flac file")
    parser.add_argument("--input", help="Path to input audio file (optional, will search for RAVE.flac if not provided)")
    parser.add_argument("--output-dir", help="Output directory (optional)")
    parser.add_argument("--quality", type=int, choices=[1, 2, 3], default=3, help="Quality level (1=Low, 2=Medium, 3=High)")
    parser.add_argument("--no-transients", action="store_true", help="Disable transient preservation")
    parser.add_argument("--no-multiband", action="store_true", help="Disable multiband processing")
    parser.add_argument("--no-play", action="store_true", help="Don't play the result after processing")
    
    args = parser.parse_args()
    
    # Process file
    output_file = enhance_rave_dynamics(
        input_file=args.input,
        output_dir=args.output_dir,
        quality_level=args.quality,
        preserve_transients=not args.no_transients,
        multiband=not args.no_multiband,
        play_result=not args.no_play
    )
    
    if output_file:
        logger.info(f"Successfully processed file: {output_file}")
        return 0
    else:
        logger.error("Processing failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
