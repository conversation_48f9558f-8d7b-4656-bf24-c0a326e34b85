"""
File Comparison Tool
GUI tool to compare original and upscaled audio files
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import webbrowser

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from utils.file_comparison import compare_audio_files, generate_comparison_report
from core.file_utils import play_audio_file, open_directory

class FileComparisonTool(tk.Tk):
    """GUI for comparing audio files"""
    
    def __init__(self):
        super().__init__()
        
        # Set up the main window
        self.title("Audio File Comparison Tool")
        self.geometry("700x500")
        self.minsize(700, 500)
        
        # Initialize variables
        self.original_file = None
        self.upscaled_file = None
        self.comparison_results = None
        self.report_path = None
        
        # Create the main frame
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create title
        title_label = ttk.Label(self.main_frame, text="Audio File Comparison Tool", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Create file selection frame
        file_frame = ttk.LabelFrame(self.main_frame, text="File Selection")
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Original file selection
        original_frame = ttk.Frame(file_frame)
        original_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(original_frame, text="Original File:").pack(side=tk.LEFT, padx=(0, 10))
        self.original_var = tk.StringVar()
        self.original_entry = ttk.Entry(original_frame, textvariable=self.original_var, width=50)
        self.original_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(original_frame, text="Browse", command=self._browse_original).pack(side=tk.LEFT, padx=5)
        ttk.Button(original_frame, text="Play", command=lambda: play_audio_file(self.original_file)).pack(side=tk.LEFT, padx=5)
        
        # Upscaled file selection
        upscaled_frame = ttk.Frame(file_frame)
        upscaled_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(upscaled_frame, text="Upscaled File:").pack(side=tk.LEFT, padx=(0, 10))
        self.upscaled_var = tk.StringVar()
        self.upscaled_entry = ttk.Entry(upscaled_frame, textvariable=self.upscaled_var, width=50)
        self.upscaled_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(upscaled_frame, text="Browse", command=self._browse_upscaled).pack(side=tk.LEFT, padx=5)
        ttk.Button(upscaled_frame, text="Play", command=lambda: play_audio_file(self.upscaled_file)).pack(side=tk.LEFT, padx=5)
        
        # Add compare button
        compare_frame = ttk.Frame(self.main_frame)
        compare_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.compare_button = ttk.Button(compare_frame, text="Compare Files", command=self._compare_files)
        self.compare_button.pack(side=tk.LEFT, padx=5)
        
        self.view_report_button = ttk.Button(compare_frame, text="View Report", command=self._view_report, state=tk.DISABLED)
        self.view_report_button.pack(side=tk.LEFT, padx=5)
        
        # Add results frame
        results_frame = ttk.LabelFrame(self.main_frame, text="Comparison Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Add results text
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, height=10, width=80)
        self.results_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.results_text, orient=tk.VERTICAL, command=self.results_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_text.config(yscrollcommand=scrollbar.set)
        
        # Add status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=(5, 0))
    
    def _browse_original(self):
        """Browse for original audio file"""
        file_path = filedialog.askopenfilename(
            title="Select Original Audio File",
            filetypes=[
                ("Audio Files", "*.wav *.mp3 *.flac *.ogg *.m4a *.aac"),
                ("All Files", "*.*")
            ]
        )
        
        if file_path:
            self.original_file = file_path
            self.original_var.set(file_path)
            
            # Try to find matching upscaled file
            if not self.upscaled_file:
                original_dir = os.path.dirname(file_path)
                original_name = os.path.splitext(os.path.basename(file_path))[0]
                
                # Check common upscaled file patterns
                upscaled_patterns = [
                    f"{original_name}_upscaled",
                    f"{original_name}_SR",
                    f"{original_name}_enhanced",
                    f"{original_name}_processed"
                ]
                
                # Check in common output directories
                search_dirs = [
                    original_dir,
                    os.path.join(original_dir, "upscaled"),
                    os.path.join(original_dir, "audiosr_output")
                ]
                
                for search_dir in search_dirs:
                    if os.path.isdir(search_dir):
                        for file in os.listdir(search_dir):
                            file_base = os.path.splitext(file)[0]
                            if any(pattern in file_base for pattern in upscaled_patterns):
                                upscaled_path = os.path.join(search_dir, file)
                                self.upscaled_file = upscaled_path
                                self.upscaled_var.set(upscaled_path)
                                self.status_var.set(f"Found matching upscaled file: {file}")
                                break
    
    def _browse_upscaled(self):
        """Browse for upscaled audio file"""
        file_path = filedialog.askopenfilename(
            title="Select Upscaled Audio File",
            filetypes=[
                ("Audio Files", "*.wav *.mp3 *.flac *.ogg *.m4a *.aac"),
                ("All Files", "*.*")
            ]
        )
        
        if file_path:
            self.upscaled_file = file_path
            self.upscaled_var.set(file_path)
    
    def _compare_files(self):
        """Compare the selected audio files"""
        if not self.original_file:
            messagebox.showerror("Error", "Please select an original audio file")
            return
        
        if not self.upscaled_file:
            messagebox.showerror("Error", "Please select an upscaled audio file")
            return
        
        # Disable UI during comparison
        self.compare_button.config(state=tk.DISABLED)
        self.view_report_button.config(state=tk.DISABLED)
        self.status_var.set("Comparing files...")
        
        # Clear results
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "Comparing files...\n")
        
        # Run comparison in a separate thread
        threading.Thread(target=self._compare_files_thread, daemon=True).start()
    
    def _compare_files_thread(self):
        """Thread for file comparison"""
        try:
            # Compare files
            self.comparison_results = compare_audio_files(self.original_file, self.upscaled_file)
            
            # Generate report
            self.report_path = generate_comparison_report(self.comparison_results)
            
            # Update UI
            self.after(0, self._update_results)
            
        except Exception as e:
            self.after(0, lambda: self._show_error(f"Error comparing files: {str(e)}"))
    
    def _update_results(self):
        """Update the results display"""
        # Re-enable UI
        self.compare_button.config(state=tk.NORMAL)
        
        if 'error' in self.comparison_results:
            self._show_error(self.comparison_results['error'])
            return
        
        # Enable view report button if report was generated
        if self.report_path:
            self.view_report_button.config(state=tk.NORMAL)
        
        # Clear results
        self.results_text.delete(1.0, tk.END)
        
        # Add summary
        self.results_text.insert(tk.END, "=== Comparison Summary ===\n\n")
        
        # Original file info
        self.results_text.insert(tk.END, "Original File:\n")
        self.results_text.insert(tk.END, f"  Name: {self.comparison_results['original']['name']}\n")
        self.results_text.insert(tk.END, f"  Sample Rate: {self.comparison_results['original']['sample_rate']} Hz\n")
        self.results_text.insert(tk.END, f"  Channels: {self.comparison_results['original']['channels']}\n")
        self.results_text.insert(tk.END, f"  Duration: {self.comparison_results['original']['duration']:.2f} seconds\n")
        self.results_text.insert(tk.END, f"  File Size: {self.comparison_results['original']['size'] / 1024:.2f} KB\n\n")
        
        # Upscaled file info
        self.results_text.insert(tk.END, "Upscaled File:\n")
        self.results_text.insert(tk.END, f"  Name: {self.comparison_results['upscaled']['name']}\n")
        self.results_text.insert(tk.END, f"  Sample Rate: {self.comparison_results['upscaled']['sample_rate']} Hz\n")
        self.results_text.insert(tk.END, f"  Channels: {self.comparison_results['upscaled']['channels']}\n")
        self.results_text.insert(tk.END, f"  Duration: {self.comparison_results['upscaled']['duration']:.2f} seconds\n")
        self.results_text.insert(tk.END, f"  File Size: {self.comparison_results['upscaled']['size'] / 1024:.2f} KB\n\n")
        
        # Comparison results
        self.results_text.insert(tk.END, "Comparison Results:\n")
        self.results_text.insert(tk.END, f"  Sample Rate Ratio: {self.comparison_results['comparison']['sample_rate_ratio']:.2f}\n")
        self.results_text.insert(tk.END, f"  Duration Ratio: {self.comparison_results['comparison']['duration_ratio']:.2f}\n")
        self.results_text.insert(tk.END, f"  File Size Ratio: {self.comparison_results['comparison']['size_ratio']:.2f}\n")
        
        # Add spectral analysis if available
        if 'spectral_difference' in self.comparison_results['comparison']:
            self.results_text.insert(tk.END, f"  Spectral Difference: {self.comparison_results['comparison']['spectral_difference']:.4f}\n")
            self.results_text.insert(tk.END, f"  High Frequency Ratio: {self.comparison_results['comparison']['high_frequency_ratio']:.2f}\n")
        
        # Add conclusion
        self.results_text.insert(tk.END, "\n=== Conclusion ===\n")
        
        if (self.comparison_results['comparison']['sample_rate_ratio'] > 1.5 and 
            self.comparison_results['comparison'].get('high_frequency_ratio', 0) > 1.2):
            self.results_text.insert(tk.END, "The upscaling process has significantly improved the audio quality by increasing the sample rate and enhancing high-frequency content. This should result in better clarity and detail.\n")
        elif (self.comparison_results['comparison']['sample_rate_ratio'] > 1.0 and 
              self.comparison_results['comparison'].get('high_frequency_ratio', 0) > 1.0):
            self.results_text.insert(tk.END, "The upscaling process has moderately improved the audio quality. There is some enhancement in high-frequency content which may improve clarity.\n")
        else:
            self.results_text.insert(tk.END, "The upscaling process has made minimal changes to the audio quality. The differences between the original and upscaled files are subtle.\n")
        
        # Add report info
        if self.report_path:
            self.results_text.insert(tk.END, f"\nDetailed report generated: {self.report_path}\n")
            self.results_text.insert(tk.END, "Click 'View Report' to open it in your browser.\n")
        
        self.status_var.set("Comparison complete")
    
    def _show_error(self, error_message):
        """Show error message"""
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"Error: {error_message}")
        self.status_var.set(f"Error: {error_message}")
        self.compare_button.config(state=tk.NORMAL)
    
    def _view_report(self):
        """View the generated report"""
        if self.report_path and os.path.isfile(self.report_path):
            # Open in default browser
            webbrowser.open(f"file://{os.path.abspath(self.report_path)}")
        else:
            messagebox.showerror("Error", "Report file not found")

def main():
    """Main function to run the application"""
    app = FileComparisonTool()
    app.mainloop()

if __name__ == "__main__":
    main()
