@echo on
echo Running Standard Audio Upscaler Test

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Run the test script with only standard processing
echo Running test script...
"%PYTHON_PATH%\python.exe" test_audio_upscaler.py --skip-win11 --skip-amd --skip-mojo

echo.
echo Test completed.
echo Check the logs directory for detailed test results.
pause
