"""
<PERSON><PERSON><PERSON> to run the Audio Upscaler GUI
"""

import os
import sys
import subprocess
import importlib.util

def check_package(package_name):
    """Check if a package is installed"""
    return importlib.util.find_spec(package_name) is not None

def install_package(package_name):
    """Install a package using pip"""
    print(f"Installing {package_name}...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])

def main():
    # Check required packages
    required_packages = ["numpy", "scipy", "soundfile", "tkinter"]
    
    for package in required_packages:
        if not check_package(package):
            try:
                install_package(package)
            except Exception as e:
                print(f"Error installing {package}: {e}")
                if package != "tkinter":  # tkinter comes with Python, can't be installed via pip
                    return 1
    
    # Change to AudioUpscaler directory
    try:
        os.chdir("AudioUpscaler")
    except FileNotFoundError:
        print("Error: AudioUpscaler directory not found")
        print("Make sure you're running this script from the correct directory")
        return 1
    
    # Add current directory to path
    sys.path.append(os.getcwd())
    
    # Run the GUI
    try:
        from gui.main_gui import main
        main()
    except ImportError as e:
        print(f"Error importing main_gui: {e}")
        print("Make sure the AudioUpscaler package is properly installed")
        return 1
    except Exception as e:
        print(f"Error running GUI: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
