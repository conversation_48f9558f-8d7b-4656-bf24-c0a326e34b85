"""
Process Manager Utility
Manages processes related to audio upscaling
"""

import os
import sys
import platform
import subprocess
import signal
from typing import List, Dict, Any, Optional

def get_running_processes() -> List[Dict[str, Any]]:
    """
    Get a list of running processes related to audio upscaling
    
    Returns:
        List of process dictionaries with pid, name, and command
    """
    processes = []
    
    try:
        import psutil
        
        # Get all Python processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_info = proc.info
                cmdline = proc_info.get('cmdline', [])
                
                # Check if this is an audio upscaler process
                if cmdline and any(x in ' '.join(cmdline) for x in ['audio_upscaler', 'realtime_audio']):
                    processes.append({
                        'pid': proc_info['pid'],
                        'name': proc_info['name'],
                        'command': ' '.join(cmdline) if cmdline else ''
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except ImportError:
        print("psutil not available, using basic process detection")
        
        # Basic process detection (Windows only)
        if platform.system() == "Windows":
            try:
                output = subprocess.check_output(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV']).decode()
                for line in output.splitlines()[1:]:  # Skip header
                    if 'python.exe' in line:
                        parts = line.strip('"').split('","')
                        if len(parts) >= 2:
                            processes.append({
                                'pid': int(parts[1]),
                                'name': parts[0],
                                'command': 'Unknown'
                            })
            except subprocess.SubprocessError:
                pass
    
    return processes

def kill_process(pid: int) -> bool:
    """
    Kill a process by its PID
    
    Args:
        pid: Process ID to kill
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if platform.system() == "Windows":
            subprocess.run(['taskkill', '/F', '/PID', str(pid)])
        else:
            os.kill(pid, signal.SIGTERM)
        return True
    except Exception as e:
        print(f"Error killing process {pid}: {e}")
        return False

def kill_all_upscaler_processes() -> int:
    """
    Kill all processes related to audio upscaling
    
    Returns:
        Number of processes killed
    """
    processes = get_running_processes()
    killed_count = 0
    
    for proc in processes:
        if kill_process(proc['pid']):
            print(f"Killed process: {proc['name']} (PID: {proc['pid']})")
            killed_count += 1
    
    return killed_count

def is_realtime_upscaler_running() -> bool:
    """
    Check if the real-time audio upscaler is running
    
    Returns:
        True if running, False otherwise
    """
    processes = get_running_processes()
    
    for proc in processes:
        if 'realtime_audio' in proc.get('command', ''):
            return True
    
    return False

def start_realtime_upscaler() -> bool:
    """
    Start the real-time audio upscaler
    
    Returns:
        True if successful, False otherwise
    """
    if is_realtime_upscaler_running():
        print("Real-time audio upscaler is already running")
        return True
    
    try:
        # Get the path to the realtime_audio_upscaler.py script
        script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        script_path = os.path.join(script_dir, 'gui', 'realtime_audio_upscaler.py')
        
        # Start the process
        if platform.system() == "Windows":
            subprocess.Popen([sys.executable, script_path], 
                            creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            subprocess.Popen([sys.executable, script_path], 
                            stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("Started real-time audio upscaler")
        return True
    except Exception as e:
        print(f"Error starting real-time audio upscaler: {e}")
        return False

def stop_realtime_upscaler() -> bool:
    """
    Stop the real-time audio upscaler
    
    Returns:
        True if successful, False otherwise
    """
    processes = get_running_processes()
    stopped = False
    
    for proc in processes:
        if 'realtime_audio' in proc.get('command', ''):
            if kill_process(proc['pid']):
                print(f"Stopped real-time audio upscaler (PID: {proc['pid']})")
                stopped = True
    
    if not stopped:
        print("Real-time audio upscaler is not running")
    
    return stopped
