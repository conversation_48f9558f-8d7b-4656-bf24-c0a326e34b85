const axios = require('axios');
const config = require('../config');

class GroqService {
  constructor() {
    this.baseUrl = 'https://api.groq.com/openai/v1';
    this.currentAccountIndex = 0;
    this.currentModelIndex = 0;
    this.accounts = config.groq.accounts;
    this.models = config.groq.models;
    this.apiKey = this.accounts[this.currentAccountIndex].apiKey;
    this.modelId = this.models[this.currentModelIndex].id;
    this.requestCount = 0;
    this.maxRequestsPerAccount = 100; // Adjust based on Groq's free tier limits
  }

  /**
   * Get headers for API requests
   * @returns {Object} - Headers object
   */
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Switch to the next account in rotation
   */
  switchAccount() {
    this.currentAccountIndex = (this.currentAccountIndex + 1) % this.accounts.length;
    this.apiKey = this.accounts[this.currentAccountIndex].apiKey;
    this.requestCount = 0;
    console.log(`Switched to Groq account: ${this.accounts[this.currentAccountIndex].name}`);
  }

  /**
   * Switch to the next model in rotation
   */
  switchModel() {
    this.currentModelIndex = (this.currentModelIndex + 1) % this.models.length;
    this.modelId = this.models[this.currentModelIndex].id;
    console.log(`Switched to Groq model: ${this.models[this.currentModelIndex].name}`);
  }

  /**
   * Switch to a specific model by name
   * @param {string} modelName - Name of the model to switch to
   */
  switchToModel(modelName) {
    const modelIndex = this.models.findIndex(model => model.name === modelName);
    if (modelIndex !== -1) {
      this.currentModelIndex = modelIndex;
      this.modelId = this.models[this.currentModelIndex].id;
      console.log(`Switched to Groq model: ${modelName}`);
    } else {
      console.error(`Model ${modelName} not found, using current model`);
    }
  }

  /**
   * Check if we need to switch accounts based on request count
   */
  checkAndSwitchAccount() {
    this.requestCount++;
    if (this.requestCount >= this.maxRequestsPerAccount) {
      this.switchAccount();
    }
  }

  /**
   * Generate a solution for a coding problem
   * @param {string} problemText - The coding problem text
   * @returns {Promise<Object>} - Generated solution
   */
  async generateSolution(problemText) {
    try {
      this.checkAndSwitchAccount();
      
      const prompt = `
You are an expert coding interview assistant. Analyze the following problem and provide a detailed solution.

PROBLEM:
${problemText}

Provide your response in the following JSON format:
{
  "analysis": "Brief analysis of the problem, identifying key concepts and patterns",
  "approach": "Step-by-step approach to solve the problem",
  "code": "Well-commented code solution",
  "time_complexity": "Analysis of time complexity",
  "space_complexity": "Analysis of space complexity"
}

Make sure your response is valid JSON.
`;

      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: this.modelId,
          messages: [
            { role: "system", content: "You are an expert coding assistant that helps with technical interviews." },
            { role: "user", content: prompt }
          ],
          temperature: 0.3,
          max_tokens: 2048,
          response_format: { type: "json_object" }
        },
        { headers: this.getHeaders() }
      );

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content;
        try {
          return JSON.parse(content);
        } catch (e) {
          console.error('Error parsing JSON response:', e);
          // Try to extract JSON from the response using regex
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
          }
          throw new Error('Failed to parse solution JSON');
        }
      } else {
        throw new Error('Invalid response format from Groq API');
      }
    } catch (error) {
      console.error('Groq API Error:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        
        // Handle rate limiting or quota exceeded
        if (error.response.status === 429) {
          console.log('Rate limit exceeded, switching account and retrying...');
          this.switchAccount();
          return this.generateSolution(problemText);
        }
      }
      throw error;
    }
  }

  /**
   * Get available models from Groq API
   * @returns {Promise<Array>} - List of available models
   */
  async listModels() {
    try {
      const response = await axios.get(
        `${this.baseUrl}/models`,
        { headers: this.getHeaders() }
      );
      
      return response.data.data;
    } catch (error) {
      console.error('Error listing Groq models:', error.message);
      throw error;
    }
  }
}

module.exports = GroqService;
