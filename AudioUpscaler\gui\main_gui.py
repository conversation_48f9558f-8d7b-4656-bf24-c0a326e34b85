"""
Main GUI for Audio Upscaler
Provides a unified interface for real-time and batch audio upscaling
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import time
from typing import List, Dict, Any, Optional

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import core modules
from core.audio_processor import AudioProcessor
from core.file_utils import list_audio_files, get_audio_info, play_audio_file, open_directory, create_output_path
from utils.process_manager import is_realtime_upscaler_running, start_realtime_upscaler, stop_realtime_upscaler

class AudioUpscalerGUI(tk.Tk):
    """Main GUI for Audio Upscaler"""
    
    def __init__(self):
        super().__init__()
        
        # Set up the main window
        self.title("Audio Upscaler")
        self.geometry("900x700")
        self.minsize(800, 600)
        
        # Initialize variables
        self.use_win11_opt = tk.BooleanVar(value=True)
        self.use_hardware_accel = tk.BooleanVar(value=True)
        self.upscale_factor = tk.IntVar(value=2)
        self.quality_level = tk.IntVar(value=2)  # 1=Low, 2=Medium, 3=High
        self.batch_files = []
        self.processing_queue = queue.Queue()
        self.is_processing = False
        self.realtime_running = is_realtime_upscaler_running()
        
        # Create the main frame
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create the notebook (tabbed interface)
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        self.realtime_tab = ttk.Frame(self.notebook)
        self.batch_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.realtime_tab, text="Real-time Upscaling")
        self.notebook.add(self.batch_tab, text="Batch Processing")
        
        # Create the content for each tab
        self._create_realtime_tab()
        self._create_batch_tab()
        
        # Create status bar
        self.status_bar = ttk.Frame(self.main_frame)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=(5, 0))
        
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(self.status_bar, textvariable=self.status_var, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT)
        
        # Create progress bar
        self.progress = ttk.Progressbar(self.status_bar, mode='determinate', length=200)
        self.progress.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Initialize audio processor
        self.audio_processor = AudioProcessor(
            use_win11_opt=self.use_win11_opt.get(),
            use_hardware_accel=self.use_hardware_accel.get()
        )
        
        # Set up periodic check for realtime upscaler status
        self._check_realtime_status()
    
    def _create_realtime_tab(self):
        """Create the real-time upscaling tab"""
        # Create frame for controls
        control_frame = ttk.LabelFrame(self.realtime_tab, text="Real-time Audio Upscaling")
        control_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Add description
        description = ttk.Label(
            control_frame, 
            text="Real-time audio upscaling processes your system audio on-the-fly.\n"
                 "This allows you to enhance audio from any application without saving files.",
            wraplength=600
        )
        description.pack(pady=10)
        
        # Add settings frame
        settings_frame = ttk.Frame(control_frame)
        settings_frame.pack(fill=tk.X, pady=10)
        
        # Add settings
        ttk.Label(settings_frame, text="Upscale Factor:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(settings_frame, text="2x", variable=self.upscale_factor, value=2).grid(row=0, column=1, padx=5, pady=5)
        ttk.Radiobutton(settings_frame, text="4x", variable=self.upscale_factor, value=4).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Quality Level:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(settings_frame, text="Low (Better Performance)", variable=self.quality_level, value=1).grid(row=1, column=1, padx=5, pady=5)
        ttk.Radiobutton(settings_frame, text="Medium", variable=self.quality_level, value=2).grid(row=1, column=2, padx=5, pady=5)
        ttk.Radiobutton(settings_frame, text="High (Better Quality)", variable=self.quality_level, value=3).grid(row=1, column=3, padx=5, pady=5)
        
        ttk.Checkbutton(settings_frame, text="Use Windows 11 Optimizations", variable=self.use_win11_opt).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        ttk.Checkbutton(settings_frame, text="Use Hardware Acceleration", variable=self.use_hardware_accel).grid(row=2, column=2, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Add start/stop button
        self.realtime_button = ttk.Button(control_frame, text="Start Real-time Upscaling", command=self._toggle_realtime)
        self.realtime_button.pack(pady=20)
        
        # Add status indicator
        self.realtime_status_frame = ttk.Frame(control_frame)
        self.realtime_status_frame.pack(fill=tk.X, pady=10)
        
        self.realtime_status_label = ttk.Label(self.realtime_status_frame, text="Status: Not Running")
        self.realtime_status_label.pack(side=tk.LEFT, padx=5)
        
        self.realtime_indicator = tk.Canvas(self.realtime_status_frame, width=20, height=20, bg=self.cget('bg'), highlightthickness=0)
        self.realtime_indicator.pack(side=tk.LEFT)
        self.realtime_indicator.create_oval(5, 5, 15, 15, fill="red", tags="indicator")
        
        # Add instructions
        instructions_frame = ttk.LabelFrame(control_frame, text="Instructions")
        instructions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        instructions_text = tk.Text(instructions_frame, wrap=tk.WORD, height=8, width=60)
        instructions_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        instructions_text.insert(tk.END, 
            "1. Select your desired upscale factor and quality level.\n"
            "2. Click 'Start Real-time Upscaling' to begin processing.\n"
            "3. All system audio will be processed in real-time.\n"
            "4. You may notice a slight delay in audio playback.\n"
            "5. Higher quality settings require more processing power.\n"
            "6. Click 'Stop Real-time Upscaling' to return to normal audio.\n\n"
            "Note: Real-time processing continues even when you switch to the Batch tab."
        )
        instructions_text.config(state=tk.DISABLED)
    
    def _create_batch_tab(self):
        """Create the batch processing tab"""
        # Create frame for file list
        file_frame = ttk.LabelFrame(self.batch_tab, text="Audio Files")
        file_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Add file list
        self.file_list = tk.Listbox(file_frame, selectmode=tk.EXTENDED, height=10)
        self.file_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(file_frame, orient=tk.VERTICAL, command=self.file_list.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.file_list.config(yscrollcommand=scrollbar.set)
        
        # Add buttons for file operations
        file_button_frame = ttk.Frame(self.batch_tab)
        file_button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(file_button_frame, text="Add Files", command=self._add_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_button_frame, text="Add Folder", command=self._add_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_button_frame, text="Remove Selected", command=self._remove_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_button_frame, text="Clear All", command=self._clear_files).pack(side=tk.LEFT, padx=5)
        
        # Add settings frame
        settings_frame = ttk.LabelFrame(self.batch_tab, text="Processing Settings")
        settings_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Add settings
        settings_grid = ttk.Frame(settings_frame)
        settings_grid.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(settings_grid, text="Upscale Factor:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="2x", variable=self.upscale_factor, value=2).grid(row=0, column=1, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="4x", variable=self.upscale_factor, value=4).grid(row=0, column=2, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="8x", variable=self.upscale_factor, value=8).grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Label(settings_grid, text="Quality Level:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="Low (Faster)", variable=self.quality_level, value=1).grid(row=1, column=1, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="Medium", variable=self.quality_level, value=2).grid(row=1, column=2, padx=5, pady=5)
        ttk.Radiobutton(settings_grid, text="High (Better Quality)", variable=self.quality_level, value=3).grid(row=1, column=3, padx=5, pady=5)
        
        ttk.Checkbutton(settings_grid, text="Use Windows 11 Optimizations", variable=self.use_win11_opt).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        ttk.Checkbutton(settings_grid, text="Use Hardware Acceleration", variable=self.use_hardware_accel).grid(row=2, column=2, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Add output directory selection
        output_frame = ttk.Frame(settings_frame)
        output_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(output_frame, text="Output Directory:").pack(side=tk.LEFT, padx=5)
        self.output_var = tk.StringVar(value="")
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=40)
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(output_frame, text="Browse", command=self._browse_output).pack(side=tk.LEFT, padx=5)
        
        # Add process button
        process_frame = ttk.Frame(self.batch_tab)
        process_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.process_button = ttk.Button(process_frame, text="Process Files", command=self._process_files)
        self.process_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(process_frame, text="Stop Processing", command=self._stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Add progress information
        self.progress_frame = ttk.LabelFrame(self.batch_tab, text="Processing Progress")
        self.progress_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.progress_label = ttk.Label(self.progress_frame, text="No files in queue")
        self.progress_label.pack(padx=10, pady=5)
        
        self.file_progress = ttk.Progressbar(self.progress_frame, mode='determinate', length=100)
        self.file_progress.pack(fill=tk.X, padx=10, pady=5)
        
        self.overall_progress_label = ttk.Label(self.progress_frame, text="Overall Progress: 0%")
        self.overall_progress_label.pack(padx=10, pady=5)
        
        self.overall_progress = ttk.Progressbar(self.progress_frame, mode='determinate', length=100)
        self.overall_progress.pack(fill=tk.X, padx=10, pady=5)
    
    def _check_realtime_status(self):
        """Check if real-time upscaler is running and update UI"""
        running = is_realtime_upscaler_running()
        
        if running != self.realtime_running:
            self.realtime_running = running
            self._update_realtime_ui()
        
        # Schedule next check
        self.after(2000, self._check_realtime_status)
    
    def _update_realtime_ui(self):
        """Update the real-time UI based on current status"""
        if self.realtime_running:
            self.realtime_button.config(text="Stop Real-time Upscaling")
            self.realtime_status_label.config(text="Status: Running")
            self.realtime_indicator.itemconfig("indicator", fill="green")
        else:
            self.realtime_button.config(text="Start Real-time Upscaling")
            self.realtime_status_label.config(text="Status: Not Running")
            self.realtime_indicator.itemconfig("indicator", fill="red")
    
    def _toggle_realtime(self):
        """Toggle real-time audio upscaling"""
        if self.realtime_running:
            # Stop real-time upscaling
            if stop_realtime_upscaler():
                self.status_var.set("Real-time upscaling stopped")
                self.realtime_running = False
                self._update_realtime_ui()
            else:
                messagebox.showerror("Error", "Failed to stop real-time upscaling")
        else:
            # Start real-time upscaling
            if start_realtime_upscaler():
                self.status_var.set("Real-time upscaling started")
                self.realtime_running = True
                self._update_realtime_ui()
            else:
                messagebox.showerror("Error", "Failed to start real-time upscaling")
    
    def _add_files(self):
        """Add files to the batch processing list"""
        files = filedialog.askopenfilenames(
            title="Select Audio Files",
            filetypes=[
                ("Audio Files", "*.wav *.mp3 *.flac *.ogg *.m4a *.aac"),
                ("All Files", "*.*")
            ]
        )
        
        if files:
            for file in files:
                if file not in self.batch_files:
                    self.batch_files.append(file)
                    self.file_list.insert(tk.END, os.path.basename(file))
            
            self.status_var.set(f"Added {len(files)} files to the queue")
    
    def _add_folder(self):
        """Add all audio files from a folder"""
        folder = filedialog.askdirectory(title="Select Folder with Audio Files")
        
        if folder:
            audio_files = list_audio_files(folder)
            
            if audio_files:
                for file in audio_files:
                    if file not in self.batch_files:
                        self.batch_files.append(file)
                        self.file_list.insert(tk.END, os.path.basename(file))
                
                self.status_var.set(f"Added {len(audio_files)} files from folder")
            else:
                messagebox.showinfo("No Audio Files", "No audio files found in the selected folder")
    
    def _remove_selected(self):
        """Remove selected files from the list"""
        selected = self.file_list.curselection()
        
        if selected:
            # Remove in reverse order to avoid index shifting
            for index in sorted(selected, reverse=True):
                del self.batch_files[index]
                self.file_list.delete(index)
            
            self.status_var.set(f"Removed {len(selected)} files from the queue")
    
    def _clear_files(self):
        """Clear all files from the list"""
        self.batch_files = []
        self.file_list.delete(0, tk.END)
        self.status_var.set("Cleared all files from the queue")
    
    def _browse_output(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        
        if directory:
            self.output_var.set(directory)
    
    def _process_files(self):
        """Process all files in the batch queue"""
        if not self.batch_files:
            messagebox.showinfo("No Files", "No files in the queue to process")
            return
        
        if self.is_processing:
            messagebox.showinfo("Already Processing", "Batch processing is already running")
            return
        
        # Update UI
        self.is_processing = True
        self.process_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # Get settings
        output_dir = self.output_var.get()
        if not output_dir:
            # Use default output directory
            output_dir = None
        
        # Update audio processor settings
        self.audio_processor = AudioProcessor(
            use_win11_opt=self.use_win11_opt.get(),
            use_hardware_accel=self.use_hardware_accel.get()
        )
        
        # Start processing thread
        threading.Thread(target=self._process_files_thread, 
                        args=(self.batch_files, output_dir, 
                              self.upscale_factor.get(), 
                              self.quality_level.get()),
                        daemon=True).start()
    
    def _process_files_thread(self, files, output_dir, upscale_factor, quality_level):
        """Thread for batch processing files"""
        total_files = len(files)
        processed_files = 0
        
        for file in files:
            if not self.is_processing:
                break
            
            # Update progress
            self._update_progress(file, processed_files, total_files)
            
            try:
                # Process file
                output_file = self.audio_processor.process_file(
                    file, 
                    output_file=create_output_path(file, output_dir, "upscaled"),
                    upscale_factor=upscale_factor,
                    quality_level=quality_level
                )
                
                if output_file:
                    # File processed successfully
                    processed_files += 1
                    self._update_progress(f"Processed: {os.path.basename(file)}", 
                                         processed_files, total_files)
                else:
                    # Processing failed
                    self._update_progress(f"Failed: {os.path.basename(file)}", 
                                         processed_files, total_files)
            
            except Exception as e:
                self._update_progress(f"Error processing {os.path.basename(file)}: {str(e)}", 
                                     processed_files, total_files)
        
        # Processing complete
        if self.is_processing:
            self._update_progress("Processing complete", total_files, total_files)
            
            # Show completion message
            if processed_files > 0:
                if output_dir:
                    self.after(100, lambda: messagebox.showinfo(
                        "Processing Complete", 
                        f"Processed {processed_files} of {total_files} files.\n\n"
                        f"Files saved to: {output_dir}"
                    ))
                    
                    # Open output directory
                    self.after(200, lambda: open_directory(output_dir))
                else:
                    self.after(100, lambda: messagebox.showinfo(
                        "Processing Complete", 
                        f"Processed {processed_files} of {total_files} files."
                    ))
        else:
            self._update_progress("Processing stopped", processed_files, total_files)
        
        # Reset UI
        self.after(100, self._reset_processing_ui)
    
    def _update_progress(self, current_file, processed, total):
        """Update progress UI from the processing thread"""
        def update():
            self.progress_label.config(text=current_file)
            self.file_progress["value"] = 100
            
            overall_percent = int(processed / total * 100) if total > 0 else 0
            self.overall_progress_label.config(text=f"Overall Progress: {overall_percent}%")
            self.overall_progress["value"] = overall_percent
            
            self.status_var.set(f"Processing file {processed+1} of {total}")
        
        self.after(0, update)
    
    def _reset_processing_ui(self):
        """Reset the processing UI after completion"""
        self.is_processing = False
        self.process_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_label.config(text="No files in queue")
        self.file_progress["value"] = 0
        self.overall_progress_label.config(text="Overall Progress: 0%")
        self.overall_progress["value"] = 0
        self.status_var.set("Ready")
    
    def _stop_processing(self):
        """Stop batch processing"""
        if self.is_processing:
            self.is_processing = False
            self.status_var.set("Stopping processing...")
            self.stop_button.config(state=tk.DISABLED)

def main():
    """Main function to run the application"""
    app = AudioUpscalerGUI()
    app.mainloop()

if __name__ == "__main__":
    main()
