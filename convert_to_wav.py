"""
Convert audio file to WAV format
"""

import os
import sys
import argparse
import subprocess
import tempfile

def convert_to_wav(input_file, output_file=None):
    """Convert audio file to WAV format using ffmpeg"""
    if not os.path.isfile(input_file):
        print(f"Error: Input file not found: {input_file}")
        return None
    
    # Generate output filename if not provided
    if output_file is None:
        input_dir = os.path.dirname(input_file)
        input_basename = os.path.basename(input_file)
        input_name, _ = os.path.splitext(input_basename)
        output_dir = os.path.join(input_dir, "converted")
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, f"{input_name}.wav")
    
    try:
        # Check if ffmpeg is available
        subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
    except (subprocess.SubprocessError, FileNotFoundError):
        print("Error: ffmpeg not found. Please install ffmpeg.")
        return None
    
    print(f"Converting {input_file} to WAV format...")
    print(f"Output: {output_file}")
    
    try:
        # Run ffmpeg to convert the file
        subprocess.run([
            "ffmpeg", 
            "-i", input_file,
            "-acodec", "pcm_s16le",  # 16-bit PCM
            "-ar", "44100",          # 44.1 kHz sample rate
            "-y",                    # Overwrite output file if it exists
            output_file
        ], check=True)
        
        print("Conversion completed successfully.")
        return output_file
    except subprocess.SubprocessError as e:
        print(f"Error converting file: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Convert audio file to WAV format")
    parser.add_argument("input_file", help="Path to input audio file")
    parser.add_argument("--output", help="Path to output WAV file (optional)")
    
    args = parser.parse_args()
    
    output_file = convert_to_wav(args.input_file, args.output)
    
    if output_file is None:
        print("Conversion failed.")
        return 1
    
    print(f"Successfully converted to: {output_file}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
