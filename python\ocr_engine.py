import sys
import cv2
import pytesseract
import numpy as np
import json

class OCREngine:
    def __init__(self):
        # Configure Tesseract path if needed
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        self.clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    
    def preprocess_image(self, image_path):
        """Preprocess image for better OCR results"""
        # Read image
        img = cv2.imread(image_path)
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        enhanced = self.clahe.apply(gray)
        
        # Binarization with Otsu's method
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Denoise with morphological operations
        kernel = np.ones((1,1), np.uint8)
        denoised = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        return denoised
    
    def extract_text(self, image_path):
        """Extract text from image using OCR"""
        # Preprocess image
        processed_img = self.preprocess_image(image_path)
        
        # Perform OCR
        custom_config = r'--oem 3 --psm 6 -l eng'
        text = pytesseract.image_to_string(processed_img, config=custom_config)
        
        return text

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python ocr_engine.py <image_path>")
        sys.exit(1)
    
    image_path = sys.argv[1]
    ocr = OCREngine()
    
    try:
        extracted_text = ocr.extract_text(image_path)
        print(extracted_text)
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)
