@echo on
echo Checking Python environment

REM Set Python paths
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Run the script
echo Running script...
"%PYTHON_PATH%\python.exe" check_env.py > env_info.txt

echo.
echo Environment information saved to env_info.txt
type env_info.txt
pause
