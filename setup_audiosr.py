import os
import sys
import subprocess
import platform
import shutil
import tempfile
import argparse
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info.major != 3 or sys.version_info.minor > 11:
        print(f"Warning: This application is tested with Python 3.9-3.11.")
        print(f"Your Python version is {sys.version_info.major}.{sys.version_info.minor}")
        if sys.version_info.minor > 11:
            print("Python 3.12+ is not currently supported by some dependencies.")
            return False
    return True

def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    
    # Install tkinterdnd2 for drag and drop functionality
    subprocess.check_call([sys.executable, "-m", "pip", "install", "tkinterdnd2"])
    
    # Install PyInstaller for creating the executable
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # Install other dependencies from requirements.txt with modifications for compatibility
    with tempfile.NamedTemporaryFile('w', delete=False, suffix='.txt') as temp_req:
        # Modify requirements for compatibility
        with open('requirements.txt', 'r') as original_req:
            for line in original_req:
                # Skip the audiosr line since we're installing from the local repo
                if line.strip() == 'audiosr':
                    continue
                # Fix torch version for compatibility
                if line.startswith('torch==') or line.startswith('torchvision==') or line.startswith('torchaudio=='):
                    # Use CPU-only version for simplicity in the executable
                    if 'sys_platform' in line:
                        parts = line.split(';')
                        if "darwin" in parts[1]:  # Use the macOS version as it's CPU-only
                            temp_req.write(parts[0] + '\n')
                        else:
                            continue
                    else:
                        temp_req.write(line)
                else:
                    temp_req.write(line)
        
        temp_req.flush()
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", temp_req.name])
    
    # Clean up temporary file
    os.unlink(temp_req.name)
    
    # Install the local audiosr package
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-e", "."])
    
    print("Dependencies installed successfully.")

def fix_mono_output():
    """Fix the mono output issue in the AudioSR code."""
    print("Fixing mono output issue...")
    
    # Path to the inference.py file
    inference_path = Path("audiosr/inference.py")
    
    if not inference_path.exists():
        print(f"Error: {inference_path} not found. Make sure you're in the repository root.")
        return False
    
    # Read the file
    with open(inference_path, 'r') as f:
        content = f.read()
    
    # Check if the file already has the fix
    if "# Preserve stereo if input is stereo" in content:
        print("Mono output fix already applied.")
        return True
    
    # Find the line where the output is returned and modify it to preserve stereo
    if "return waveform[0]" in content:
        modified_content = content.replace(
            "return waveform[0]",
            "# Preserve stereo if input is stereo\n    if is_stereo and len(waveform) > 1:\n        return np.stack([waveform[0], waveform[1]], axis=1)\n    return waveform[0]"
        )
        
        # Write the modified content back
        with open(inference_path, 'w') as f:
            f.write(modified_content)
        
        print("Mono output issue fixed.")
        return True
    else:
        print("Could not find the line to modify. Manual fix required.")
        return False

def create_executable():
    """Create an executable using PyInstaller."""
    print("Creating executable...")
    
    # Create a PyInstaller spec file
    spec_content = """# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

block_cipher = None

# Add the audiosr package
audiosr_path = Path('audiosr').resolve()
model_path = Path('audiosr/models').resolve()

a = Analysis(
    ['audiosr_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        (str(audiosr_path), 'audiosr'),
        (str(model_path), 'audiosr/models')
    ],
    hiddenimports=[
        'torch',
        'torchaudio',
        'torchvision',
        'diffusers',
        'transformers',
        'librosa',
        'soundfile',
        'numpy',
        'scipy',
        'tkinterdnd2'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='AudioSR',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='AudioSR',
)
"""
    
    # Write the spec file
    with open('audiosr.spec', 'w') as f:
        f.write(spec_content)
    
    # Create a simple icon for the application
    create_icon()
    
    # Run PyInstaller
    subprocess.check_call([sys.executable, "-m", "PyInstaller", "audiosr.spec", "--clean"])
    
    print("Executable created successfully. Check the 'dist/AudioSR' directory.")

def create_icon():
    """Create a simple icon for the application."""
    try:
        from PIL import Image, ImageDraw
        
        # Create a 256x256 image with a blue background
        img = Image.new('RGB', (256, 256), color=(53, 59, 72))
        draw = ImageDraw.Draw(img)
        
        # Draw a simple waveform
        for i in range(20, 236, 4):
            amplitude = 30 + 50 * abs(((i - 128) / 100) ** 3)
            draw.line([(i, 128 - amplitude), (i, 128 + amplitude)], fill=(46, 204, 113), width=3)
        
        # Save the icon
        img.save('icon.ico')
        print("Icon created successfully.")
    except ImportError:
        print("PIL not installed. Skipping icon creation.")
    except Exception as e:
        print(f"Error creating icon: {e}")

def main():
    parser = argparse.ArgumentParser(description="Setup AudioSR with drag-and-drop UI and create executable")
    parser.add_argument('--skip-deps', action='store_true', help="Skip installing dependencies")
    parser.add_argument('--skip-fix', action='store_true', help="Skip fixing mono output issue")
    parser.add_argument('--skip-exe', action='store_true', help="Skip creating executable")
    
    args = parser.parse_args()
    
    # Check Python version
    if not check_python_version():
        if input("Continue anyway? (y/n): ").lower() != 'y':
            sys.exit(1)
    
    # Install dependencies
    if not args.skip_deps:
        install_dependencies()
    
    # Fix mono output issue
    if not args.skip_fix:
        fix_mono_output()
    
    # Create executable
    if not args.skip_exe:
        create_executable()
    
    print("Setup complete!")

if __name__ == "__main__":
    main()
