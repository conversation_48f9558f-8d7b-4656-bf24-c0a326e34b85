@echo on
echo Installing required packages for Audio Upscaler

REM Set Python path
set "PYTHON_PATH=C:\Python312"
set "PYTHON_SCRIPTS=C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PYTHON_SCRIPTS%;%PATH%"

REM Install required packages
echo Installing numpy...
"%PYTHON_PATH%\python.exe" -m pip install numpy

echo Installing scipy...
"%PYTHON_PATH%\python.exe" -m pip install scipy

echo Installing soundfile...
"%PYTHON_PATH%\python.exe" -m pip install soundfile

echo Installing matplotlib...
"%PYTHON_PATH%\python.exe" -m pip install matplotlib

echo.
echo Installation complete!
echo.
echo You can now run the Audio Upscaler by double-clicking run_upscaler.bat in the AudioUpscaler folder.
echo.
pause
