"""
Process the RAVE.flac file with GPU acceleration and Sony-inspired techniques
"""

import os
import sys
import time
import logging
import argparse
import platform
import numpy as np
import soundfile as sf
from typing import Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("rave_processing.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def find_rave_file():
    """Find the RAVE.flac file in the Downloads folder"""
    downloads_dir = os.path.join(os.path.expanduser("~"), "Downloads")

    # Look for files containing "RAVE" and ending with ".flac"
    for file in os.listdir(downloads_dir):
        if "RAVE" in file and file.endswith(".flac"):
            return os.path.join(downloads_dir, file)

    return None

def process_rave_file(input_file: Optional[str] = None,
                     output_dir: Optional[str] = None,
                     quality_level: int = 2,
                     upscale_factor: int = 2,
                     use_gpu: bool = True,
                     use_win11_opt: bool = True,
                     reduce_load: bool = True,
                     use_ai_model: bool = True,
                     ai_model_type: str = "dynamic",
                     play_result: bool = True):
    """
    Process the RAVE.flac file with GPU acceleration

    Args:
        input_file: Path to input file (if None, will search for RAVE.flac)
        output_dir: Output directory (if None, will use 'sony_upscaled' in same dir as input)
        quality_level: Quality level (1=Low, 2=Medium, 3=High)
        upscale_factor: Factor to upscale the sample rate
        use_gpu: Whether to use GPU acceleration
        use_win11_opt: Whether to use Windows 11 optimizations
        reduce_load: Whether to reduce GPU/CPU load for better quality
        play_result: Whether to play the result after processing

    Returns:
        Path to the processed file
    """
    # Find input file if not provided
    if input_file is None:
        input_file = find_rave_file()
        if input_file is None:
            logger.error("RAVE.flac file not found in Downloads folder")
            return None

    # Check if input file exists
    if not os.path.isfile(input_file):
        logger.error(f"Input file not found: {input_file}")
        return None

    logger.info(f"Processing file: {input_file}")

    # Create output directory if not provided
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(input_file), "sony_upscaled")

    os.makedirs(output_dir, exist_ok=True)

    # Create output path
    output_file = os.path.join(output_dir, "RAVE_sony_upscaled.wav")
    logger.info(f"Output will be saved to: {output_file}")

    # Log system information
    logger.info("System Information:")
    logger.info(f"  Platform: {platform.platform()}")
    logger.info(f"  Processor: {platform.processor()}")
    logger.info(f"  Python version: {platform.python_version()}")

    # Check for GPU
    gpu_available = False
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        if gpu_available:
            logger.info(f"  GPU: {torch.cuda.get_device_name(0)}")
            logger.info(f"  CUDA version: {torch.version.cuda}")
            logger.info(f"  PyTorch version: {torch.__version__}")
        else:
            logger.info("  GPU: Not available")
    except ImportError:
        logger.warning("PyTorch not installed, GPU acceleration not available")

    # Check for AI model support
    ai_model_available = False
    try:
        import torch.nn as nn
        ai_model_available = True
        logger.info("  AI model support: Available")
    except ImportError:
        logger.warning("PyTorch neural network modules not available, AI model processing disabled")
        use_ai_model = False

    # Use GPU processor if available and requested
    if use_gpu and gpu_available:
        try:
            from gpu_audio_processor import GPUAudioProcessor

            logger.info("Using GPU Audio Processor")
            processor = GPUAudioProcessor(
                device="auto",
                use_win11_opt=use_win11_opt,
                quality_level=quality_level,
                reduce_load=reduce_load
            )

            # Process file
            output_file = processor.process_file(
                input_file,
                output_file=output_file,
                upscale_factor=upscale_factor
            )

            # Get natural version path
            natural_output = os.path.join(output_dir, "RAVE_sony_natural.wav")

            # Play result if requested
            if play_result:
                logger.info("Playing processed file...")
                if platform.system() == "Windows":
                    os.startfile(natural_output)
                    logger.info("Natural version file opened in default media player")

                    logger.info(f"\nTwo versions have been created:")
                    logger.info(f"1. Natural version (44.1kHz): {natural_output}")
                    logger.info(f"2. High-resolution version ({44100 * upscale_factor}Hz): {output_file}")
                    logger.info(f"\nThe natural version is playing now. To play the high-resolution version, open:")
                    logger.info(f"{output_file}")
                else:
                    logger.info("Automatic playback not supported on this platform")

            return output_file

        except ImportError:
            logger.warning("GPU Audio Processor not available, falling back to CPU processing")

    # Fall back to CPU processing
    logger.info("Using CPU Audio Processor")

    try:
        # Load audio file
        logger.info("Loading audio file...")
        audio_data, sample_rate = sf.read(input_file)

        # Log audio properties
        duration = len(audio_data) / sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]
        logger.info(f"Audio duration: {duration:.2f} seconds")
        logger.info(f"Audio channels: {channels}")
        logger.info(f"Audio sample rate: {sample_rate}Hz")

        # Process audio
        logger.info(f"Processing audio with upscale factor: {upscale_factor}x")
        start_time = time.time()

        # Set quality parameters based on quality level
        if quality_level == 1:  # Low
            filter_size = 32
            high_boost = 0.05
            low_boost = 0.03
        elif quality_level == 2:  # Medium
            filter_size = 64
            high_boost = 0.05
            low_boost = 0.03
        else:  # High
            filter_size = 128
            high_boost = 0.05
            low_boost = 0.03

        # Apply Windows 11 optimizations if requested
        if use_win11_opt:
            try:
                # Try to use the enhanced hardware accelerator first
                try:
                    from win11_hardware_accel import Win11HardwareAccelerator
                    logger.info("Applying Windows 11 hardware acceleration...")
                    accelerator = Win11HardwareAccelerator()
                    optimizations = accelerator.optimize_for_audio()
                    logger.info("Hardware acceleration results:")
                    for key, value in optimizations.items():
                        logger.info(f"  {key}: {value}")
                except ImportError:
                    # Fall back to basic Windows 11 optimizations
                    from windows11_optimizations import Windows11Optimizer
                    logger.info("Applying basic Windows 11 optimizations...")
                    optimizer = Windows11Optimizer()
                    optimizations = optimizer.optimize_audio_processing()
                    logger.info("Optimization results:")
                    for key, value in optimizations.items():
                        logger.info(f"  {key}: {value}")
            except ImportError:
                logger.warning("Windows 11 optimizations not available")

        # Process audio with resampling
        from scipy import signal

        if audio_data.ndim == 1:
            # Mono
            processed_audio = signal.resample_poly(
                audio_data,
                upscale_factor,
                1,
                window=('kaiser', filter_size)
            )
        else:
            # Stereo or multi-channel
            processed_audio = np.zeros((int(audio_data.shape[0] * upscale_factor), audio_data.shape[1]))
            for i in range(audio_data.shape[1]):
                processed_audio[:, i] = signal.resample_poly(
                    audio_data[:, i],
                    upscale_factor,
                    1,
                    window=('kaiser', filter_size)
                )

        # Apply enhanced dynamic range processing
        try:
            # Try to use the GPU audio processor for better dynamic range
            from gpu_audio_processor import GPUAudioProcessor

            logger.info("Using GPU Audio Processor for enhanced dynamic range")
            processor = GPUAudioProcessor(
                device="auto",
                use_win11_opt=use_win11_opt,
                quality_level=quality_level,
                reduce_load=reduce_load
            )

            # Process audio with enhanced dynamic range
            processed_audio = processor._process_audio(audio_data, sample_rate, upscale_factor)

            logger.info("Enhanced dynamic range processing completed")
            return processed_audio

        except (ImportError, Exception) as e:
            logger.warning(f"GPU Audio Processor not available: {e}")
            logger.info("Falling back to basic processing")

            # Apply more nuanced frequency enhancement with dynamic range preservation
            if audio_data.ndim == 1:
                # Mono
                # Analyze original dynamics
                orig_peak = np.max(np.abs(audio_data))
                orig_rms = np.sqrt(np.mean(audio_data ** 2))
                orig_crest = orig_peak / (orig_rms + 1e-10)
                logger.info(f"Original dynamics - Peak: {20*np.log10(orig_peak):.2f}dB, RMS: {20*np.log10(orig_rms):.2f}dB, Crest: {20*np.log10(orig_crest):.2f}dB")

                # Process in frequency domain
                spectrum = np.fft.rfft(processed_audio)
                freq_bins = len(spectrum)

                # Boost high frequencies (more subtle)
                high_boost_start = int(freq_bins * 0.75)  # Start at 75% of frequency range (higher than before)
                high_boost_factor = 1.0 + high_boost * 0.6  # Reduce boost by 40%

                # Create a gradual transition for high boost
                high_boost_range = freq_bins - high_boost_start
                high_boost_curve = np.linspace(1.0, high_boost_factor, high_boost_range)
                spectrum[high_boost_start:] *= high_boost_curve

                # Add warmth to low frequencies
                low_boost_end = int(freq_bins * 0.15)  # Boost frequencies below 15% (reduced from 20%)
                low_boost_factor = 1.0 + low_boost * 0.7  # Reduce boost by 30%

                # Create a gradual transition for low boost
                low_boost_curve = np.linspace(low_boost_factor, 1.0, low_boost_end)
                spectrum[1:low_boost_end+1] *= low_boost_curve  # Skip DC component (index 0)

                # Apply back to time domain
                processed_audio = np.fft.irfft(spectrum, len(processed_audio))

                # Enhance transients for better dynamics
                # Compute derivative to detect transients
                derivative = np.diff(processed_audio, prepend=processed_audio[0])
                transient_mask = np.abs(derivative) > 0.08 * np.max(np.abs(derivative))

                # Apply transient boost
                transient_boost = 1.08  # 8% boost for transients
                processed_audio[transient_mask] *= transient_boost

                # Apply dynamic range expansion
                # Compute envelope
                envelope = np.zeros_like(processed_audio)
                attack = 0.001  # 1ms attack
                release = 0.1  # 100ms release
                attack_coef = np.exp(-1.0 / (attack * sample_rate * upscale_factor))
                release_coef = np.exp(-1.0 / (release * sample_rate * upscale_factor))

                # Simple envelope follower
                for i in range(1, len(processed_audio)):
                    env = np.abs(processed_audio[i])
                    if env > envelope[i-1]:
                        envelope[i] = attack_coef * envelope[i-1] + (1 - attack_coef) * env
                    else:
                        envelope[i] = release_coef * envelope[i-1] + (1 - release_coef) * env

                # Apply dynamic range expansion
                threshold = 0.2
                ratio = 0.8  # 0.8:1 expansion ratio
                threshold_amp = threshold * np.max(envelope)

                # Compute gain for expansion
                gain = np.ones_like(processed_audio)
                mask = envelope < threshold_amp
                if np.any(mask):
                    gain[mask] = (envelope[mask] / threshold_amp) ** (ratio - 1.0)
                    processed_audio[mask] *= gain[mask]
            else:
                # Stereo or multi-channel
                for i in range(processed_audio.shape[1]):
                    # Analyze original dynamics for this channel
                    orig_peak = np.max(np.abs(audio_data[:, i]))
                    orig_rms = np.sqrt(np.mean(audio_data[:, i] ** 2))
                    orig_crest = orig_peak / (orig_rms + 1e-10)
                    if i == 0:  # Only log for first channel
                        logger.info(f"Original dynamics (ch1) - Peak: {20*np.log10(orig_peak):.2f}dB, RMS: {20*np.log10(orig_rms):.2f}dB, Crest: {20*np.log10(orig_crest):.2f}dB")

                    # Process in frequency domain
                    spectrum = np.fft.rfft(processed_audio[:, i])
                    freq_bins = len(spectrum)

                    # Boost high frequencies (more subtle)
                    high_boost_start = int(freq_bins * 0.75)  # Start at 75% of frequency range (higher than before)
                    high_boost_factor = 1.0 + high_boost * 0.6  # Reduce boost by 40%

                    # Create a gradual transition for high boost
                    high_boost_range = freq_bins - high_boost_start
                    high_boost_curve = np.linspace(1.0, high_boost_factor, high_boost_range)
                    spectrum[high_boost_start:] *= high_boost_curve

                    # Add warmth to low frequencies
                    low_boost_end = int(freq_bins * 0.15)  # Boost frequencies below 15% (reduced from 20%)
                    low_boost_factor = 1.0 + low_boost * 0.7  # Reduce boost by 30%

                    # Create a gradual transition for low boost
                    low_boost_curve = np.linspace(low_boost_factor, 1.0, low_boost_end)
                    spectrum[1:low_boost_end+1] *= low_boost_curve  # Skip DC component (index 0)

                    # Apply back to time domain
                    processed_audio[:, i] = np.fft.irfft(spectrum, len(processed_audio[:, i]))

                    # Enhance transients for better dynamics
                    # Compute derivative to detect transients
                    derivative = np.diff(processed_audio[:, i], prepend=processed_audio[0, i])
                    transient_mask = np.abs(derivative) > 0.08 * np.max(np.abs(derivative))

                    # Apply transient boost
                    transient_boost = 1.08  # 8% boost for transients
                    processed_audio[transient_mask, i] *= transient_boost

                    # Apply dynamic range expansion
                    # Compute envelope
                    envelope = np.zeros_like(processed_audio[:, i])
                    attack = 0.001  # 1ms attack
                    release = 0.1  # 100ms release
                    attack_coef = np.exp(-1.0 / (attack * sample_rate * upscale_factor))
                    release_coef = np.exp(-1.0 / (release * sample_rate * upscale_factor))

                    # Simple envelope follower
                    for j in range(1, len(processed_audio[:, i])):
                        env = np.abs(processed_audio[j, i])
                        if env > envelope[j-1]:
                            envelope[j] = attack_coef * envelope[j-1] + (1 - attack_coef) * env
                        else:
                            envelope[j] = release_coef * envelope[j-1] + (1 - release_coef) * env

                    # Apply dynamic range expansion
                    threshold = 0.2
                    ratio = 0.8  # 0.8:1 expansion ratio
                    threshold_amp = threshold * np.max(envelope)

                    # Compute gain for expansion
                    gain = np.ones_like(processed_audio[:, i])
                    mask = envelope < threshold_amp
                    if np.any(mask):
                        gain[mask] = (envelope[mask] / threshold_amp) ** (ratio - 1.0)
                        processed_audio[mask, i] *= gain[mask]

        # Apply AI model enhancement if requested and available
        if use_ai_model and ai_model_available:
            try:
                from audio_ai_model import AudioAIProcessor

                logger.info(f"Applying AI model enhancement (model type: {ai_model_type})")
                ai_start_time = time.time()

                # Create AI processor
                processor = AudioAIProcessor(
                    model_type=ai_model_type,
                    device="cuda" if gpu_available and use_gpu else "cpu"
                )

                # Process audio with AI model
                processed_audio = processor.process_audio(processed_audio, sample_rate * upscale_factor)

                ai_process_time = time.time() - ai_start_time
                logger.info(f"AI model enhancement completed in {ai_process_time:.2f} seconds")

                # Analyze dynamics after AI processing
                ai_peak = np.max(np.abs(processed_audio))
                ai_rms = np.sqrt(np.mean(processed_audio ** 2))
                ai_crest = ai_peak / (ai_rms + 1e-10)
                logger.info(f"AI-enhanced dynamics - Peak: {20*np.log10(ai_peak):.2f}dB, RMS: {20*np.log10(ai_rms):.2f}dB, Crest: {20*np.log10(ai_crest):.2f}dB")
            except Exception as e:
                logger.warning(f"Failed to apply AI model enhancement: {e}")
                logger.info("Continuing with traditional processing only")

        # Apply a gentle limiter to prevent clipping
        max_val = np.max(np.abs(processed_audio))
        if max_val > 0.95:  # If close to clipping
            logger.info(f"Applying limiter (peak value: {max_val:.3f})")
            # Apply soft knee limiting
            scale_factor = 0.95 / max_val
            processed_audio *= scale_factor
            logger.info(f"New peak value: {np.max(np.abs(processed_audio)):.3f}")

        process_time = time.time() - start_time
        logger.info(f"Processing completed in {process_time:.2f} seconds")

        # Save output
        logger.info(f"Saving output to: {output_file}")
        sf.write(output_file, processed_audio, sample_rate * upscale_factor)

        # Create a natural version at original sample rate
        natural_output = os.path.join(output_dir, "RAVE_sony_natural.wav")
        logger.info(f"Creating natural version at original sample rate: {natural_output}")

        # Downsample the processed audio to original sample rate
        if processed_audio.ndim == 1:
            # Mono
            natural_audio = signal.resample_poly(
                processed_audio,
                1,
                upscale_factor,
                window=('kaiser', filter_size)
            )
        else:
            # Stereo or multi-channel
            natural_audio = np.zeros((int(processed_audio.shape[0] / upscale_factor), processed_audio.shape[1]))
            for i in range(processed_audio.shape[1]):
                natural_audio[:, i] = signal.resample_poly(
                    processed_audio[:, i],
                    1,
                    upscale_factor,
                    window=('kaiser', filter_size)
                )

        # Save natural version
        sf.write(natural_output, natural_audio, sample_rate)

        # Play result if requested
        if play_result:
            logger.info("Playing processed file...")
            if platform.system() == "Windows":
                os.startfile(natural_output)
                logger.info("Natural version file opened in default media player")

                logger.info(f"\nTwo versions have been created:")
                logger.info(f"1. Natural version (44.1kHz): {natural_output}")
                logger.info(f"2. High-resolution version ({sample_rate * upscale_factor}Hz): {output_file}")
                logger.info(f"\nThe natural version is playing now. To play the high-resolution version, open:")
                logger.info(f"{output_file}")
            else:
                logger.info("Automatic playback not supported on this platform")

        return output_file

    except Exception as e:
        logger.error(f"Error processing audio: {e}", exc_info=True)
        return None

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Process the RAVE.flac file with GPU acceleration, AI models, and Sony-inspired techniques")
    parser.add_argument("--input", help="Path to input audio file (optional, will search for RAVE.flac if not provided)")
    parser.add_argument("--output-dir", help="Output directory (optional)")
    parser.add_argument("--quality", type=int, choices=[1, 2, 3], default=2, help="Quality level (1=Low, 2=Medium, 3=High)")
    parser.add_argument("--upscale-factor", type=int, choices=[2, 4, 8], default=2, help="Upscale factor")
    parser.add_argument("--no-gpu", action="store_true", help="Disable GPU acceleration")
    parser.add_argument("--no-win11-opt", action="store_true", help="Disable Windows 11 optimizations")
    parser.add_argument("--no-reduce-load", action="store_true", help="Disable load reduction (faster but lower quality)")
    parser.add_argument("--no-ai", action="store_true", help="Disable AI model enhancement")
    parser.add_argument("--ai-model", choices=["time", "spectral", "dynamic"], default="dynamic", help="AI model type to use")
    parser.add_argument("--no-play", action="store_true", help="Don't play the result after processing")

    args = parser.parse_args()

    # Process file
    output_file = process_rave_file(
        input_file=args.input,
        output_dir=args.output_dir,
        quality_level=args.quality,
        upscale_factor=args.upscale_factor,
        use_gpu=not args.no_gpu,
        use_win11_opt=not args.no_win11_opt,
        reduce_load=not args.no_reduce_load,
        use_ai_model=not args.no_ai,
        ai_model_type=args.ai_model,
        play_result=not args.no_play
    )

    if output_file:
        logger.info(f"Successfully processed file: {output_file}")
        return 0
    else:
        logger.error("Processing failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
