"""
File Utilities Module
Handles file operations for the audio upscaler
"""

import os
import platform
import subprocess
from typing import List, Dict, Any, Optional, Tuple

def list_audio_files(directory: str) -> List[str]:
    """
    List all audio files in a directory
    
    Args:
        directory: Directory to search for audio files
        
    Returns:
        List of audio file paths
    """
    audio_extensions = ['.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac']
    audio_files = []
    
    if not os.path.isdir(directory):
        return []
    
    for file in os.listdir(directory):
        if any(file.lower().endswith(ext) for ext in audio_extensions):
            audio_files.append(os.path.join(directory, file))
    
    return audio_files

def get_audio_info(file_path: str) -> Optional[Dict[str, Any]]:
    """
    Get information about an audio file
    
    Args:
        file_path: Path to audio file
        
    Returns:
        Dictionary with audio information or None if file not found
    """
    if not os.path.isfile(file_path):
        return None
    
    try:
        import soundfile as sf
        info = sf.info(file_path)
        
        return {
            'path': file_path,
            'name': os.path.basename(file_path),
            'format': info.format,
            'sample_rate': info.samplerate,
            'channels': info.channels,
            'duration': info.duration,
            'size': os.path.getsize(file_path)
        }
    except Exception as e:
        print(f"Error getting audio info: {e}")
        
        # Return basic info if soundfile fails
        return {
            'path': file_path,
            'name': os.path.basename(file_path),
            'size': os.path.getsize(file_path)
        }

def play_audio_file(file_path: str) -> bool:
    """
    Play an audio file using the system default player
    
    Args:
        file_path: Path to audio file
        
    Returns:
        True if successful, False otherwise
    """
    if not os.path.isfile(file_path):
        print(f"Error: File not found: {file_path}")
        return False
    
    try:
        # Use system default player
        if platform.system() == "Windows":
            os.startfile(file_path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", file_path])
        else:  # Linux
            subprocess.run(["xdg-open", file_path])
        
        print(f"Playing: {file_path}")
        return True
    except Exception as e:
        print(f"Error playing audio: {e}")
        return False

def open_directory(directory: str) -> bool:
    """
    Open a directory in the file explorer
    
    Args:
        directory: Directory to open
        
    Returns:
        True if successful, False otherwise
    """
    if not os.path.isdir(directory):
        print(f"Error: Directory not found: {directory}")
        return False
    
    try:
        # Open directory in file explorer
        if platform.system() == "Windows":
            os.startfile(directory)
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", directory])
        else:  # Linux
            subprocess.run(["xdg-open", directory])
        
        print(f"Opened directory: {directory}")
        return True
    except Exception as e:
        print(f"Error opening directory: {e}")
        return False

def create_output_path(input_file: str, output_dir: Optional[str] = None, suffix: str = "upscaled") -> str:
    """
    Create an output path for a processed file
    
    Args:
        input_file: Path to input file
        output_dir: Directory for output file (optional)
        suffix: Suffix to add to filename
        
    Returns:
        Path to output file
    """
    input_dir = os.path.dirname(input_file)
    input_basename = os.path.basename(input_file)
    input_name, ext = os.path.splitext(input_basename)
    
    if output_dir is None:
        output_dir = os.path.join(input_dir, "upscaled")
    
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, f"{input_name}_{suffix}{ext}")
    
    # If file already exists, add a number
    counter = 1
    while os.path.exists(output_file):
        output_file = os.path.join(output_dir, f"{input_name}_{suffix}_{counter}{ext}")
        counter += 1
    
    return output_file
