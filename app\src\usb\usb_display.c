#include "usb_display.h"

#include <assert.h>
#include <string.h>
#include <libusb.h>
#include "util/log.h"

// HDMI-CEC protocol constants
#define CEC_OPCODE_GIVE_PHYSICAL_ADDR 0x83
#define CEC_OPCODE_REPORT_PHYSICAL_ADDR 0x84
#define CEC_OPCODE_GIVE_DEVICE_POWER_STATUS 0x8F
#define CEC_OPCODE_REPORT_POWER_STATUS 0x90

// Samsung Smart View protocol constants
#define SMART_VIEW_MAGIC 0x4D535600  // "MSV\0"
#define SMART_VIEW_CMD_HELLO 0x01
#define SMART_VIEW_CMD_READY 0x02
#define SMART_VIEW_CMD_FRAME 0x03

// Samsung DeX protocol constants
#define DEX_MAGIC 0x44455800  // "DEX\0"
#define DEX_CMD_INIT 0x01
#define DEX_CMD_READY 0x02
#define DEX_CMD_FRAME 0x03

// USB transfer timeout in milliseconds
#define USB_TRANSFER_TIMEOUT 5000

// Initialize USB display connection
bool
sc_usb_display_init(struct sc_usb_display *ud) {
    assert(ud);
    
    // Initialize libusb
    int ret = libusb_init(&ud->usb_ctx);
    if (ret < 0) {
        LOGE("Failed to initialize libusb: %s", libusb_error_name(ret));
        return false;
    }
    
    // Set debug level
#ifdef SC_DEBUG
    libusb_set_option(ud->usb_ctx, LIBUSB_OPTION_LOG_LEVEL, LIBUSB_LOG_LEVEL_INFO);
#endif
    
    ud->device_handle = NULL;
    ud->state = SC_USB_DISPLAY_STATE_DISCONNECTED;
    ud->ep_in = 0;
    ud->ep_out = 0;
    ud->device_name = NULL;
    ud->width = 0;
    ud->height = 0;
    ud->recv_buffer = NULL;
    ud->recv_buffer_size = 0;
    
    return true;
}

// Find Samsung device
static bool
find_samsung_device(struct sc_usb_display *ud) {
    libusb_device **device_list;
    ssize_t device_count = libusb_get_device_list(ud->usb_ctx, &device_list);
    if (device_count < 0) {
        LOGE("Failed to get USB device list: %s", 
             libusb_error_name((int) device_count));
        return false;
    }
    
    bool found = false;
    
    for (ssize_t i = 0; i < device_count; i++) {
        libusb_device *device = device_list[i];
        struct libusb_device_descriptor desc;
        
        int ret = libusb_get_device_descriptor(device, &desc);
        if (ret < 0) {
            LOGW("Failed to get device descriptor: %s", 
                 libusb_error_name(ret));
            continue;
        }
        
        // Check if this is a Samsung device
        // We're looking for Samsung A22 or any Samsung device that might work
        if (desc.idVendor == SAMSUNG_VID) {
            LOGI("Found Samsung device (VID: %04x, PID: %04x)", 
                 desc.idVendor, desc.idProduct);
            
            // Try to open the device
            ret = libusb_open(device, &ud->device_handle);
            if (ret < 0) {
                LOGW("Failed to open Samsung device: %s", 
                     libusb_error_name(ret));
                continue;
            }
            
            // Find the interface for display/control
            struct libusb_config_descriptor *config;
            ret = libusb_get_active_config_descriptor(device, &config);
            if (ret < 0) {
                LOGW("Failed to get config descriptor: %s", 
                     libusb_error_name(ret));
                libusb_close(ud->device_handle);
                ud->device_handle = NULL;
                continue;
            }
            
            // Look for a suitable interface
            for (int j = 0; j < config->bNumInterfaces; j++) {
                const struct libusb_interface *interface = &config->interface[j];
                
                for (int k = 0; k < interface->num_altsetting; k++) {
                    const struct libusb_interface_descriptor *iface_desc = 
                        &interface->altsetting[k];
                    
                    // Look for a suitable interface class
                    // Class 255 is vendor-specific, which is likely for Samsung's protocol
                    if (iface_desc->bInterfaceClass == LIBUSB_CLASS_VENDOR_SPEC ||
                        iface_desc->bInterfaceClass == LIBUSB_CLASS_HID) {
                        
                        // Find endpoints
                        for (int l = 0; l < iface_desc->bNumEndpoints; l++) {
                            const struct libusb_endpoint_descriptor *ep_desc = 
                                &iface_desc->endpoint[l];
                            
                            if ((ep_desc->bmAttributes & LIBUSB_TRANSFER_TYPE_MASK) == 
                                LIBUSB_TRANSFER_TYPE_BULK) {
                                
                                if ((ep_desc->bEndpointAddress & LIBUSB_ENDPOINT_DIR_MASK) == 
                                    LIBUSB_ENDPOINT_IN) {
                                    ud->ep_in = ep_desc->bEndpointAddress;
                                } else {
                                    ud->ep_out = ep_desc->bEndpointAddress;
                                }
                            }
                        }
                        
                        // If we found both endpoints, claim the interface
                        if (ud->ep_in && ud->ep_out) {
                            // Detach kernel driver if necessary
                            if (libusb_kernel_driver_active(ud->device_handle, j) == 1) {
                                ret = libusb_detach_kernel_driver(ud->device_handle, j);
                                if (ret < 0) {
                                    LOGW("Failed to detach kernel driver: %s", 
                                         libusb_error_name(ret));
                                    continue;
                                }
                            }
                            
                            ret = libusb_claim_interface(ud->device_handle, j);
                            if (ret < 0) {
                                LOGW("Failed to claim interface: %s", 
                                     libusb_error_name(ret));
                                continue;
                            }
                            
                            found = true;
                            break;
                        }
                    }
                }
                
                if (found) break;
            }
            
            libusb_free_config_descriptor(config);
            
            if (found) break;
            
            // If we didn't find suitable endpoints, close the device and continue
            libusb_close(ud->device_handle);
            ud->device_handle = NULL;
        }
    }
    
    libusb_free_device_list(device_list, 1);
    return found;
}

// Connect to Samsung device
bool
sc_usb_display_connect(struct sc_usb_display *ud, struct sc_intr *intr) {
    assert(ud);
    
    if (ud->state == SC_USB_DISPLAY_STATE_CONNECTED) {
        // Already connected
        return true;
    }
    
    ud->state = SC_USB_DISPLAY_STATE_CONNECTING;
    
    // Find and open Samsung device
    if (!find_samsung_device(ud)) {
        LOGE("No compatible Samsung device found");
        ud->state = SC_USB_DISPLAY_STATE_ERROR;
        return false;
    }
    
    // Allocate receive buffer
    ud->recv_buffer_size = 16384; // 16KB should be enough for initial communication
    ud->recv_buffer = malloc(ud->recv_buffer_size);
    if (!ud->recv_buffer) {
        LOGE("Failed to allocate receive buffer");
        sc_usb_display_disconnect(ud);
        ud->state = SC_USB_DISPLAY_STATE_ERROR;
        return false;
    }
    
    // Send identification data to trick the device
    if (!sc_usb_display_send_identification(ud)) {
        LOGE("Failed to send identification data");
        sc_usb_display_disconnect(ud);
        ud->state = SC_USB_DISPLAY_STATE_ERROR;
        return false;
    }
    
    // Set default display parameters
    ud->width = 1920;
    ud->height = 1080;
    
    ud->state = SC_USB_DISPLAY_STATE_CONNECTED;
    LOGI("Successfully connected to Samsung device");
    
    return true;
}

// Disconnect from device
void
sc_usb_display_disconnect(struct sc_usb_display *ud) {
    assert(ud);
    
    if (ud->device_handle) {
        // Release interface (assuming interface 0)
        libusb_release_interface(ud->device_handle, 0);
        libusb_close(ud->device_handle);
        ud->device_handle = NULL;
    }
    
    free(ud->recv_buffer);
    ud->recv_buffer = NULL;
    ud->recv_buffer_size = 0;
    
    ud->state = SC_USB_DISPLAY_STATE_DISCONNECTED;
}

// Clean up resources
void
sc_usb_display_destroy(struct sc_usb_display *ud) {
    assert(ud);
    
    sc_usb_display_disconnect(ud);
    
    if (ud->usb_ctx) {
        libusb_exit(ud->usb_ctx);
        ud->usb_ctx = NULL;
    }
    
    free(ud->device_name);
    ud->device_name = NULL;
}

// Send display identification data to trick device
bool
sc_usb_display_send_identification(struct sc_usb_display *ud) {
    assert(ud);
    
    if (!ud->device_handle) {
        LOGE("No device handle available");
        return false;
    }
    
    // First, try HDMI-CEC protocol
    unsigned char cec_data[8] = {
        CEC_OPCODE_REPORT_PHYSICAL_ADDR,
        0x10, 0x00,  // Physical address (*******)
        0x04,        // Device type (Display)
        0xFF, 0xFF, 0xFF, 0xFF  // Padding
    };
    
    int transferred;
    int ret = libusb_bulk_transfer(ud->device_handle, ud->ep_out,
                                  cec_data, sizeof(cec_data),
                                  &transferred, USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGW("Failed to send CEC identification: %s", libusb_error_name(ret));
        // Continue anyway, as this might not be supported
    }
    
    // Next, try Samsung Smart View protocol
    unsigned char smart_view_data[32] = {0};
    
    // Header
    *(uint32_t *)&smart_view_data[0] = SMART_VIEW_MAGIC;
    smart_view_data[4] = SMART_VIEW_CMD_HELLO;
    *(uint16_t *)&smart_view_data[5] = SAMSUNG_SMART_VIEW_PROTOCOL_VERSION;
    
    // Device info
    strcpy((char *)&smart_view_data[8], "SCRCPY-PC");  // Device name
    *(uint16_t *)&smart_view_data[18] = 1920;  // Width
    *(uint16_t *)&smart_view_data[20] = 1080;  // Height
    
    ret = libusb_bulk_transfer(ud->device_handle, ud->ep_out,
                              smart_view_data, sizeof(smart_view_data),
                              &transferred, USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGW("Failed to send Smart View identification: %s", libusb_error_name(ret));
        // Continue anyway, as this might not be supported
    }
    
    // Finally, try Samsung DeX protocol
    unsigned char dex_data[32] = {0};
    
    // Header
    *(uint32_t *)&dex_data[0] = DEX_MAGIC;
    dex_data[4] = DEX_CMD_INIT;
    *(uint16_t *)&dex_data[5] = SAMSUNG_DEX_PROTOCOL_VERSION;
    
    // Device info
    strcpy((char *)&dex_data[8], "SCRCPY-DEX");  // Device name
    *(uint16_t *)&dex_data[18] = 1920;  // Width
    *(uint16_t *)&dex_data[20] = 1080;  // Height
    
    ret = libusb_bulk_transfer(ud->device_handle, ud->ep_out,
                              dex_data, sizeof(dex_data),
                              &transferred, USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGW("Failed to send DeX identification: %s", libusb_error_name(ret));
        // Continue anyway, as this might not be supported
    }
    
    // Wait for a response
    ret = libusb_bulk_transfer(ud->device_handle, ud->ep_in,
                              ud->recv_buffer, ud->recv_buffer_size,
                              &transferred, USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGW("No response received: %s", libusb_error_name(ret));
        // This is not necessarily an error, as the device might not respond immediately
    } else {
        LOGI("Received %d bytes from device", transferred);
        // Parse response to determine which protocol worked
        // This is simplified and would need to be expanded based on actual protocol details
        if (transferred >= 4) {
            uint32_t magic = *(uint32_t *)ud->recv_buffer;
            if (magic == SMART_VIEW_MAGIC) {
                LOGI("Device responded to Smart View protocol");
                return true;
            } else if (magic == DEX_MAGIC) {
                LOGI("Device responded to DeX protocol");
                return true;
            }
        }
    }
    
    // If we got here, we didn't get a clear success response, but we'll continue anyway
    // as the device might still be processing our request
    return true;
}

// Send HID input event
bool
sc_usb_display_send_input(struct sc_usb_display *ud, const unsigned char *buffer, size_t len) {
    assert(ud);
    
    if (!ud->device_handle || ud->state != SC_USB_DISPLAY_STATE_CONNECTED) {
        LOGE("Not connected to device");
        return false;
    }
    
    int transferred;
    int ret = libusb_bulk_transfer(ud->device_handle, ud->ep_out,
                                  (unsigned char *)buffer, len,
                                  &transferred, USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        LOGE("Failed to send input: %s", libusb_error_name(ret));
        return false;
    }
    
    return (size_t)transferred == len;
}

// Receive screen data
ssize_t
sc_usb_display_receive(struct sc_usb_display *ud, unsigned char *buffer, size_t len) {
    assert(ud);
    
    if (!ud->device_handle || ud->state != SC_USB_DISPLAY_STATE_CONNECTED) {
        LOGE("Not connected to device");
        return -1;
    }
    
    int transferred;
    int ret = libusb_bulk_transfer(ud->device_handle, ud->ep_in,
                                  buffer, len,
                                  &transferred, USB_TRANSFER_TIMEOUT);
    
    if (ret < 0) {
        if (ret == LIBUSB_ERROR_TIMEOUT) {
            // Timeout is not necessarily an error
            return 0;
        }
        LOGE("Failed to receive data: %s", libusb_error_name(ret));
        return -1;
    }
    
    return transferred;
}

// Check if device is connected
bool
sc_usb_display_is_connected(struct sc_usb_display *ud) {
    assert(ud);
    return ud->state == SC_USB_DISPLAY_STATE_CONNECTED;
}
