@echo off
echo Processing and playing "Dxrk ダーク - RAVE.flac"

REM Set Python path
set "PYTHON_PATH=C:\Python312"

REM Add to PATH
set "PATH=%PYTHON_PATH%;%PATH%"

REM Install required packages if not already installed
echo Installing required packages...
"%PYTHON_PATH%\python.exe" -m pip install soundfile numpy pygame scipy

REM Run the script
echo Running script...
"%PYTHON_PATH%\python.exe" process_and_play.py "C:\Users\<USER>\Downloads\Dxrk ダーク - RAVE.flac" --play-processed

echo.
echo Done!
pause
