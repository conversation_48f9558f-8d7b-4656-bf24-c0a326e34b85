@echo off
echo Audio Upscaling System - Test File
echo =================================
echo.

set OUTPUT_DIR="upscaled_output"

REM Generate a test file using our test script
echo Generating test file...
python audio_test.py

set TEST_FILE="test_signal.wav"

echo Processing test file: %TEST_FILE%
echo Output directory: %OUTPUT_DIR%
echo.

REM Create output directory if it doesn't exist
if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

REM Process with high quality settings
echo Processing with high quality settings...
python audio_upscaling_system.py %TEST_FILE% -o "%OUTPUT_DIR%\high_quality.wav" -q 3 -s 96000 -d 24 -l -14

REM Process with medium quality settings
echo Processing with medium quality settings...
python audio_upscaling_system.py %TEST_FILE% -o "%OUTPUT_DIR%\medium_quality.wav" -q 2 -s 48000 -d 24 -l -14

REM Process with low quality settings
echo Processing with low quality settings...
python audio_upscaling_system.py %TEST_FILE% -o "%OUTPUT_DIR%\low_quality.wav" -q 1 -s 48000 -d 16 -l -14

REM Analyze original file
echo Analyzing original file...
python audio_upscaling_system.py %TEST_FILE% -a

REM Analyze high quality output
echo Analyzing high quality output...
python audio_upscaling_system.py "%OUTPUT_DIR%\high_quality.wav" -a --compare %TEST_FILE%

echo.
echo Processing complete.
echo All files have been saved to the %OUTPUT_DIR% directory.
pause
