"""
Audio Postprocessor Module
Handles post-processing steps for audio upscaling
"""

import os
import time
import logging
import numpy as np
import soundfile as sf
import torch
from typing import Dict, Any, Optional, List, Union, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AudioPostprocessor:
    """
    Audio postprocessor for enhancing audio quality after upscaling
    """
    def __init__(self,
                 device: str = "auto",
                 quality_level: int = 3,
                 reduce_load: bool = True):
        """
        Initialize the audio postprocessor
        
        Args:
            device: Device to use ("auto", "cuda", or "cpu")
            quality_level: Quality level (1=Low, 2=Medium, 3=High)
            reduce_load: Whether to reduce load on GPU/CPU
        """
        self.device = self._get_device(device)
        self.quality_level = quality_level
        self.reduce_load = reduce_load
        
        # Initialize parameters based on quality level
        self._init_parameters()
        
        logger.info(f"Audio Postprocessor initialized on {self.device}")
        logger.info(f"  Quality level: {quality_level}")
        logger.info(f"  Reduced load: {reduce_load}")
    
    def _get_device(self, device: str) -> torch.device:
        """Determine the appropriate device to use"""
        if device == "auto":
            if torch.cuda.is_available():
                return torch.device("cuda")
            return torch.device("cpu")
        return torch.device(device)
    
    def _init_parameters(self):
        """Initialize parameters based on quality level"""
        # Common parameters
        self.limiter_threshold = 0.95  # -0.5 dB
        self.limiter_release = 0.05  # 50ms
        
        # Quality-specific parameters
        if self.quality_level == 1:  # Low
            self.filter_size = 1024
            self.fft_size = 2048
            self.harmonic_enhancement = 0.05  # 5%
            self.stereo_enhancement = 0.1  # 10%
            self.dither_type = "triangular"
        elif self.quality_level == 2:  # Medium
            self.filter_size = 4096
            self.fft_size = 4096
            self.harmonic_enhancement = 0.03  # 3%
            self.stereo_enhancement = 0.15  # 15%
            self.dither_type = "triangular_hp"
        else:  # High
            self.filter_size = 8192
            self.fft_size = 8192
            self.harmonic_enhancement = 0.02  # 2% (more subtle)
            self.stereo_enhancement = 0.2  # 20%
            self.dither_type = "shaped"
    
    def postprocess_file(self,
                        input_file: str,
                        output_file: Optional[str] = None,
                        target_lufs: Optional[float] = None,
                        apply_dither: bool = True) -> str:
        """
        Postprocess an audio file
        
        Args:
            input_file: Path to input audio file
            output_file: Path to output audio file (optional)
            target_lufs: Target LUFS level (optional)
            apply_dither: Whether to apply dithering
            
        Returns:
            Path to the postprocessed audio file
        """
        # Check if input file exists
        if not os.path.isfile(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")
        
        # Generate output filename if not provided
        if output_file is None:
            input_dir = os.path.dirname(input_file)
            input_basename = os.path.basename(input_file)
            input_name, input_ext = os.path.splitext(input_basename)
            output_dir = os.path.join(input_dir, "postprocessed")
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{input_name}_postprocessed{input_ext}")
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Load audio file
        logger.info(f"Loading audio file: {input_file}")
        audio_data, sample_rate = sf.read(input_file)
        
        # Log audio properties
        duration = len(audio_data) / sample_rate
        channels = 1 if audio_data.ndim == 1 else audio_data.shape[1]
        logger.info(f"Audio duration: {duration:.2f} seconds")
        logger.info(f"Audio channels: {channels}")
        logger.info(f"Audio sample rate: {sample_rate}Hz")
        
        # Process audio
        logger.info(f"Postprocessing audio...")
        start_time = time.time()
        
        # Process audio based on channels
        if audio_data.ndim == 1:
            # Mono
            processed_audio = self._postprocess_mono(audio_data, sample_rate, target_lufs)
        else:
            # Stereo or multi-channel
            processed_audio = self._postprocess_stereo(audio_data, sample_rate, target_lufs)
        
        # Apply dithering if requested
        if apply_dither:
            processed_audio = self._apply_dither(processed_audio, sample_rate)
        
        process_time = time.time() - start_time
        logger.info(f"Postprocessing completed in {process_time:.2f} seconds")
        
        # Save output
        logger.info(f"Saving output to: {output_file}")
        sf.write(output_file, processed_audio, sample_rate)
        
        return output_file
    
    def _postprocess_mono(self,
                         audio_data: np.ndarray,
                         sample_rate: int,
                         target_lufs: Optional[float] = None) -> np.ndarray:
        """
        Postprocess mono audio data
        
        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio data
            target_lufs: Target LUFS level (optional)
            
        Returns:
            Postprocessed audio data
        """
        # Convert to tensor and move to device
        audio_tensor = torch.tensor(audio_data, dtype=torch.float32).to(self.device)
        
        # Apply harmonic enhancement
        audio_tensor = self._enhance_harmonics(audio_tensor, sample_rate)
        
        # Apply limiting
        audio_tensor = self._apply_limiter(audio_tensor, sample_rate)
        
        # Apply loudness normalization if target LUFS is provided
        if target_lufs is not None:
            audio_tensor = self._normalize_loudness(audio_tensor, sample_rate, target_lufs)
        
        # Convert back to numpy
        processed_audio = audio_tensor.cpu().numpy()
        
        return processed_audio
    
    def _postprocess_stereo(self,
                           audio_data: np.ndarray,
                           sample_rate: int,
                           target_lufs: Optional[float] = None) -> np.ndarray:
        """
        Postprocess stereo audio data
        
        Args:
            audio_data: Audio data as numpy array [samples, channels]
            sample_rate: Sample rate of the audio data
            target_lufs: Target LUFS level (optional)
            
        Returns:
            Postprocessed audio data
        """
        # Convert to tensor and move to device
        audio_tensor = torch.tensor(audio_data, dtype=torch.float32).to(self.device)
        
        # Apply harmonic enhancement to each channel
        enhanced_channels = []
        for i in range(audio_tensor.shape[1]):
            channel = audio_tensor[:, i]
            enhanced = self._enhance_harmonics(channel, sample_rate)
            enhanced_channels.append(enhanced)
        
        # Stack channels
        enhanced_tensor = torch.stack(enhanced_channels, dim=1)
        
        # Apply stereo enhancement
        enhanced_tensor = self._enhance_stereo(enhanced_tensor, sample_rate)
        
        # Apply limiting
        enhanced_tensor = self._apply_limiter_stereo(enhanced_tensor, sample_rate)
        
        # Apply loudness normalization if target LUFS is provided
        if target_lufs is not None:
            enhanced_tensor = self._normalize_loudness_stereo(enhanced_tensor, sample_rate, target_lufs)
        
        # Convert back to numpy
        processed_audio = enhanced_tensor.cpu().numpy()
        
        return processed_audio
    
    def _enhance_harmonics(self,
                          audio_tensor: torch.Tensor,
                          sample_rate: int) -> torch.Tensor:
        """
        Enhance harmonics in audio
        
        Args:
            audio_tensor: Audio tensor
            sample_rate: Sample rate of the audio
            
        Returns:
            Enhanced audio tensor
        """
        # Compute FFT
        fft_size = self.fft_size
        fft = torch.fft.rfft(audio_tensor, n=fft_size)
        
        # Get magnitude and phase
        magnitude = torch.abs(fft)
        phase = torch.angle(fft)
        
        # Apply harmonic enhancement
        freq_bins = len(magnitude)
        
        # Enhance harmonics
        harmonic_order = 4  # Number of harmonics to enhance
        for h in range(2, harmonic_order + 2):
            # Calculate harmonic frequencies
            harmonic_indices = torch.arange(1, freq_bins // h, device=self.device).long()
            source_indices = harmonic_indices.clone()
            target_indices = (harmonic_indices * h).long()
            
            # Only enhance harmonics that are within the frequency range
            valid_mask = target_indices < freq_bins
            source_indices = source_indices[valid_mask]
            target_indices = target_indices[valid_mask]
            
            # Enhance harmonics
            harmonic_boost_factor = 1.0 + (self.harmonic_enhancement / h)
            magnitude[target_indices] *= harmonic_boost_factor
        
        # Reconstruct FFT
        fft_real = magnitude * torch.cos(phase)
        fft_imag = magnitude * torch.sin(phase)
        fft = torch.complex(fft_real, fft_imag)
        
        # Inverse FFT
        enhanced = torch.fft.irfft(fft, n=fft_size)
        
        # Trim to original length
        enhanced = enhanced[:len(audio_tensor)]
        
        return enhanced
    
    def _enhance_stereo(self,
                       audio_tensor: torch.Tensor,
                       sample_rate: int) -> torch.Tensor:
        """
        Enhance stereo image
        
        Args:
            audio_tensor: Audio tensor [samples, channels]
            sample_rate: Sample rate of the audio
            
        Returns:
            Enhanced audio tensor
        """
        # Check if audio is stereo
        if audio_tensor.shape[1] != 2:
            return audio_tensor
        
        # Extract left and right channels
        left = audio_tensor[:, 0]
        right = audio_tensor[:, 1]
        
        # Convert to mid-side
        mid = (left + right) / 2
        side = (left - right) / 2
        
        # Enhance side channel
        side_enhanced = side * (1.0 + self.stereo_enhancement)
        
        # Convert back to left-right
        left_enhanced = mid + side_enhanced
        right_enhanced = mid - side_enhanced
        
        # Stack channels
        enhanced = torch.stack([left_enhanced, right_enhanced], dim=1)
        
        return enhanced
    
    def _apply_limiter(self,
                      audio_tensor: torch.Tensor,
                      sample_rate: int) -> torch.Tensor:
        """
        Apply limiter to audio
        
        Args:
            audio_tensor: Audio tensor
            sample_rate: Sample rate of the audio
            
        Returns:
            Limited audio tensor
        """
        # Calculate release time in samples
        release_samples = int(self.limiter_release * sample_rate)
        
        # Find peaks
        peak_env = torch.zeros_like(audio_tensor)
        
        # For each sample, look ahead to find the maximum
        for i in range(len(audio_tensor)):
            peak_env[i] = torch.max(torch.abs(audio_tensor[max(0, i-1):min(len(audio_tensor), i+2)]))
        
        # Apply release envelope
        release_coef = torch.exp(-torch.tensor(2.2) / release_samples).to(self.device)
        for i in range(1, len(audio_tensor)):
            peak_env[i] = max(peak_env[i], release_coef * peak_env[i-1])
        
        # Calculate gain reduction
        gain = torch.ones_like(audio_tensor)
        mask = peak_env > self.limiter_threshold
        
        if torch.any(mask):
            gain[mask] = self.limiter_threshold / peak_env[mask]
            
            # Apply smooth gain reduction
            # Use a 5-sample moving average to smooth gain changes
            kernel = torch.ones(5, device=self.device) / 5
            gain_smoothed = torch.nn.functional.conv1d(
                gain.unsqueeze(0).unsqueeze(0),
                kernel.unsqueeze(0).unsqueeze(0),
                padding=2
            ).squeeze(0).squeeze(0)
            
            # Apply smoothed gain
            limited = audio_tensor * gain_smoothed
        else:
            limited = audio_tensor
        
        return limited
    
    def _apply_limiter_stereo(self,
                             audio_tensor: torch.Tensor,
                             sample_rate: int) -> torch.Tensor:
        """
        Apply limiter to stereo audio
        
        Args:
            audio_tensor: Audio tensor [samples, channels]
            sample_rate: Sample rate of the audio
            
        Returns:
            Limited audio tensor
        """
        # Calculate release time in samples
        release_samples = int(self.limiter_release * sample_rate)
        
        # Find peaks across all channels
        peak_env = torch.zeros(len(audio_tensor), device=self.device)
        
        # For each sample, find the maximum across all channels
        for i in range(len(audio_tensor)):
            peak_env[i] = torch.max(torch.abs(audio_tensor[i]))
        
        # Apply release envelope
        release_coef = torch.exp(-torch.tensor(2.2) / release_samples).to(self.device)
        for i in range(1, len(audio_tensor)):
            peak_env[i] = max(peak_env[i], release_coef * peak_env[i-1])
        
        # Calculate gain reduction
        gain = torch.ones_like(peak_env)
        mask = peak_env > self.limiter_threshold
        
        if torch.any(mask):
            gain[mask] = self.limiter_threshold / peak_env[mask]
            
            # Apply smooth gain reduction
            # Use a 5-sample moving average to smooth gain changes
            kernel = torch.ones(5, device=self.device) / 5
            gain_smoothed = torch.nn.functional.conv1d(
                gain.unsqueeze(0).unsqueeze(0),
                kernel.unsqueeze(0).unsqueeze(0),
                padding=2
            ).squeeze(0).squeeze(0)
            
            # Apply smoothed gain to all channels
            limited = audio_tensor.clone()
            for i in range(audio_tensor.shape[1]):
                limited[:, i] = audio_tensor[:, i] * gain_smoothed
        else:
            limited = audio_tensor
        
        return limited
    
    def _normalize_loudness(self,
                           audio_tensor: torch.Tensor,
                           sample_rate: int,
                           target_lufs: float) -> torch.Tensor:
        """
        Normalize loudness to target LUFS level
        
        Args:
            audio_tensor: Audio tensor
            sample_rate: Sample rate of the audio
            target_lufs: Target LUFS level
            
        Returns:
            Normalized audio tensor
        """
        # This is a simplified LUFS calculation
        # For accurate LUFS measurement, use a dedicated library like pyloudnorm
        
        # Calculate current RMS level (approximation of loudness)
        rms = torch.sqrt(torch.mean(audio_tensor ** 2))
        current_db = 20 * torch.log10(rms + 1e-10)
        
        # Approximate LUFS from dB (very rough approximation)
        # In practice, use a proper LUFS meter
        current_lufs = current_db - 10  # Rough approximation
        
        # Calculate gain needed
        gain_db = target_lufs - current_lufs
        gain_linear = 10 ** (gain_db / 20.0)
        
        # Apply gain
        normalized = audio_tensor * gain_linear
        
        logger.info(f"Normalized loudness: {current_lufs.item():.2f} LUFS -> {target_lufs:.2f} LUFS")
        logger.info(f"Applied gain: {gain_db.item():.2f} dB")
        
        return normalized
    
    def _normalize_loudness_stereo(self,
                                  audio_tensor: torch.Tensor,
                                  sample_rate: int,
                                  target_lufs: float) -> torch.Tensor:
        """
        Normalize loudness of stereo audio to target LUFS level
        
        Args:
            audio_tensor: Audio tensor [samples, channels]
            sample_rate: Sample rate of the audio
            target_lufs: Target LUFS level
            
        Returns:
            Normalized audio tensor
        """
        # This is a simplified LUFS calculation
        # For accurate LUFS measurement, use a dedicated library like pyloudnorm
        
        # Calculate current RMS level (approximation of loudness)
        # Average across channels
        rms = torch.sqrt(torch.mean(torch.mean(audio_tensor ** 2, dim=1)))
        current_db = 20 * torch.log10(rms + 1e-10)
        
        # Approximate LUFS from dB (very rough approximation)
        # In practice, use a proper LUFS meter
        current_lufs = current_db - 10  # Rough approximation
        
        # Calculate gain needed
        gain_db = target_lufs - current_lufs
        gain_linear = 10 ** (gain_db / 20.0)
        
        # Apply gain
        normalized = audio_tensor * gain_linear
        
        logger.info(f"Normalized loudness: {current_lufs.item():.2f} LUFS -> {target_lufs:.2f} LUFS")
        logger.info(f"Applied gain: {gain_db.item():.2f} dB")
        
        return normalized
    
    def _apply_dither(self,
                     audio_data: np.ndarray,
                     sample_rate: int) -> np.ndarray:
        """
        Apply dithering to audio
        
        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio
            
        Returns:
            Dithered audio data
        """
        # Determine bit depth based on audio data type
        if audio_data.dtype == np.float32 or audio_data.dtype == np.float64:
            # Assume we're dithering for 24-bit output
            bits = 24
        elif audio_data.dtype == np.int16:
            bits = 16
        elif audio_data.dtype == np.int32:
            bits = 32
        else:
            bits = 16  # Default
        
        # Calculate quantization step
        q = 2.0 ** (-bits)
        
        # Generate dither noise
        if self.dither_type == "triangular":
            # Triangular dither (TPDF)
            if audio_data.ndim == 1:
                # Mono
                r1 = np.random.uniform(-q/2, q/2, size=len(audio_data))
                r2 = np.random.uniform(-q/2, q/2, size=len(audio_data))
                dither = r1 + r2
            else:
                # Stereo or multi-channel
                r1 = np.random.uniform(-q/2, q/2, size=audio_data.shape)
                r2 = np.random.uniform(-q/2, q/2, size=audio_data.shape)
                dither = r1 + r2
        elif self.dither_type == "triangular_hp":
            # Triangular dither with high-pass filter (TPDF)
            if audio_data.ndim == 1:
                # Mono
                r1 = np.random.uniform(-q/2, q/2, size=len(audio_data))
                r2 = np.random.uniform(-q/2, q/2, size=len(audio_data))
                dither = r1 + r2
                # Apply simple high-pass filter
                dither[1:] = dither[1:] - dither[:-1]
            else:
                # Stereo or multi-channel
                r1 = np.random.uniform(-q/2, q/2, size=audio_data.shape)
                r2 = np.random.uniform(-q/2, q/2, size=audio_data.shape)
                dither = r1 + r2
                # Apply simple high-pass filter
                dither[1:] = dither[1:] - dither[:-1]
        elif self.dither_type == "shaped":
            # Noise-shaped dither
            # This is a simplified version of noise shaping
            if audio_data.ndim == 1:
                # Mono
                r1 = np.random.uniform(-q/2, q/2, size=len(audio_data))
                r2 = np.random.uniform(-q/2, q/2, size=len(audio_data))
                dither = r1 + r2
                # Apply noise shaping
                error = np.zeros_like(dither)
                for i in range(1, len(dither)):
                    # Quantize with dither
                    x = audio_data[i-1] + dither[i-1]
                    y = np.round(x / q) * q
                    # Calculate error
                    error[i-1] = x - y
                    # Feed error into next sample
                    dither[i] += 0.5 * error[i-1]
            else:
                # Stereo or multi-channel
                r1 = np.random.uniform(-q/2, q/2, size=audio_data.shape)
                r2 = np.random.uniform(-q/2, q/2, size=audio_data.shape)
                dither = r1 + r2
                # Apply noise shaping
                error = np.zeros_like(dither)
                for i in range(1, dither.shape[0]):
                    # Quantize with dither
                    x = audio_data[i-1]
                    y = np.round((x + dither[i-1]) / q) * q
                    # Calculate error
                    error[i-1] = x - y
                    # Feed error into next sample
                    dither[i] += 0.5 * error[i-1]
        else:
            # Rectangular dither (RPDF)
            dither = np.random.uniform(-q/2, q/2, size=audio_data.shape)
        
        # Apply dither
        dithered = audio_data + dither
        
        return dithered


# Test function
def test_postprocessor():
    """Test the audio postprocessor with a simple sine wave"""
    # Create a simple test signal
    sample_rate = 44100
    duration = 2  # seconds
    t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
    
    # Create a test signal with multiple frequencies
    signal = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz
    signal += 0.3 * np.sin(2 * np.pi * 880 * t)  # 880 Hz
    signal += 0.2 * np.sin(2 * np.pi * 1760 * t)  # 1760 Hz
    
    # Create stereo signal
    stereo_signal = np.column_stack((signal, signal * 0.8))
    
    # Save test signals
    mono_file = "test_mono.wav"
    stereo_file = "test_stereo.wav"
    sf.write(mono_file, signal, sample_rate)
    sf.write(stereo_file, stereo_signal, sample_rate)
    
    # Create postprocessor
    postprocessor = AudioPostprocessor(device="auto", quality_level=3)
    
    # Process mono signal
    mono_output = postprocessor.postprocess_file(mono_file, target_lufs=-16)
    print(f"Postprocessed mono file saved to: {mono_output}")
    
    # Process stereo signal
    stereo_output = postprocessor.postprocess_file(stereo_file, target_lufs=-16)
    print(f"Postprocessed stereo file saved to: {stereo_output}")


if __name__ == "__main__":
    test_postprocessor()
