"""
AMD ROCm Accelerator for AudioSR
Provides optimized audio processing for AMD GPUs using ROCm
"""

import torch
import numpy as np

class AMDAccelerator:
    """
    Accelerates audio processing on AMD GPUs using ROCm
    """
    def __init__(self, device: str = "auto"):
        self.device = self._get_device(device)
        self.is_rocm_available = self._check_rocm()

    def _get_device(self, device: str) -> torch.device:
        """Determine the appropriate device to use"""
        if device == "auto":
            if torch.cuda.is_available():
                # Check if ROCm is being used
                if 'rocm' in torch.version.hip:
                    return torch.device("cuda")
            return torch.device("cpu")
        return torch.device(device)

    def _check_rocm(self) -> bool:
        """Check if ROCm is available"""
        if not torch.cuda.is_available():
            return False

        try:
            # Check if ROCm is being used
            return 'rocm' in torch.version.hip
        except AttributeError:
            return False

    def optimize_model(self, model):
        """
        Optimize model for AMD GPU execution
        """
        if not self.is_rocm_available:
            return model

        # Apply AMD-specific optimizations
        if hasattr(torch, 'jit'):
            try:
                # Use TorchScript to optimize the model
                model = torch.jit.script(model)

                # Enable ROCm-specific optimizations
                if hasattr(torch.jit, '_rocm_fuser_state'):
                    torch.jit._rocm_fuser_state().enabled = True

                return model
            except Exception as e:
                print(f"Warning: Could not optimize model for ROCm: {e}")
                return model
        return model

    def process_audio(self, audio_tensor: torch.Tensor) -> torch.Tensor:
        """
        Process audio using AMD-optimized operations
        """
        if not self.is_rocm_available:
            return audio_tensor

        # Move tensor to GPU if not already there
        if audio_tensor.device != self.device:
            audio_tensor = audio_tensor.to(self.device)

        # Apply ROCm-specific memory optimizations
        if hasattr(torch.cuda, 'amp') and self.device.type == 'cuda':
            with torch.cuda.amp.autocast():
                # Process with mixed precision for better performance on AMD GPUs
                return audio_tensor

        return audio_tensor

    def optimize_inference(self, model, input_tensor: torch.Tensor) -> torch.Tensor:
        """
        Run optimized inference on AMD GPU
        """
        if not self.is_rocm_available:
            return model(input_tensor)

        # Move tensors to appropriate device
        if input_tensor.device != self.device:
            input_tensor = input_tensor.to(self.device)

        # Use mixed precision for AMD GPUs
        if hasattr(torch.cuda, 'amp') and self.device.type == 'cuda':
            with torch.cuda.amp.autocast():
                output = model(input_tensor)
                return output

        return model(input_tensor)

    def get_optimal_chunk_size(self) -> int:
        """
        Get optimal audio chunk size for processing based on GPU memory
        """
        if not self.is_rocm_available:
            return 8192  # Default for CPU

        # Get GPU memory info
        try:
            free_mem, _ = torch.cuda.mem_get_info()
            # Calculate based on available memory (conservative estimate)
            # Each float32 sample is 4 bytes
            max_samples = (free_mem * 0.7) // 4  # Use 70% of free memory

            # Round to nearest power of 2 for better performance
            chunk_size = 2 ** int(np.log2(max_samples / 8))  # Divide by 8 for safety

            # Clamp to reasonable range
            return max(4096, min(chunk_size, 32768))
        except (RuntimeError, AttributeError):
            # Fallback for older PyTorch versions or when CUDA operations fail
            return 8192
