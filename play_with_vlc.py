"""
Play audio file with Windows Media Player

This script was originally designed to use VLC but has been updated to use the default Windows media player.
"""

import os
import sys
import argparse

def play_with_windows_media_player(file_path):
    """Play audio file with Windows Media Player"""
    if not os.path.isfile(file_path):
        print(f"Error: File not found: {file_path}")
        return False

    print(f"Playing {file_path} with Windows Media Player...")
    print("The file will open in the default media player.")

    try:
        # Use os.startfile to open the file with the default application
        os.startfile(os.path.abspath(file_path))
        return True
    except Exception as e:
        print(f"Error playing file: {e}")
        return False

def play_with_vlc(file_path, _enable_hardware_logging=True):
    """Redirects to play_with_windows_media_player for compatibility"""
    return play_with_windows_media_player(file_path)

def main():
    parser = argparse.ArgumentParser(description="Play audio file with Windows Media Player")
    parser.add_argument("file_path", help="Path to the audio file to play")

    args = parser.parse_args()

    success = play_with_windows_media_player(args.file_path)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
