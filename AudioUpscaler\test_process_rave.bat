@echo on
echo Testing Audio Upscaler with "Dxrk ダーク - RAVE.flac"

REM Set Python path if not already set
REM Dynamically locate Python installation path
if not defined PYTHON_PATH (
	echo Error: PYTHON_PATH is not set. Please set the PYTHON_PATH environment variable and try again.
for %%P in ("%PYTHON_PATH%") do set "PYTHON_PATH=%%~dpP"

REM Add to PATH if not already present
echo %PATH% | find /i "%PYTHON_PATH%" >nul || set "PATH=%PYTHON_PATH%;%PATH%"

REM Check if Python is accessible
"%PYTHON_PATH%\python.exe" --version >nul 2>&1 || (
	echo Error: Python is not installed or not accessible at "%PYTHON_PATH%".
	exit /b 1
)
	"%PYTHON_PATH%\python.exe" -m pip install numpy==1.23.5 scipy==1.10.1 soundfile==0.12.1
REM Add Python Scripts directory to PATH if not already present
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo %PATH% | find /i "%PYTHON_SCRIPTS%" >nul || set "PATH=%PYTHON_SCRIPTS%;%PATH%"
echo Checking required packages...
"%PYTHON_PATH%\python.exe" -c "import numpy, scipy, soundfile" 2>nul || (
	echo Required packages not found. Installing...
	"%PYTHON_PATH%\python.exe" -m pip install numpy scipy soundfile
REM Run the test script
set "SCRIPT_DIR=%~dp0"
echo Running test script...
"%PYTHON_PATH%\python.exe" "%SCRIPT_DIR%test_process_file.py"
REM Run the test script
echo Running test script...
"%PYTHON_PATH%\python.exe" test_process_file.py

echo.
REM Pause the script if the PAUSE_ON_EXIT variable is set to any value
if not "%PAUSE_ON_EXIT%"=="" pause
pause
